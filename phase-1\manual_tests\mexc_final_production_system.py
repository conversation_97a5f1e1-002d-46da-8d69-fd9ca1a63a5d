#!/usr/bin/env python3
"""
MEXC Final Production System
Complete trading system with signature algorithm implementation
"""

import json
import time
import hashlib
import hmac
import random
import string
from playwright.sync_api import sync_playwright
from curl_cffi import requests
from dotenv import dotenv_values
from typing import Dict, Optional, List

class MEXCFinalProductionSystem:
    """Final production-ready MEXC trading system"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        # Load cracked algorithm if available
        self.signature_algorithm = self._load_cracked_algorithm()
        
        # Browser components for parameter extraction
        self.playwright = None
        self.browser = None
        self.page = None
        
        print("🚀 MEXC Final Production System")
        print("="*40)
        
        if self.signature_algorithm:
            print(f"✅ Loaded signature algorithm: {self.signature_algorithm.get('hashMethod', 'Unknown')}")
        else:
            print("⚠️ No signature algorithm loaded - will use hybrid approach")
    
    def _load_cracked_algorithm(self):
        """Load cracked algorithm from file"""
        try:
            with open('cracked_algorithm.json', 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return None
        except Exception as e:
            print(f"❌ Error loading algorithm: {e}")
            return None
    
    def setup_browser_connection(self):
        """Setup browser connection for hybrid approach"""
        
        print("🌐 Setting up browser connection...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            if context.pages:
                self.page = context.pages[0]
            else:
                self.page = context.new_page()
            
            # Navigate to MEXC
            if 'mexc.com' not in self.page.url:
                self.page.goto('https://www.mexc.com/futures/BTC_USDT', wait_until='domcontentloaded')
                time.sleep(3)
            
            # Inject session tokens
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            print("✅ Browser connection established")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def generate_signature(self, order_data: Dict, nonce: str) -> str:
        """Generate signature using cracked algorithm or fallback"""
        
        if self.signature_algorithm:
            return self._generate_cracked_signature(order_data, nonce)
        else:
            return self._generate_fallback_signature(order_data, nonce)
    
    def _generate_cracked_signature(self, order_data: Dict, nonce: str) -> str:
        """Generate signature using cracked algorithm"""
        
        try:
            algo = self.signature_algorithm
            content_method = algo.get('contentMethod', 0)
            hash_method = algo.get('hashMethod', 'SHA256')
            length = algo.get('length', 32)
            
            # Generate content based on method
            if content_method == 0:  # JSON.stringify(orderData)
                content = json.dumps(order_data)
            elif content_method == 1:  # JSON.stringify(orderData, sorted keys)
                content = json.dumps(order_data, sort_keys=True)
            elif content_method == 5:  # auth + nonce + JSON
                content = self.auth + nonce + json.dumps(order_data, sort_keys=True)
            elif content_method == 10:  # auth + nonce + query
                content = self.auth + nonce + self._to_query_string(order_data)
            else:
                # Default fallback
                content = self.auth + nonce + json.dumps(order_data, sort_keys=True)
            
            # Apply hash function
            if hash_method == 'MD5':
                signature = hashlib.md5(content.encode()).hexdigest()
            elif hash_method == 'SHA256':
                signature = hashlib.sha256(content.encode()).hexdigest()
            elif hash_method == 'HMAC-SHA256-auth':
                signature = hmac.new(self.auth.encode(), content.encode(), hashlib.sha256).hexdigest()
            elif hash_method == 'HMAC-SHA256-nonce':
                signature = hmac.new(nonce.encode(), content.encode(), hashlib.sha256).hexdigest()
            else:
                signature = hashlib.sha256(content.encode()).hexdigest()
            
            return signature[:length]
            
        except Exception as e:
            print(f"❌ Cracked signature generation failed: {e}")
            return self._generate_fallback_signature(order_data, nonce)
    
    def _generate_fallback_signature(self, order_data: Dict, nonce: str) -> str:
        """Generate fallback signature"""
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        content = self.auth + nonce + json_str
        return hashlib.sha256(content.encode()).hexdigest()[:32]
    
    def _to_query_string(self, data: Dict) -> str:
        """Convert dict to query string"""
        params = []
        for key in sorted(data.keys()):
            params.append(f"{key}={data[key]}")
        return '&'.join(params)
    
    def get_market_price(self, symbol: str) -> float:
        """Get current market price"""
        
        headers = {'Accept': 'application/json', 'authorization': self.auth}
        
        try:
            r = self.session.get('https://futures.mexc.com/api/v1/contract/ticker',
                               params={'symbol': symbol}, headers=headers)
            
            if r.status_code == 200:
                data = r.json()
                if data.get('code') == 0:
                    ticker_data = data.get('data')
                    if isinstance(ticker_data, list) and ticker_data:
                        return float(ticker_data[0].get('lastPrice', 0))
                    elif isinstance(ticker_data, dict):
                        return float(ticker_data.get('lastPrice', 0))
        except Exception as e:
            print(f"❌ Market data error: {e}")
        
        return 0.0
    
    def place_order(self, symbol: str, side: int, price: float, volume: int = 1) -> Dict:
        """Place order using production system"""
        
        print(f"🚀 Placing order via production system...")
        print(f"   Symbol: {symbol}")
        print(f"   Side: {'LONG' if side == 1 else 'SHORT'}")
        print(f"   Price: ${price:,.2f}")
        print(f"   Volume: {volume}")
        
        # Generate nonce
        nonce = str(int(time.time() * 1000))
        
        # Prepare order data
        order_data = {
            'symbol': symbol,
            'side': side,
            'openType': 1,
            'type': '2',
            'vol': volume,
            'leverage': 1,
            'marketCeiling': False,
            'price': str(price),
            'priceProtect': '0'
        }
        
        # Generate opaque parameters
        p0 = hashlib.md5(f"{nonce}{json.dumps(order_data)}{self.auth}".encode()).hexdigest()
        k0 = hashlib.md5(f"{time.time()}{random.random()}".encode()).hexdigest()[:16]
        
        order_data['p0'] = p0
        order_data['k0'] = k0
        
        # Generate signature
        signature = self.generate_signature(order_data, nonce)
        
        print(f"🔐 Generated signature: {signature[:16]}...")
        print(f"🔢 Nonce: {nonce}")
        
        # Prepare headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'authorization': self.auth,
            'x-mxc-nonce': nonce,
            'x-mxc-sign': signature,
            'x-language': 'en_US',
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        # Place order
        try:
            mhash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
            url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={mhash}'
            
            r = self.session.post(url, json=order_data, headers=headers)
            
            print(f"📡 Response: {r.status_code}")
            
            if r.status_code == 200:
                result = r.json()
                
                if result.get('success') and result.get('code') == 0:
                    order_id = result.get('data', {}).get('orderId')
                    print(f"✅ Order placed successfully! ID: {order_id}")
                    return {
                        'success': True,
                        'order_id': order_id,
                        'result': result
                    }
                else:
                    error_code = result.get('code')
                    error_msg = result.get('message', '')
                    print(f"❌ Order failed: {error_code} - {error_msg}")
                    
                    if error_code == 602:
                        print("   → Signature verification failed")
                        if not self.signature_algorithm:
                            print("   → Consider using browser automation fallback")
                    
                    return {'success': False, 'error_code': error_code, 'error_msg': error_msg}
            else:
                print(f"❌ HTTP error: {r.status_code}")
                return {'success': False, 'error': f'HTTP {r.status_code}'}
                
        except Exception as e:
            print(f"❌ Order placement error: {e}")
            return {'success': False, 'error': str(e)}
    
    def cancel_order(self, order_id: str) -> Dict:
        """Cancel order"""
        
        print(f"🔄 Canceling order {order_id}...")
        
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'authorization': self.auth,
            'x-mxc-nonce': str(int(time.time() * 1000)),
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        try:
            r = self.session.post('https://futures.mexc.com/api/v1/private/order/cancel',
                                json=[order_id], headers=headers)
            
            if r.status_code == 200:
                result = r.json()
                if result.get('success') and result.get('code') == 0:
                    print("✅ Order canceled successfully")
                    return {'success': True}
                else:
                    print(f"❌ Cancel failed: {result.get('message', 'Unknown')}")
                    return {'success': False}
            else:
                print(f"❌ Cancel HTTP error: {r.status_code}")
                return {'success': False}
                
        except Exception as e:
            print(f"❌ Cancel error: {e}")
            return {'success': False}
    
    def execute_trade(self, symbol: str, side: int, price: float, volume: int = 1) -> Dict:
        """Execute complete trade with place and cancel"""
        
        print(f"\n{'='*60}")
        print(f"EXECUTING PRODUCTION TRADE: {symbol}")
        print(f"Side: {'LONG' if side == 1 else 'SHORT'}")
        print(f"Price: ${price:,.2f}")
        print(f"Volume: {volume}")
        print(f"{'='*60}")
        
        # Place order
        order_result = self.place_order(symbol, side, price, volume)
        
        if order_result.get('success'):
            order_id = order_result.get('order_id')
            print(f"🎉 ORDER PLACED SUCCESSFULLY!")
            
            if order_id:
                # Wait before cancel
                time.sleep(2)
                cancel_result = self.cancel_order(str(order_id))
                
                return {
                    'success': True,
                    'order_placed': True,
                    'order_canceled': cancel_result.get('success', False),
                    'order_id': order_id
                }
            else:
                return {
                    'success': True,
                    'order_placed': True,
                    'order_canceled': False,
                    'order_id': None
                }
        else:
            return {
                'success': False,
                'error': order_result.get('error_msg', 'Order placement failed')
            }
    
    def run_production_test(self, symbol: str = 'BTC_USDT'):
        """Run production test"""
        
        print(f"🧪 Running production test for {symbol}...")
        
        # Get market price
        market_price = self.get_market_price(symbol)
        if market_price <= 0:
            print("❌ Could not get market price")
            return False
        
        # Calculate test price (70% below market)
        test_price = round(market_price * 0.3, 2)
        
        print(f"📊 Market price: ${market_price:,.2f}")
        print(f"🎯 Test price: ${test_price:,.2f}")
        
        # Execute trade
        result = self.execute_trade(symbol, 1, test_price, 1)
        
        if result.get('success'):
            print(f"\n🎉 PRODUCTION SYSTEM SUCCESS!")
            print(f"✅ Order placed: {result.get('order_placed', False)}")
            print(f"✅ Order canceled: {result.get('order_canceled', False)}")
            print(f"📋 Order ID: {result.get('order_id', 'None')}")
            print(f"\n🚀 SYSTEM READY FOR LIVE TRADING!")
            return True
        else:
            print(f"\n❌ Production test failed: {result.get('error', 'Unknown')}")
            
            if not self.signature_algorithm:
                print(f"💡 Consider capturing signature algorithm first")
                print(f"   Run: python order_signature_hunter.py")
            
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        if self.browser:
            self.browser.close()
        if self.playwright:
            self.playwright.stop()

def main():
    """Main production system test"""
    
    system = MEXCFinalProductionSystem()
    
    try:
        # Setup browser connection (for hybrid approach if needed)
        browser_ok = system.setup_browser_connection()
        
        if not browser_ok:
            print("⚠️ Browser connection failed - continuing with API-only mode")
        
        # Run production test
        success = system.run_production_test('BTC_USDT')
        
        if success:
            print(f"\n🎉 MEXC PRODUCTION SYSTEM FULLY OPERATIONAL!")
            print(f"Ready for:")
            print(f"  • Automated trading")
            print(f"  • TradingView webhook integration")
            print(f"  • Risk management")
            print(f"  • Production deployment")
        else:
            print(f"\n🔧 System needs refinement")
            print(f"Next steps:")
            print(f"  1. Capture signature algorithm")
            print(f"  2. Test with different parameters")
            print(f"  3. Consider browser automation fallback")
        
    finally:
        system.cleanup()

if __name__ == '__main__':
    main()
