# MEXC Advanced UI Automation Guide
## 🎯 **Complete Guide for All UI Elements**

Based on our breakthrough success with quantity field automation, this guide provides comprehensive patterns for automating ALL MEXC UI elements including fields, tabs, checkboxes, popups, and more.

## 🚀 **CORE BREAKTHROUGH PRINCIPLE**

**THE FUNDAMENTAL DISCOVERY:**
MEXC uses **blur events** to clear/reset form fields and trigger validations. Our solution blocks these events to maintain control.

```javascript
// UNIVERSAL BLUR PREVENTION PATTERN
function preventBlur(element) {
    // Method 1: Override blur method
    element.blur = function() { return; };
    
    // Method 2: Block blur events
    element.addEventListener('blur', function(event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
        return false;
    }, true);
    
    // Method 3: Block focusout events
    element.addEventListener('focusout', function(event) {
        event.preventDefault();
        event.stopPropagation();
        event.stopImmediatePropagation();
        return false;
    }, true);
}
```

## 📋 **1. INPUT FIELDS AUTOMATION**

### **Pattern for All Input Fields**
```javascript
function automateInputField(selector, targetValue) {
    const field = document.querySelector(selector);
    if (!field) return false;
    
    // Step 1: Apply blur prevention
    preventBlur(field);
    
    // Step 2: Advanced value protection
    const originalDescriptor = Object.getOwnPropertyDescriptor(HTMLInputElement.prototype, 'value');
    Object.defineProperty(field, 'value', {
        get: function() {
            return originalDescriptor.get.call(this);
        },
        set: function(newValue) {
            if (newValue !== targetValue && newValue !== '') {
                console.log(`Value change blocked: "${newValue}" -> "${targetValue}"`);
                return originalDescriptor.set.call(this, targetValue);
            }
            return originalDescriptor.set.call(this, newValue);
        }
    });
    
    // Step 3: Set value with comprehensive events
    field.focus();
    field.value = targetValue;
    field.dispatchEvent(new Event('input', { bubbles: true }));
    field.dispatchEvent(new Event('change', { bubbles: true }));
    
    return true;
}

// Usage Examples:
automateInputField('input[placeholder*="Quantity"]', '2.5');
automateInputField('input[placeholder*="Price"]', '0.03334');
automateInputField('input[placeholder*="Stop Loss"]', '0.03000');
automateInputField('input[placeholder*="Take Profit"]', '0.03500');
```

### **Specific Field Locations**
```javascript
const MEXC_FIELDS = {
    quantity: { selector: 'input.ant-input', position: { x: 668, y: 603 } },
    price: { selector: 'input.ant-input', position: { x: 668, y: 523 } },
    stopLoss: { selector: 'input[placeholder*="Stop"]', position: { x: 668, y: 683 } },
    takeProfit: { selector: 'input[placeholder*="Profit"]', position: { x: 668, y: 743 } },
    leverage: { selector: 'input[placeholder*="Leverage"]', position: { x: 668, y: 463 } }
};
```

## 🔘 **2. CHECKBOX AUTOMATION**

### **Checkbox Pattern**
```javascript
function automateCheckbox(selector, checked = true) {
    const checkbox = document.querySelector(selector);
    if (!checkbox) return false;
    
    // Step 1: Apply blur prevention
    preventBlur(checkbox);
    
    // Step 2: Set checked state
    checkbox.checked = checked;
    checkbox.dispatchEvent(new Event('change', { bubbles: true }));
    checkbox.dispatchEvent(new Event('click', { bubbles: true }));
    
    // Step 3: Verify state
    setTimeout(() => {
        if (checkbox.checked !== checked) {
            checkbox.checked = checked;
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        }
    }, 100);
    
    return true;
}

// Usage Examples:
automateCheckbox('input[type="checkbox"][name="reduce-only"]', true);
automateCheckbox('input[type="checkbox"][name="post-only"]', false);
automateCheckbox('.ant-checkbox-input', true);
```

## 📑 **3. TAB NAVIGATION AUTOMATION**

### **Tab Switching Pattern**
```javascript
function switchTab(tabText) {
    // Find tab by text content
    const tabs = document.querySelectorAll('.ant-tabs-tab');
    
    for (const tab of tabs) {
        if (tab.textContent.includes(tabText)) {
            // Use MouseEvent to prevent focus issues
            const rect = tab.getBoundingClientRect();
            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                clientX: rect.x + rect.width / 2,
                clientY: rect.y + rect.height / 2
            });
            
            tab.dispatchEvent(clickEvent);
            
            // Verify tab is active
            setTimeout(() => {
                if (!tab.classList.contains('ant-tabs-tab-active')) {
                    tab.click(); // Fallback
                }
            }, 500);
            
            return true;
        }
    }
    
    return false;
}

// Usage Examples:
switchTab('Limit');      // Switch to Limit order tab
switchTab('Market');     // Switch to Market order tab
switchTab('Stop');       // Switch to Stop order tab
switchTab('Positions');  // Switch to Positions tab
switchTab('Orders');     // Switch to Orders tab
```

## 🎛️ **4. DROPDOWN/SELECT AUTOMATION**

### **Dropdown Pattern**
```javascript
function automateDropdown(dropdownSelector, optionText) {
    const dropdown = document.querySelector(dropdownSelector);
    if (!dropdown) return false;
    
    // Step 1: Open dropdown
    dropdown.click();
    
    // Step 2: Wait for options to appear
    setTimeout(() => {
        const options = document.querySelectorAll('.ant-select-item, .ant-dropdown-menu-item');
        
        for (const option of options) {
            if (option.textContent.includes(optionText)) {
                // Use MouseEvent for reliable clicking
                const rect = option.getBoundingClientRect();
                const clickEvent = new MouseEvent('click', {
                    bubbles: true,
                    cancelable: true,
                    clientX: rect.x + rect.width / 2,
                    clientY: rect.y + rect.height / 2
                });
                
                option.dispatchEvent(clickEvent);
                return true;
            }
        }
    }, 200);
    
    return true;
}

// Usage Examples:
automateDropdown('.ant-select-selector', '10x');     // Set leverage to 10x
automateDropdown('.ant-select-selector', 'Cross');   // Set margin mode to Cross
automateDropdown('.ant-select-selector', 'GTC');     // Set time in force to GTC
```

## 🪟 **5. POPUP/MODAL AUTOMATION**

### **Popup Detection and Handling**
```javascript
function handlePopups() {
    const popupSelectors = [
        '.ant-modal',
        '.ant-notification',
        '.ant-message',
        '.modal',
        '[role="dialog"]',
        '.popup',
        '.overlay'
    ];
    
    const results = {
        popupsFound: 0,
        popupsClosed: 0,
        errors: []
    };
    
    for (const selector of popupSelectors) {
        const popups = document.querySelectorAll(selector);
        
        popups.forEach(popup => {
            const rect = popup.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
                results.popupsFound++;
                
                // Try to find close button
                const closeButtons = popup.querySelectorAll(
                    '.ant-modal-close, .ant-notification-close, .close, [aria-label="close"], .modal-close'
                );
                
                if (closeButtons.length > 0) {
                    closeButtons[0].click();
                    results.popupsClosed++;
                } else {
                    // Try clicking outside the popup
                    const overlay = popup.querySelector('.ant-modal-mask, .overlay, .backdrop');
                    if (overlay) {
                        overlay.click();
                        results.popupsClosed++;
                    }
                }
            }
        });
    }
    
    return results;
}

// Auto-close popups every 2 seconds
setInterval(handlePopups, 2000);
```

## 🔄 **6. BUTTON AUTOMATION (ADVANCED)**

### **Focus-Maintaining Button Clicks**
```javascript
function clickButtonSafely(buttonSelector, maintainFocus = null) {
    const button = document.querySelector(buttonSelector);
    if (!button) return false;
    
    // Step 1: If we need to maintain focus on another element
    if (maintainFocus) {
        maintainFocus.focus();
    }
    
    // Step 2: Use MouseEvent to avoid focus changes
    const rect = button.getBoundingClientRect();
    const clickEvent = new MouseEvent('click', {
        bubbles: true,
        cancelable: true,
        clientX: rect.x + rect.width / 2,
        clientY: rect.y + rect.height / 2
    });
    
    button.dispatchEvent(clickEvent);
    
    // Step 3: Restore focus if needed
    if (maintainFocus) {
        setTimeout(() => {
            maintainFocus.focus();
        }, 10);
    }
    
    return true;
}

// Usage Examples:
const quantityField = document.querySelector('input[placeholder*="Quantity"]');
clickButtonSafely('button.component_longBtn__eazYU', quantityField);  // Buy button
clickButtonSafely('button.component_shortBtn__x5P3I', quantityField); // Sell button
clickButtonSafely('.ant-btn-primary', quantityField);                 // Confirm button
```

## 🎯 **7. COMPLETE AUTOMATION WORKFLOW**

### **Full Trading Automation Example**
```javascript
async function executeCompleteTradeAutomation(tradeConfig) {
    const {
        symbol,
        side,           // 'BUY' or 'SELL'
        quantity,
        price,
        orderType,      // 'LIMIT', 'MARKET', 'STOP'
        leverage,
        stopLoss,
        takeProfit,
        reduceOnly,
        postOnly
    } = tradeConfig;
    
    console.log('Starting complete trade automation...');
    
    try {
        // Step 1: Handle any existing popups
        handlePopups();
        
        // Step 2: Switch to correct order type tab
        if (orderType !== 'LIMIT') {
            switchTab(orderType);
            await sleep(500);
        }
        
        // Step 3: Set leverage if specified
        if (leverage) {
            automateDropdown('.leverage-selector', `${leverage}x`);
            await sleep(300);
        }
        
        // Step 4: Fill quantity field (CRITICAL - use our breakthrough method)
        const quantitySuccess = automateInputField('input[placeholder*="Quantity"]', quantity);
        if (!quantitySuccess) {
            throw new Error('Failed to set quantity');
        }
        
        // Step 5: Fill price field if limit order
        if (orderType === 'LIMIT' && price) {
            automateInputField('input[placeholder*="Price"]', price);
            await sleep(200);
        }
        
        // Step 6: Set stop loss if specified
        if (stopLoss) {
            automateInputField('input[placeholder*="Stop"]', stopLoss);
            await sleep(200);
        }
        
        // Step 7: Set take profit if specified
        if (takeProfit) {
            automateInputField('input[placeholder*="Profit"]', takeProfit);
            await sleep(200);
        }
        
        // Step 8: Set checkboxes
        if (reduceOnly !== undefined) {
            automateCheckbox('input[name="reduce-only"]', reduceOnly);
        }
        if (postOnly !== undefined) {
            automateCheckbox('input[name="post-only"]', postOnly);
        }
        
        // Step 9: Execute trade with focus maintenance
        const quantityField = document.querySelector('input[placeholder*="Quantity"]');
        const buttonClass = side === 'BUY' ? 'component_longBtn__eazYU' : 'component_shortBtn__x5P3I';
        
        const tradeSuccess = clickButtonSafely(`button.${buttonClass}`, quantityField);
        
        if (!tradeSuccess) {
            throw new Error('Failed to click trade button');
        }
        
        // Step 10: Wait and verify
        await sleep(2000);
        
        // Check for errors
        const errors = document.querySelectorAll('.ant-message-error, .error');
        if (errors.length > 0) {
            const errorTexts = Array.from(errors).map(e => e.textContent);
            throw new Error(`Trade errors: ${errorTexts.join(', ')}`);
        }
        
        console.log('Trade automation completed successfully');
        return { success: true, message: 'Trade executed successfully' };
        
    } catch (error) {
        console.error('Trade automation failed:', error);
        return { success: false, error: error.message };
    }
}

// Helper function
function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}
```

## 🛡️ **8. ERROR HANDLING AND RECOVERY**

### **Comprehensive Error Detection**
```javascript
function detectAndHandleErrors() {
    const errorSelectors = [
        '.ant-message-error',
        '.ant-notification-notice-error',
        '.ant-form-item-explain-error',
        '.error',
        '[class*="error"]',
        '.validation-error'
    ];
    
    const errors = [];
    
    for (const selector of errorSelectors) {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            const text = element.textContent || '';
            if (text.trim()) {
                errors.push({
                    selector: selector,
                    text: text.trim(),
                    element: element,
                    visible: element.getBoundingClientRect().width > 0
                });
            }
        });
    }
    
    // Handle specific error types
    errors.forEach(error => {
        const text = error.text.toLowerCase();
        
        if (text.includes('quantity') || text.includes('amount')) {
            console.log('Quantity error detected - reapplying blur prevention');
            const quantityField = document.querySelector('input[placeholder*="Quantity"]');
            if (quantityField) {
                preventBlur(quantityField);
                automateInputField('input[placeholder*="Quantity"]', window.targetQuantity);
            }
        }
        
        if (text.includes('price')) {
            console.log('Price error detected - checking price field');
            // Handle price-related errors
        }
        
        if (text.includes('balance') || text.includes('insufficient')) {
            console.log('Balance error detected - trade cannot proceed');
            // Handle insufficient balance
        }
    });
    
    return errors;
}

// Run error detection every 1 second
setInterval(detectAndHandleErrors, 1000);
```

## 🎯 **9. USAGE EXAMPLES**

### **Simple Trade Execution**
```javascript
// Execute a simple buy order
executeCompleteTradeAutomation({
    symbol: 'TRU_USDT',
    side: 'BUY',
    quantity: '2.5',
    orderType: 'MARKET'
});
```

### **Advanced Limit Order with Stop Loss**
```javascript
// Execute advanced limit order
executeCompleteTradeAutomation({
    symbol: 'BTC_USDT',
    side: 'BUY',
    quantity: '0.1',
    price: '45000',
    orderType: 'LIMIT',
    leverage: '10',
    stopLoss: '44000',
    takeProfit: '47000',
    postOnly: true,
    reduceOnly: false
});
```

## 🚀 **10. INTEGRATION WITH PYTHON**

### **Python Wrapper Example**
```python
def execute_mexc_trade(page, trade_config):
    """Execute trade using our JavaScript automation"""
    
    automation_script = f"""
    () => {{
        // Include all the JavaScript functions above
        {BLUR_PREVENTION_CODE}
        {AUTOMATION_FUNCTIONS}
        
        // Execute the trade
        return executeCompleteTradeAutomation({trade_config});
    }}
    """
    
    result = page.evaluate(automation_script)
    return result
```

This comprehensive guide provides the complete framework for automating ANY UI element on MEXC using our breakthrough blur prevention technique. The key is always to prevent blur events and maintain control over form elements.
