# Path B: MEXC Futures Trading via Web Session (Head<PERSON> Browser)

This document captures the plan to implement programmatic Futures order placement on MEXC by using a logged‑in web session (browser automation), since API "Order Placing" is not supported on the current account.

## Goals
- Place and cancel Futures orders programmatically by leveraging the same flows the web app uses
- Keep session alive (cookies, tokens) with auto‑refresh; handle WAF and headers
- Provide a clean Python facade: place_order(), cancel_order(), list_open_orders()
- Minimize risk during tests: Post‑Only Maker orders, far from market, and immediate cancel

## Constraints and Risks
- Terms of Service: This approach may be disallowed; use only with approval
- 2FA: Login may require TOTP; need either secret or manual code input
- WAF/Headers: Must mimic browser (User‑Agent, sec‑ch‑ua, fetch headers) and pass anti‑bot checks
- Fragility: Web endpoints and frontend contracts can change without notice; build resilient fallbacks

## High‑Level Approach
1) Attach to live Edge via CDP (your real session)
   - Reuse your cookies/localStorage; no password handling in code
   - Navigate to futures.mexc.com; ensure you are logged in
2) UI‑driven place/cancel (primary)
   - Drive the order form (Isolated, leverage, Post‑Only, price, vol) and click submit
   - The site mints x-mxc-sign/x-mxc-nonce/mtoken and p0/k0 automatically
   - Cancel via UI; verify orderId in table
3) Optional XHR replication (secondary)
   - Only if stable and necessary; otherwise stick to UI for robustness
4) Resilience
   - Session re‑attach; instruct login if needed
   - Retries with short backoff; minimal telemetry

## Architecture
- Module: webtrader/
  - session_manager.py
    - login(headless: bool, totp_secret: Optional[str]) -> storage_state
    - load_state() / save_state()
    - ensure_session_alive() -> bool (refreshes if needed)
  - mexc_web_client.py
    - place_order(symbol, side, type, price, vol, open_type, leverage, reduce_only=False)
    - cancel_order(order_id)
    - list_open_orders(symbol=None)
    - Internally: either (A) XHR with cookies/headers or (B) UI automation fallback
  - headers.py
    - functions to produce browser‑like headers (sec‑ch‑ua, sec‑fetch, UA, origin/referer)
  - totp.py (optional)
    - generate TOTP codes from a shared secret
  - cli.py
    - demo: place Post‑Only order far from market -> verify open -> cancel immediately

## Data Flow
- First‑run: login() -> solve 2FA -> save storage_state.json.enc
- On each action: ensure_session_alive() -> make XHR with cookies -> parse JSON -> return strong types
- On failure 401/403: re‑login and retry once

## Security
- Never print credentials or tokens to logs
- Store storage_state encrypted at rest (Fernet key in .env, e.g., WEB_SESSION_KEY)
- Keep Playwright in sandboxed user‑data dir; isolate profiles
- Optional: run all traffic via a trusted residential proxy to stabilize WAF behavior

## Testing Strategy
- Local smoke:
  - ping public endpoints via XHR
  - list_open_orders (should be 200)
  - place Post‑Only order with price far from market
  - verify in open orders; then cancel and verify removal
- Negative tests:
  - Invalid cookies -> expect 401, auto re‑login path
  - Network timeouts -> retry with backoff
- CI suggestion:
  - Do not run real order tests in CI
  - Provide a mocked server or record/replay harness for unit tests

## Step‑by‑Step Implementation Plan
1) Scaffolding
   - Create webtrader/ package with session_manager, mexc_web_client, headers, cli
2) Playwright bootstrap
   - Add playwright dependency; script to install chromium
   - Implement login() with optional headless toggle and TOTP support
   - Save/load encrypted storage state
3) Headers and WAF stabilization
   - Generate Chrome‑like headers matching our successful patterns
   - Keep origin/referer to futures.mexc.com/exchange
4) Discover and replicate XHR endpoints
   - Use DevTools to capture requests the site makes for place/cancel/open orders
   - Reproduce them via curl_cffi with cookies
5) Facade functions
   - place_order(), cancel_order(), list_open_orders() with robust error handling
6) CLI and smoke tests
   - CLI command: place‑and‑cancel Post‑Only test order
   - Logging summary output (status codes, codes/messages, orderId)
7) Hardening
   - Auto re‑login on 401/403
   - Token/cookie refresh loop
   - Backoff policy

## Operational Notes
- Keep separate .env values for browser login: MEXC_LOGIN_EMAIL, MEXC_LOGIN_PASSWORD, MEXC_TOTP_SECRET (optional)
- Configure WEB_SESSION_KEY for encrypting storage state
- Consider proxy settings if needed (residential IPs are often more stable)

## Open Questions
- Do we have TOTP secret or do we prompt interactively at runtime?
- Do we prefer direct XHR or UI click as the default path? (recommend: XHR first, UI fallback)
- Any region/IP constraints requiring proxies?

---

## TODO
- [ ] Scaffold webtrader/ module (session_manager.py, mexc_web_client.py, headers.py, cli.py)
- [ ] Add Playwright to project; login() with headless flag and TOTP support
- [ ] Implement encrypted storage_state persistence (Fernet + WEB_SESSION_KEY)
- [ ] Implement headers helper producing Chrome‑like headers (sec‑ch‑ua etc.)
- [ ] Capture XHR endpoints for: place order, cancel order, open orders, order history
- [ ] Implement XHR client using curl_cffi with cookies from storage_state
- [ ] Implement place_order() with Post‑Only support and validation
- [ ] Implement cancel_order() and list_open_orders()
- [ ] Implement UI‑fallback for place/cancel if XHR fails (Playwright page.click flows)
- [ ] CLI demo: place far‑from‑market Post‑Only order, verify, cancel, verify
- [ ] Add structured logging and retry/backoff
- [ ] Document 2FA flows and ops runbook
- [ ] Create minimal unit tests (mocked) and a local smoke test script

---

## Changelog
- 2025‑08‑11 v0.1.0
  - Initial Path B implementation plan drafted
  - Added Goals, Constraints, Architecture, Security, Testing, and Step‑by‑Step plan
  - Seeded TODO list and initial changelog

