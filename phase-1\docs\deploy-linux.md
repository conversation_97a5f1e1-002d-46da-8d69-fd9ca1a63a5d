# Deploy on Linux with Reverse Proxy (Nginx) and UI Automation

## Overview
- Nginx reverse proxy exposes /tv-webhook and /admin/health
- FastAPI service handles TradingView alerts and calls UI executor
- UI executor attaches to a headful Chromium/Edge session via CDP and automates MEXC UI

## Prereqs
- Ubuntu 22.04+ (or similar)
- Python 3.10+
- Nginx installed
- Playwright + Chromium: `pip install playwright && playwright install chromium`
- Xvfb for headful runs: `sudo apt-get install -y xvfb` (or run chromium with a real display)

## System layout
- /opt/trader
  - services/api/app.py (FastAPI)
  - services/executor/ui_executor.py (Playwright UI automation)
  - venv
- Nginx reverse proxy listens on 80/443 and proxies to 127.0.0.1:8000

## FastAPI service (dev run)
- export TV_SHARED_SECRET=your-secret
- uvicorn services.api.app:app --host 127.0.0.1 --port 8000

## Nginx config
- Copy docker/nginx.conf into /etc/nginx/nginx.conf (or add equivalent server block)
- Create /etc/nginx/.htpasswd for /admin/health:
  - `sudo sh -c "openssl passwd -apr1 | awk '{print \$0}' > /etc/nginx/.htpasswd"` (then edit to add `user:hash`)
- `sudo nginx -t && sudo systemctl reload nginx`

## Browser session for UI automation
Option 1 (Chromium):
- Run a persistent Chromium with remote debugging:
  - `chromium --remote-debugging-port=9222 --user-data-dir=/opt/trader/chrome-profile`
- First time: open futures.mexc.com, log in, complete 2FA

Option 2 (Headless server with Xvfb):
- `Xvfb :99 -screen 0 1360x900x24 & export DISPLAY=:99`
- `chromium --remote-debugging-port=9222 --user-data-dir=/opt/trader/chrome-profile &`
- Use SSH port forward or a temporary VNC to complete login once

## TradingView setup
- Alert URL: https://yourdomain.com/tv-webhook
- Method: POST, Content-Type: application/json
- Example payload:
```
{
  "secret": "your-secret",
  "symbol": "TRU_USDT",
  "side": "long",
  "price": 0.02,
  "vol": 1,
  "post_only": true,
  "cancel_after_ms": 10000
}
```

## Observability
- Nginx access/error logs for inbound requests
- FastAPI logs; add file logging if desired

## Safety
- Don’t expose the CDP port publicly. It must remain 127.0.0.1 or bound to localhost behind a firewall.
- Protect /admin with basic auth or OAuth and IP allowlists
- Use Post‑Only and far‑from‑market pricing to avoid unintended fills when testing

## Hardening (later)
- Systemd services for uvicorn worker and a watchdog for the browser process
- TLS via certbot for Nginx
- Redis job queue to decouple webhook from UI executor
- Login assist page that temporarily shows the live browser for 2FA

