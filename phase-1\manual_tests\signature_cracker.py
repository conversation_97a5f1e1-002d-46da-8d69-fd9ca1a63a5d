#!/usr/bin/env python3
"""
SIGNATURE CRACKER - BRUTE FORCE APPROACH
We WILL crack this signature algorithm!
"""

import json
import hashlib
import hmac
import base64
import urllib.parse
import itertools
import binascii
import struct

# Real captured signatures - we WILL crack these!
SIGNATURES = [
    {
        "sig": "e5d090fa331cef9aa0921b014f53210e",
        "nonce": "1754926560782",
        "auth": "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6",
        "body": '{"symbol":"TRU_USDT","side":1,"openType":1,"type":"2","vol":1,"leverage":1,"marketCeiling":false,"price":"0.02","priceProtect":"0","p0":"...","k0":"...","chash":"...","mtoken":"b03MOmeXoiZid75ogtwP","ts":1754926562073,"mhash":"85723e9fb269ff0e1e19525050842a3c"}'
    },
    {
        "sig": "e048fb8b1b6e42caf416298ce272548f",
        "nonce": "1754926571186", 
        "auth": "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6",
        "body": '{"symbol":"TRU_USDT","side":1,"openType":1,"type":"2","vol":1,"leverage":1,"marketCeiling":false,"price":"0.02","priceProtect":"0","p0":"...","k0":"...","chash":"...","mtoken":"b03MOmeXoiZid75ogtwP","ts":1754926573186,"mhash":"85723e9fb269ff0e1e19525050842a3c"}'
    },
    {
        "sig": "047836d7d32b9c04a4671e8ad93e5baf",
        "nonce": "1754926580545",
        "auth": "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6", 
        "body": '{"symbol":"TRU_USDT","side":1,"openType":1,"type":"2","vol":1,"leverage":1,"marketCeiling":false,"price":"0.02","priceProtect":"0","p0":"...","k0":"...","chash":"...","mtoken":"b03MOmeXoiZid75ogtwP","ts":1754926582545,"mhash":"85723e9fb269ff0e1e19525050842a3c"}'
    }
]

class SignatureCracker:
    """BRUTE FORCE signature algorithm cracker"""
    
    def __init__(self):
        print("💀 SIGNATURE CRACKER - BRUTE FORCE MODE")
        print("="*50)
        print("🎯 WE WILL CRACK THIS ALGORITHM!")
        self.attempts = 0
        self.max_attempts = 100000
    
    def test_algorithm(self, data, algorithm_func, description):
        """Test a specific algorithm"""
        self.attempts += 1
        
        try:
            result = algorithm_func(data)
            if result and len(result) == 32:
                # Check against all known signatures
                for sig_data in SIGNATURES:
                    if result.lower() == sig_data['sig'].lower():
                        print(f"🎉 CRACKED! Algorithm: {description}")
                        print(f"   Input: {data[:100]}...")
                        print(f"   Output: {result}")
                        print(f"   Matches signature: {sig_data['sig']}")
                        return True
        except:
            pass
        
        if self.attempts % 1000 == 0:
            print(f"   Tested {self.attempts} algorithms...")
        
        return False
    
    def crack_with_brute_force(self):
        """Brute force approach - test EVERYTHING"""
        
        print("\n💥 STARTING BRUTE FORCE ATTACK")
        print("="*40)
        
        auth = SIGNATURES[0]['auth']
        
        for i, sig_data in enumerate(SIGNATURES):
            print(f"\n🎯 Cracking signature #{i+1}: {sig_data['sig']}")
            nonce = sig_data['nonce']
            body = sig_data['body']
            
            # Parse body to get components
            try:
                body_json = json.loads(body)
                symbol = body_json.get('symbol', '')
                side = str(body_json.get('side', ''))
                price = body_json.get('price', '')
                vol = str(body_json.get('vol', ''))
                ts = str(body_json.get('ts', ''))
                mhash = body_json.get('mhash', '')
                mtoken = body_json.get('mtoken', '')
            except:
                symbol = side = price = vol = ts = mhash = mtoken = ''
            
            # Create all possible input combinations
            components = [
                auth,
                nonce, 
                symbol,
                side,
                price,
                vol,
                ts,
                mhash,
                mtoken,
                body,
                json.dumps(body_json, separators=(',', ':')),
                json.dumps(body_json, separators=(',', ':'), sort_keys=True),
                # Auth parts
                auth[:32],
                auth[32:],
                auth[:16],
                auth[-16:],
                # Nonce variations
                str(int(nonce) // 1000),  # timestamp in seconds
                nonce[:10],
                nonce[-10:],
                # Combinations
                f"{auth}{nonce}",
                f"{nonce}{auth}",
                f"{auth[:32]}{nonce}",
                f"{nonce}{auth[:32]}",
                f"{auth}{nonce}{symbol}",
                f"{nonce}{symbol}{auth}",
                f"{auth}{nonce}{price}",
                f"{nonce}{price}{auth}",
                f"{auth}{nonce}{ts}",
                f"{nonce}{ts}{auth}",
                f"{auth}{nonce}{mhash}",
                f"{nonce}{mhash}{auth}",
                f"{auth}{nonce}{mtoken}",
                f"{nonce}{mtoken}{auth}",
                # Complex combinations
                f"{auth}{nonce}{symbol}{side}{price}{vol}",
                f"{nonce}{symbol}{side}{price}{vol}{auth}",
                f"{auth}{nonce}{symbol}{side}{price}{vol}{ts}",
                f"{nonce}{symbol}{side}{price}{vol}{ts}{auth}",
                f"{auth}{nonce}{mtoken}{ts}{mhash}",
                f"{nonce}{mtoken}{ts}{mhash}{auth}",
            ]
            
            # Test with different separators
            separators = ['', '|', '&', ':', ';', ',', '_', '-', '+', '=']
            
            for sep in separators:
                components.extend([
                    f"{auth}{sep}{nonce}",
                    f"{nonce}{sep}{auth}",
                    f"{auth}{sep}{nonce}{sep}{symbol}",
                    f"{nonce}{sep}{symbol}{sep}{auth}",
                    f"{auth}{sep}{nonce}{sep}{mtoken}",
                    f"{nonce}{sep}{mtoken}{sep}{auth}",
                ])
            
            # Test all hash algorithms on all combinations
            hash_functions = [
                (lambda x: hashlib.md5(x.encode()).hexdigest(), "MD5"),
                (lambda x: hashlib.sha1(x.encode()).hexdigest()[:32], "SHA1-32"),
                (lambda x: hashlib.sha256(x.encode()).hexdigest()[:32], "SHA256-32"),
                (lambda x: hashlib.sha512(x.encode()).hexdigest()[:32], "SHA512-32"),
                # With different encodings
                (lambda x: hashlib.md5(x.encode('utf-8')).hexdigest(), "MD5-UTF8"),
                (lambda x: hashlib.md5(x.encode('latin1')).hexdigest(), "MD5-LATIN1"),
                # Base64 then hash
                (lambda x: hashlib.md5(base64.b64encode(x.encode()).decode().encode()).hexdigest(), "MD5-B64"),
                # URL encode then hash
                (lambda x: hashlib.md5(urllib.parse.quote(x).encode()).hexdigest(), "MD5-URL"),
                # Double hash
                (lambda x: hashlib.md5(hashlib.md5(x.encode()).hexdigest().encode()).hexdigest(), "MD5-DOUBLE"),
                # Reverse then hash
                (lambda x: hashlib.md5(x[::-1].encode()).hexdigest(), "MD5-REVERSE"),
                # Upper/lower case
                (lambda x: hashlib.md5(x.upper().encode()).hexdigest(), "MD5-UPPER"),
                (lambda x: hashlib.md5(x.lower().encode()).hexdigest(), "MD5-LOWER"),
            ]
            
            print(f"   Testing {len(components)} input combinations with {len(hash_functions)} algorithms...")
            
            for component in components:
                if not component:
                    continue
                    
                for hash_func, desc in hash_functions:
                    if self.test_algorithm(component, hash_func, f"{desc}({component[:30]}...)"):
                        return True
                    
                    if self.attempts >= self.max_attempts:
                        print(f"⚠️ Reached max attempts ({self.max_attempts})")
                        break
                
                if self.attempts >= self.max_attempts:
                    break
            
            # Test HMAC with different keys
            print(f"   Testing HMAC algorithms...")
            
            hmac_keys = [auth, nonce, mtoken, auth[:32], auth[32:], nonce[:10]]
            hmac_messages = components[:20]  # Test top 20 combinations
            
            for key in hmac_keys:
                if not key:
                    continue
                for message in hmac_messages:
                    if not message:
                        continue
                    
                    hmac_functions = [
                        (lambda k, m: hmac.new(k.encode(), m.encode(), hashlib.md5).hexdigest(), "HMAC-MD5"),
                        (lambda k, m: hmac.new(k.encode(), m.encode(), hashlib.sha1).hexdigest()[:32], "HMAC-SHA1-32"),
                        (lambda k, m: hmac.new(k.encode(), m.encode(), hashlib.sha256).hexdigest()[:32], "HMAC-SHA256-32"),
                    ]
                    
                    for hmac_func, desc in hmac_functions:
                        try:
                            result = hmac_func(key, message)
                            if self.test_algorithm(f"{key}|{message}", lambda x: result, f"{desc}(key={key[:10]}..., msg={message[:20]}...)"):
                                return True
                        except:
                            continue
                        
                        if self.attempts >= self.max_attempts:
                            break
                    
                    if self.attempts >= self.max_attempts:
                        break
                
                if self.attempts >= self.max_attempts:
                    break
        
        print(f"\n❌ Brute force failed after {self.attempts} attempts")
        return False
    
    def crack_with_pattern_analysis(self):
        """Try to find patterns in the signatures"""
        
        print("\n🔍 PATTERN ANALYSIS ATTACK")
        print("="*35)
        
        # Analyze signature patterns
        sigs = [s['sig'] for s in SIGNATURES]
        nonces = [s['nonce'] for s in SIGNATURES]
        
        print("Signatures:")
        for i, sig in enumerate(sigs):
            print(f"  {i+1}: {sig}")
        
        print("\nNonces:")
        for i, nonce in enumerate(nonces):
            print(f"  {i+1}: {nonce}")
        
        # Look for XOR patterns
        print("\n🔀 Testing XOR patterns...")
        
        for i in range(len(sigs)):
            for j in range(i+1, len(sigs)):
                sig1 = bytes.fromhex(sigs[i])
                sig2 = bytes.fromhex(sigs[j])
                
                xor_result = bytes(a ^ b for a, b in zip(sig1, sig2))
                xor_hex = xor_result.hex()
                
                print(f"  XOR({i+1},{j+1}): {xor_hex}")
                
                # Check if XOR result has patterns
                if len(set(xor_hex)) < 8:  # Low entropy might indicate pattern
                    print(f"    ⚠️ Low entropy XOR detected!")
        
        # Test if signatures are related to nonce differences
        print("\n📊 Testing nonce relationships...")
        
        for i in range(len(nonces)):
            for j in range(i+1, len(nonces)):
                nonce_diff = abs(int(nonces[i]) - int(nonces[j]))
                print(f"  Nonce diff({i+1},{j+1}): {nonce_diff}")
                
                # Test if signature difference relates to nonce difference
                sig1_int = int(sigs[i], 16)
                sig2_int = int(sigs[j], 16)
                sig_diff = abs(sig1_int - sig2_int)
                
                ratio = sig_diff / nonce_diff if nonce_diff > 0 else 0
                print(f"    Sig diff: {sig_diff}, Ratio: {ratio:.2f}")
    
    def run_crack_attack(self):
        """Run the complete cracking attack"""
        
        print("="*60)
        print("🚨 SIGNATURE ALGORITHM CRACKING IN PROGRESS 🚨")
        print("="*60)
        
        # Try pattern analysis first
        self.crack_with_pattern_analysis()
        
        # Then brute force
        if not self.crack_with_brute_force():
            print("\n💀 FIRST WAVE FAILED - ESCALATING ATTACK")
            self.max_attempts = 500000  # Increase attempts
            
            # Try more exotic algorithms
            print("\n🧪 TESTING EXOTIC ALGORITHMS...")
            # This would continue with more sophisticated approaches
            
        print(f"\n📊 TOTAL ATTEMPTS: {self.attempts}")

def main():
    """Main cracking function"""
    
    cracker = SignatureCracker()
    cracker.run_crack_attack()

if __name__ == '__main__':
    main()
