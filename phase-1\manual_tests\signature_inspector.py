#!/usr/bin/env python3
"""
Signature Inspector: Detailed analysis of captured signatures
"""

import json
import hashlib
import hmac
import base64
import urllib.parse

# Real captured data from our previous captures
CAPTURED_SIGNATURES = [
    {
        "signature": "e5d090fa331cef9aa0921b014f53210e",
        "nonce": "1754926560782",
        "auth": "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6",
        "symbol": "TRU_USDT",
        "side": 1,
        "price": "0.02",
        "vol": 1
    },
    {
        "signature": "e048fb8b1b6e42caf416298ce272548f", 
        "nonce": "1754926571186",
        "auth": "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6",
        "symbol": "TRU_USDT",
        "side": 1,
        "price": "0.02",
        "vol": 1
    },
    {
        "signature": "047836d7d32b9c04a4671e8ad93e5baf",
        "nonce": "1754926580545", 
        "auth": "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6",
        "symbol": "TRU_USDT",
        "side": 1,
        "price": "0.02",
        "vol": 1
    },
    # Original captured signature
    {
        "signature": "ec2e0051aaa08af9fe4f22568726fdc5",
        "nonce": "1754926229710",
        "auth": "WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6",
        "symbol": "TRU_USDT",
        "side": 1,
        "price": "0.02",
        "vol": 1
    }
]

class SignatureInspector:
    """Detailed signature analysis"""
    
    def __init__(self):
        print("🔬 MEXC Signature Inspector")
        print("="*35)
        print("🎯 Deep analysis of signature patterns")
    
    def analyze_signature_patterns(self):
        """Analyze patterns in captured signatures"""
        
        print(f"\n📊 ANALYZING {len(CAPTURED_SIGNATURES)} SIGNATURES")
        print("="*50)
        
        for i, data in enumerate(CAPTURED_SIGNATURES):
            print(f"\n🔍 SIGNATURE #{i+1}:")
            print(f"   Signature: {data['signature']}")
            print(f"   Nonce: {data['nonce']}")
            print(f"   Symbol: {data['symbol']}")
            print(f"   Side: {data['side']}")
            print(f"   Price: {data['price']}")
            print(f"   Vol: {data['vol']}")
            
            # Analyze signature characteristics
            sig = data['signature']
            print(f"   Length: {len(sig)} chars")
            print(f"   Hex valid: {all(c in '0123456789abcdef' for c in sig)}")
            print(f"   First 8: {sig[:8]}")
            print(f"   Last 8: {sig[-8:]}")
    
    def test_time_based_patterns(self):
        """Test if signatures are time-based"""
        
        print(f"\n⏰ TIME-BASED PATTERN ANALYSIS")
        print("="*40)
        
        for i, data in enumerate(CAPTURED_SIGNATURES):
            nonce = data['nonce']
            signature = data['signature']
            
            print(f"\n🔍 Signature #{i+1}:")
            print(f"   Nonce: {nonce}")
            print(f"   Signature: {signature}")
            
            # Test if nonce is involved in signature
            nonce_hash = hashlib.md5(nonce.encode()).hexdigest()
            print(f"   MD5(nonce): {nonce_hash}")
            
            # Check if any part of signature matches nonce hash
            if signature[:8] in nonce_hash or signature[-8:] in nonce_hash:
                print(f"   ✅ Possible nonce-based component found!")
            
            # Test timestamp variations
            timestamp = int(nonce) // 1000  # Convert to seconds
            ts_hash = hashlib.md5(str(timestamp).encode()).hexdigest()
            print(f"   MD5(timestamp): {ts_hash}")
    
    def test_auth_based_patterns(self):
        """Test auth token based patterns"""
        
        print(f"\n🔐 AUTH-BASED PATTERN ANALYSIS")
        print("="*40)
        
        auth = CAPTURED_SIGNATURES[0]['auth']  # Same for all
        print(f"Auth token: {auth}")
        
        # Test different parts of auth token
        auth_parts = [
            auth,
            auth[:32],
            auth[32:],
            auth[:16],
            auth[-16:],
            auth[16:48]
        ]
        
        for i, part in enumerate(auth_parts):
            if part:
                part_hash = hashlib.md5(part.encode()).hexdigest()
                print(f"MD5(auth_part_{i}): {part_hash}")
                
                # Check against signatures
                for j, data in enumerate(CAPTURED_SIGNATURES):
                    if part_hash == data['signature']:
                        print(f"   🎉 MATCH with signature #{j+1}!")
    
    def test_combined_patterns(self):
        """Test combinations of different elements"""
        
        print(f"\n🧪 COMBINED PATTERN ANALYSIS")
        print("="*40)
        
        for i, data in enumerate(CAPTURED_SIGNATURES):
            print(f"\n🔍 Testing signature #{i+1}: {data['signature']}")
            
            auth = data['auth']
            nonce = data['nonce']
            signature = data['signature']
            
            # Test various combinations
            test_combinations = [
                # Basic combinations
                f"{auth}{nonce}",
                f"{nonce}{auth}",
                f"{auth[:32]}{nonce}",
                f"{nonce}{auth[:32]}",
                f"{auth[-32:]}{nonce}",
                f"{nonce}{auth[-32:]}",
                
                # With symbol/price/vol
                f"{auth}{nonce}{data['symbol']}",
                f"{nonce}{data['symbol']}{auth}",
                f"{auth}{nonce}{data['price']}",
                f"{nonce}{data['price']}{auth}",
                f"{auth}{nonce}{data['vol']}",
                f"{nonce}{data['vol']}{auth}",
                
                # Complex combinations
                f"{auth}{nonce}{data['symbol']}{data['side']}{data['price']}{data['vol']}",
                f"{nonce}{data['symbol']}{data['side']}{data['price']}{data['vol']}{auth}",
                
                # URL encoded
                urllib.parse.quote(f"{auth}{nonce}"),
                
                # Base64 variations
                base64.b64encode(f"{auth}{nonce}".encode()).decode(),
            ]
            
            found_match = False
            for j, combo in enumerate(test_combinations):
                try:
                    # Test MD5
                    test_hash = hashlib.md5(combo.encode()).hexdigest()
                    if test_hash == signature:
                        print(f"   🎉 MD5 MATCH found!")
                        print(f"      Combination #{j}: {combo[:50]}...")
                        print(f"      Pattern: {self.describe_combination(j)}")
                        found_match = True
                        break
                    
                    # Test SHA1
                    test_hash = hashlib.sha1(combo.encode()).hexdigest()[:32]
                    if test_hash == signature:
                        print(f"   🎉 SHA1 MATCH found!")
                        print(f"      Combination #{j}: {combo[:50]}...")
                        print(f"      Pattern: {self.describe_combination(j)}")
                        found_match = True
                        break
                    
                    # Test SHA256
                    test_hash = hashlib.sha256(combo.encode()).hexdigest()[:32]
                    if test_hash == signature:
                        print(f"   🎉 SHA256 MATCH found!")
                        print(f"      Combination #{j}: {combo[:50]}...")
                        print(f"      Pattern: {self.describe_combination(j)}")
                        found_match = True
                        break
                        
                except Exception as e:
                    continue
            
            if not found_match:
                print(f"   ❌ No match found in {len(test_combinations)} combinations")
    
    def describe_combination(self, index):
        """Describe what combination pattern was used"""
        patterns = [
            "auth + nonce",
            "nonce + auth", 
            "auth[:32] + nonce",
            "nonce + auth[:32]",
            "auth[-32:] + nonce",
            "nonce + auth[-32:]",
            "auth + nonce + symbol",
            "nonce + symbol + auth",
            "auth + nonce + price",
            "nonce + price + auth",
            "auth + nonce + vol",
            "nonce + vol + auth",
            "auth + nonce + symbol + side + price + vol",
            "nonce + symbol + side + price + vol + auth",
            "URL encoded auth + nonce",
            "Base64 encoded auth + nonce"
        ]
        return patterns[index] if index < len(patterns) else f"Pattern #{index}"
    
    def test_hmac_patterns(self):
        """Test HMAC-based signatures"""
        
        print(f"\n🔑 HMAC PATTERN ANALYSIS")
        print("="*35)
        
        for i, data in enumerate(CAPTURED_SIGNATURES):
            print(f"\n🔍 Testing HMAC for signature #{i+1}")
            
            auth = data['auth']
            nonce = data['nonce']
            signature = data['signature']
            
            # Test HMAC with different keys and messages
            hmac_tests = [
                (auth, nonce),
                (auth[:32], nonce),
                (auth[-32:], nonce),
                (nonce, auth),
                (data['symbol'], f"{auth}{nonce}"),
                (auth, f"{nonce}{data['symbol']}{data['side']}{data['price']}{data['vol']}"),
            ]
            
            for key, message in hmac_tests:
                try:
                    # Test different HMAC algorithms
                    for hash_func, name in [(hashlib.md5, 'MD5'), (hashlib.sha1, 'SHA1'), (hashlib.sha256, 'SHA256')]:
                        test_sig = hmac.new(key.encode(), message.encode(), hash_func).hexdigest()[:32]
                        if test_sig == signature:
                            print(f"   🎉 HMAC-{name} MATCH!")
                            print(f"      Key: {key[:20]}...")
                            print(f"      Message: {message[:50]}...")
                            return True
                except:
                    continue
        
        print("   ❌ No HMAC matches found")
        return False
    
    def run_full_analysis(self):
        """Run complete signature analysis"""
        
        print("="*60)
        print("COMPREHENSIVE SIGNATURE ANALYSIS")
        print("="*60)
        
        self.analyze_signature_patterns()
        self.test_time_based_patterns()
        self.test_auth_based_patterns()
        self.test_combined_patterns()
        self.test_hmac_patterns()
        
        print("\n🎯 ANALYSIS COMPLETE")
        print("If no matches were found, the algorithm may involve:")
        print("- Custom encryption/encoding")
        print("- Additional secret keys")
        print("- Complex multi-step hashing")
        print("- Server-side generated components")

def main():
    """Main function"""
    
    inspector = SignatureInspector()
    inspector.run_full_analysis()

if __name__ == '__main__':
    main()
