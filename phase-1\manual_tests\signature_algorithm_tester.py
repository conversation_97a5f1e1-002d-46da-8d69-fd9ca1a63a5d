#!/usr/bin/env python3
"""
Signature Algorithm Tester
Test all discovered signature algorithms against MEXC API
"""

import json
import time
import hashlib
import hmac
import base64
import random
import string
from curl_cffi import requests
from dotenv import dotenv_values

class SignatureAlgorithmTester:
    """Test signature algorithms against MEXC API"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🧪 MEXC Signature Algorithm Tester")
        print("="*40)
    
    def get_market_price(self, symbol: str) -> float:
        """Get current market price"""
        
        headers = {'Accept': 'application/json', 'authorization': self.auth}
        
        try:
            r = self.session.get('https://futures.mexc.com/api/v1/contract/ticker',
                               params={'symbol': symbol}, headers=headers)
            
            if r.status_code == 200:
                data = r.json()
                if data.get('code') == 0:
                    ticker_data = data.get('data')
                    if isinstance(ticker_data, list) and ticker_data:
                        return float(ticker_data[0].get('lastPrice', 0))
                    elif isinstance(ticker_data, dict):
                        return float(ticker_data.get('lastPrice', 0))
        except Exception as e:
            print(f"❌ Market data error: {e}")
        
        return 0.0
    
    def generate_all_signature_algorithms(self, order_data, nonce):
        """Generate signatures using all discovered algorithms"""
        
        algorithms = {
            'v1_hmac_sha256': self._mexc_algorithm_v1,
            'v2_query_hmac': self._mexc_algorithm_v2,
            'v3_base64_double': self._mexc_algorithm_v3,
            'v4_timestamp_salt': self._mexc_algorithm_v4,
            'v5_xor_multi': self._mexc_algorithm_v5,
            'v6_nested_hmac': self._mexc_algorithm_v6,
            'v7_custom_encoding': self._mexc_algorithm_v7,
            'v8_trading_standard': self._mexc_algorithm_v8,
            'v9_binance_style': self._mexc_algorithm_v9,
            'v10_okx_style': self._mexc_algorithm_v10,
            'v11_bybit_style': self._mexc_algorithm_v11,
            'v12_mexc_specific': self._mexc_algorithm_v12
        }
        
        signatures = {}
        
        for name, algo in algorithms.items():
            try:
                signature = algo(order_data, nonce)
                signatures[name] = signature
                print(f"✅ {name}: {signature[:16]}...")
            except Exception as e:
                print(f"❌ {name}: {e}")
                signatures[name] = None
        
        return signatures
    
    def _mexc_algorithm_v1(self, order_data, nonce):
        """Standard HMAC-SHA256"""
        message = json.dumps(order_data, separators=(',', ':'), sort_keys=True) + nonce
        return hmac.new(self.auth.encode(), message.encode(), hashlib.sha256).hexdigest()
    
    def _mexc_algorithm_v2(self, order_data, nonce):
        """Query string + HMAC"""
        params = []
        for key in sorted(order_data.keys()):
            params.append(f"{key}={order_data[key]}")
        query_string = '&'.join(params)
        message = query_string + nonce
        return hmac.new(self.auth.encode(), message.encode(), hashlib.sha256).hexdigest()[:32]
    
    def _mexc_algorithm_v3(self, order_data, nonce):
        """Base64 + Double hash"""
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        message = base64.b64encode((nonce + json_str).encode()).decode()
        first_hash = hashlib.sha256((self.auth + message).encode()).hexdigest()
        return hashlib.md5(first_hash.encode()).hexdigest()
    
    def _mexc_algorithm_v4(self, order_data, nonce):
        """Timestamp-based with salt"""
        timestamp = str(int(time.time()))
        salt = "mexc_futures_trading_api"
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        message = f"{salt}{timestamp}{nonce}{json_str}{self.auth}"
        return hashlib.sha256(message.encode()).hexdigest()[:32]
    
    def _mexc_algorithm_v5(self, order_data, nonce):
        """Multi-step with XOR"""
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        base_content = self.auth + nonce + json_str
        base_hash = hashlib.sha256(base_content.encode()).digest()
        
        nonce_bytes = nonce.encode().ljust(32, b'0')[:32]
        xor_result = bytes(a ^ b for a, b in zip(base_hash, nonce_bytes))
        
        return hashlib.md5(xor_result).hexdigest()
    
    def _mexc_algorithm_v6(self, order_data, nonce):
        """Nested HMAC"""
        json_str = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        first_hmac = hmac.new(self.auth.encode(), json_str.encode(), hashlib.sha256).hexdigest()
        second_hmac = hmac.new(nonce.encode(), first_hmac.encode(), hashlib.sha256).hexdigest()
        return second_hmac[:32]
    
    def _mexc_algorithm_v7(self, order_data, nonce):
        """Custom encoding"""
        params = []
        for key in sorted(order_data.keys()):
            params.append(f"{key}={order_data[key]}")
        param_string = '&'.join(params)
        
        message = f"POST\n/api/v1/private/order/create\n{param_string}\n{nonce}"
        return hmac.new(self.auth.encode(), message.encode(), hashlib.sha256).hexdigest()[:32]
    
    def _mexc_algorithm_v8(self, order_data, nonce):
        """Trading platform standard"""
        timestamp = str(int(time.time() * 1000))
        
        sorted_params = []
        for key in sorted(order_data.keys()):
            sorted_params.append(f"{key}={order_data[key]}")
        
        canonical_request = f"POST\n/api/v1/private/order/create\n{'&'.join(sorted_params)}\ntimestamp={timestamp}&nonce={nonce}"
        signature = hmac.new(self.auth.encode(), canonical_request.encode(), hashlib.sha256).hexdigest()
        return signature[:32]
    
    def _mexc_algorithm_v9(self, order_data, nonce):
        """Binance-style signature"""
        # Binance uses query string + timestamp
        params = []
        for key in sorted(order_data.keys()):
            params.append(f"{key}={order_data[key]}")
        params.append(f"timestamp={int(time.time() * 1000)}")
        params.append(f"recvWindow=5000")
        
        query_string = '&'.join(params)
        return hmac.new(self.auth.encode(), query_string.encode(), hashlib.sha256).hexdigest()
    
    def _mexc_algorithm_v10(self, order_data, nonce):
        """OKX-style signature"""
        # OKX uses ISO timestamp + body + path
        timestamp = time.strftime('%Y-%m-%dT%H:%M:%S.%fZ', time.gmtime())[:-3] + 'Z'
        body = json.dumps(order_data, separators=(',', ':'), sort_keys=True)
        path = '/api/v1/private/order/create'
        
        message = timestamp + 'POST' + path + body
        return hmac.new(self.auth.encode(), message.encode(), hashlib.sha256).hexdigest()
    
    def _mexc_algorithm_v11(self, order_data, nonce):
        """Bybit-style signature"""
        # Bybit uses sorted params + timestamp
        timestamp = str(int(time.time() * 1000))
        params = order_data.copy()
        params['api_key'] = self.auth
        params['timestamp'] = timestamp
        
        sorted_params = []
        for key in sorted(params.keys()):
            sorted_params.append(f"{key}={params[key]}")
        
        param_str = '&'.join(sorted_params)
        return hmac.new(self.auth.encode(), param_str.encode(), hashlib.sha256).hexdigest()
    
    def _mexc_algorithm_v12(self, order_data, nonce):
        """MEXC-specific pattern (based on analysis)"""
        # Custom MEXC pattern with specific order
        auth_part = self.auth
        nonce_part = nonce
        
        # Create specific parameter order
        ordered_params = []
        param_order = ['symbol', 'side', 'type', 'vol', 'price', 'leverage', 'openType', 'marketCeiling', 'priceProtect']
        
        for param in param_order:
            if param in order_data:
                ordered_params.append(f"{param}={order_data[param]}")
        
        # Add any remaining parameters
        for key in sorted(order_data.keys()):
            if key not in param_order:
                ordered_params.append(f"{key}={order_data[key]}")
        
        param_string = '&'.join(ordered_params)
        
        # MEXC-specific signing
        message = f"{auth_part}{nonce_part}{param_string}"
        return hashlib.sha256(message.encode()).hexdigest()[:32]
    
    def test_signature_with_api(self, signature_name, signature, order_data, nonce):
        """Test a specific signature with the API"""
        
        print(f"\n🧪 Testing {signature_name}...")
        print(f"   Signature: {signature[:16]}...")
        
        # Prepare headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'authorization': self.auth,
            'x-mxc-nonce': nonce,
            'x-mxc-sign': signature,
            'x-language': 'en_US',
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        try:
            # Generate mhash
            mhash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
            url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={mhash}'
            
            r = self.session.post(url, json=order_data, headers=headers)
            
            if r.status_code == 200:
                result = r.json()
                
                if result.get('success') and result.get('code') == 0:
                    order_id = result.get('data', {}).get('orderId')
                    print(f"   🎉 SUCCESS! Order ID: {order_id}")
                    return {
                        'success': True,
                        'order_id': order_id,
                        'algorithm': signature_name,
                        'signature': signature
                    }
                else:
                    error_code = result.get('code')
                    error_msg = result.get('message', '')
                    
                    if error_code == 602:
                        print(f"   ❌ Signature verification failed")
                    elif error_code == 401:
                        print(f"   ❌ Authentication failed")
                    else:
                        print(f"   ❌ Error {error_code}: {error_msg}")
                    
                    return {'success': False, 'error_code': error_code, 'error_msg': error_msg}
            else:
                print(f"   ❌ HTTP {r.status_code}")
                return {'success': False, 'error': f'HTTP {r.status_code}'}
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return {'success': False, 'error': str(e)}
    
    def test_all_algorithms(self, symbol: str = 'BTC_USDT'):
        """Test all signature algorithms"""
        
        print("🚀 Testing All Signature Algorithms")
        print("="*45)
        
        # Get market price
        market_price = self.get_market_price(symbol)
        if market_price <= 0:
            print("❌ Could not get market price")
            return None
        
        # Calculate test price (70% below market)
        test_price = round(market_price * 0.3, 2)
        
        print(f"📊 Market price: ${market_price:,.2f}")
        print(f"🎯 Test price: ${test_price:,.2f}")
        
        # Prepare order data
        order_data = {
            'symbol': symbol,
            'side': 1,
            'openType': 1,
            'type': '2',
            'vol': 1,
            'leverage': 1,
            'marketCeiling': False,
            'price': str(test_price),
            'priceProtect': '0'
        }
        
        # Generate nonce
        nonce = str(int(time.time() * 1000))
        
        print(f"\n📋 Order data: {json.dumps(order_data, indent=2)}")
        print(f"🔢 Nonce: {nonce}")
        
        # Generate all signatures
        print(f"\n🔧 Generating signatures...")
        signatures = self.generate_all_signature_algorithms(order_data, nonce)
        
        # Test each signature
        print(f"\n🧪 Testing signatures with API...")
        
        successful_algorithms = []
        
        for name, signature in signatures.items():
            if signature:
                result = self.test_signature_with_api(name, signature, order_data, nonce)
                
                if result.get('success'):
                    successful_algorithms.append(result)
                    print(f"🎉 FOUND WORKING ALGORITHM: {name}")
                    break  # Stop at first success
        
        # Results
        print(f"\n" + "="*45)
        print("SIGNATURE TESTING RESULTS")
        print("="*45)
        
        if successful_algorithms:
            for algo in successful_algorithms:
                print(f"🎉 SUCCESS: {algo['algorithm']}")
                print(f"   Order ID: {algo['order_id']}")
                print(f"   Signature: {algo['signature'][:16]}...")
                
                # Try to cancel the order
                if algo['order_id']:
                    print(f"\n🔄 Canceling test order...")
                    self._cancel_order(str(algo['order_id']))
                
                return algo
        else:
            print("❌ NO WORKING ALGORITHMS FOUND")
            print("\n💡 NEXT STEPS:")
            print("1. All 12 algorithms tested")
            print("2. Need deeper reverse engineering")
            print("3. Consider browser automation as fallback")
            
            return None
    
    def _cancel_order(self, order_id: str):
        """Cancel test order"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'authorization': self.auth,
            'x-mxc-nonce': str(int(time.time() * 1000)),
        }
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        try:
            r = self.session.post('https://futures.mexc.com/api/v1/private/order/cancel',
                                json=[order_id], headers=headers)
            
            if r.status_code == 200:
                result = r.json()
                if result.get('success') and result.get('code') == 0:
                    print("✅ Test order canceled")
                else:
                    print("⚠️ Cancel failed, manual intervention may be needed")
            
        except Exception as e:
            print(f"⚠️ Cancel error: {e}")

def main():
    """Main testing function"""
    
    tester = SignatureAlgorithmTester()
    
    # Test all algorithms
    result = tester.test_all_algorithms('BTC_USDT')
    
    if result:
        print(f"\n🎉 SIGNATURE ALGORITHM CRACKED!")
        print(f"Working algorithm: {result['algorithm']}")
        print(f"🚀 Ready to implement in production trader!")
    else:
        print(f"\n🔧 Continue with deeper analysis or use browser automation")

if __name__ == '__main__':
    main()
