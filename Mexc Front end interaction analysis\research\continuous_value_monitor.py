#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Continuous Value Monitor
Implements continuous monitoring and restoration to prevent MEXC from clearing quantity values.

PROVEN ISSUE: MEXC clears quantity field values immediately after entry
SOLUTION: Continuous monitoring that restores values the moment they're cleared
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class ContinuousValueMonitor:
    """Continuous monitoring system to maintain quantity field values"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        # Setup logging without Unicode characters
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'continuous_monitor_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"CONTINUOUS VALUE MONITOR: {side} {quantity} {symbol}")
    
    def connect_to_mexc(self):
        """Connect to MEXC browser tab"""
        self.logger.info("Connecting to MEXC...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                url = page.url or ''
                if 'mexc.com' in url and 'testnet' not in url:
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("MEXC page not found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"Connected to MEXC: {self.page.url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Connection failed: {e}")
            return False
    
    def setup_continuous_monitoring(self):
        """Setup continuous monitoring system in the browser"""
        self.logger.info("Setting up continuous monitoring system...")
        
        monitoring_script = f"""
        () => {{
            console.log('Setting up continuous value monitoring...');
            
            const TARGET_VALUE = '{self.quantity}';
            const MONITOR_INTERVAL = 50; // Check every 50ms
            
            // Global monitoring state
            window.mexcMonitor = {{
                active: false,
                targetField: null,
                restorations: 0,
                lastRestoration: null,
                intervalId: null
            }};
            
            // Step 1: Find the quantity field (position 668, 603 from our test)
            function findQuantityField() {{
                const inputs = document.querySelectorAll('input.ant-input');
                
                for (const input of inputs) {{
                    const rect = input.getBoundingClientRect();
                    if (Math.abs(rect.x - 668) < 10 && Math.abs(rect.y - 603) < 50) {{
                        console.log(`Found quantity field at (${{rect.x}}, ${{rect.y}})`);
                        return input;
                    }}
                }}
                
                console.log('Quantity field not found by position, trying label search...');
                
                // Fallback: search by label
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {{
                    const text = element.textContent || '';
                    if (text.includes('Quantity') && text.includes('USDT')) {{
                        let parent = element.parentElement;
                        let depth = 0;
                        
                        while (parent && depth < 5) {{
                            const inputs = parent.querySelectorAll('input.ant-input');
                            for (const input of inputs) {{
                                const rect = input.getBoundingClientRect();
                                if (rect.width > 0 && rect.height > 0) {{
                                    console.log(`Found quantity field by label at (${{rect.x}}, ${{rect.y}})`);
                                    return input;
                                }}
                            }}
                            parent = parent.parentElement;
                            depth++;
                        }}
                    }}
                }}
                
                return null;
            }}
            
            // Step 2: Value restoration function
            function restoreValue(input) {{
                try {{
                    // Multiple restoration methods
                    input.focus();
                    input.value = TARGET_VALUE;
                    input.setAttribute('value', TARGET_VALUE);
                    
                    // React/Vue override
                    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    nativeInputValueSetter.call(input, TARGET_VALUE);
                    
                    // Trigger events
                    input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                    input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                    
                    window.mexcMonitor.restorations++;
                    window.mexcMonitor.lastRestoration = Date.now();
                    
                    console.log(`Value restored: "${{input.value}}" (restoration #${{window.mexcMonitor.restorations}})`);
                    
                }} catch (error) {{
                    console.log(`Restoration error: ${{error.message}}`);
                }}
            }}
            
            // Step 3: Monitoring function
            function monitorField() {{
                if (!window.mexcMonitor.active || !window.mexcMonitor.targetField) {{
                    return;
                }}
                
                const input = window.mexcMonitor.targetField;
                const currentValue = input.value;
                
                // Check if field still exists and is visible
                const rect = input.getBoundingClientRect();
                if (rect.width === 0 || rect.height === 0) {{
                    console.log('Field no longer visible, searching for new field...');
                    window.mexcMonitor.targetField = findQuantityField();
                    return;
                }}
                
                // Restore value if it's been cleared
                if (currentValue !== TARGET_VALUE) {{
                    console.log(`Value cleared! Current: "${{currentValue}}", Expected: "${{TARGET_VALUE}}"`);
                    restoreValue(input);
                }}
            }}
            
            // Step 4: Initialize monitoring
            const quantityField = findQuantityField();
            if (!quantityField) {{
                return {{
                    success: false,
                    error: 'Quantity field not found'
                }};
            }}
            
            window.mexcMonitor.targetField = quantityField;
            window.mexcMonitor.active = true;
            
            // Initial value setting
            restoreValue(quantityField);
            
            // Start continuous monitoring
            window.mexcMonitor.intervalId = setInterval(monitorField, MONITOR_INTERVAL);
            
            console.log(`Continuous monitoring started - checking every ${{MONITOR_INTERVAL}}ms`);
            
            return {{
                success: true,
                field_position: {{
                    x: Math.round(quantityField.getBoundingClientRect().x),
                    y: Math.round(quantityField.getBoundingClientRect().y)
                }},
                monitor_interval: MONITOR_INTERVAL,
                initial_value: quantityField.value
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(monitoring_script)
            
            if result.get('success'):
                field_position = result.get('field_position', {})
                monitor_interval = result.get('monitor_interval', 0)
                initial_value = result.get('initial_value', '')
                
                self.logger.info("CONTINUOUS MONITORING SETUP SUCCESS:")
                self.logger.info(f"   Field position: {field_position}")
                self.logger.info(f"   Monitor interval: {monitor_interval}ms")
                self.logger.info(f"   Initial value: '{initial_value}'")
                
                return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Monitoring setup failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Monitoring setup error: {e}")
            return False
    
    def monitor_status(self, duration_seconds=30):
        """Monitor the monitoring system status"""
        self.logger.info(f"Monitoring status for {duration_seconds} seconds...")
        
        status_script = """
        () => {
            if (!window.mexcMonitor) {
                return { error: 'Monitor not initialized' };
            }
            
            const monitor = window.mexcMonitor;
            const field = monitor.targetField;
            
            if (!field) {
                return { error: 'Target field lost' };
            }
            
            return {
                success: true,
                active: monitor.active,
                restorations: monitor.restorations,
                last_restoration: monitor.lastRestoration,
                current_value: field.value,
                field_visible: field.getBoundingClientRect().width > 0,
                time_since_last_restoration: monitor.lastRestoration ? Date.now() - monitor.lastRestoration : null
            };
        }
        """
        
        start_time = time.time()
        check_interval = 2  # Check every 2 seconds
        
        while time.time() - start_time < duration_seconds:
            try:
                status = self.page.evaluate(status_script)
                
                if status.get('success'):
                    restorations = status.get('restorations', 0)
                    current_value = status.get('current_value', '')
                    field_visible = status.get('field_visible', False)
                    time_since_last = status.get('time_since_last_restoration')
                    
                    elapsed = int(time.time() - start_time)
                    self.logger.info(f"[{elapsed}s] Restorations: {restorations}, Value: '{current_value}', Visible: {field_visible}")
                    
                    if time_since_last:
                        self.logger.info(f"       Last restoration: {time_since_last}ms ago")
                else:
                    error = status.get('error', 'Unknown error')
                    self.logger.error(f"Monitor status error: {error}")
                    return False
                
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.error(f"Status check error: {e}")
                return False
        
        return True
    
    def click_trade_button_with_monitoring(self):
        """Click trade button while monitoring maintains the value"""
        self.logger.info("Clicking trade button with active monitoring...")
        
        # Determine button class
        if self.side == "BUY":
            button_class = "component_longBtn__eazYU"
        else:
            button_class = "component_shortBtn__x5P3I"
        
        click_script = f"""
        () => {{
            console.log('Clicking trade button with monitoring active...');
            
            // Verify monitoring is still active
            if (!window.mexcMonitor || !window.mexcMonitor.active) {{
                return {{ success: false, error: 'Monitoring not active' }};
            }}
            
            // Verify quantity field still has value
            const field = window.mexcMonitor.targetField;
            if (!field || field.value !== '{self.quantity}') {{
                return {{ 
                    success: false, 
                    error: `Quantity field invalid: "${{field ? field.value : 'null'}}"` 
                }};
            }}
            
            // Find and click button
            const button = document.querySelector('button.{button_class}');
            if (!button) {{
                return {{ success: false, error: 'Trade button not found' }};
            }}
            
            // Record state before click
            const beforeModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
            const beforeNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
            
            try {{
                button.focus();
                button.click();
                
                console.log('Trade button clicked');
                
                // Check for immediate responses
                setTimeout(() => {{
                    const afterModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
                    const afterNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
                    
                    const modalChange = afterModals - beforeModals;
                    const notificationChange = afterNotifications - beforeNotifications;
                    
                    console.log(`UI changes: ${{modalChange}} modals, ${{notificationChange}} notifications`);
                    
                    // Check for error messages
                    const errorElements = document.querySelectorAll('.ant-message-error, .error, [class*="error"]');
                    const errors = [];
                    for (const errorEl of errorElements) {{
                        const errorText = errorEl.textContent || '';
                        if (errorText.toLowerCase().includes('quantity') || 
                            errorText.toLowerCase().includes('amount') || 
                            errorText.toLowerCase().includes('enter')) {{
                            errors.push(errorText);
                        }}
                    }}
                    
                    console.log(`Errors found: ${{errors.length}}`);
                    errors.forEach(error => console.log(`Error: ${{error}}`));
                    
                }}, 1000);
                
                return {{
                    success: true,
                    button_text: button.textContent,
                    quantity_at_click: field.value,
                    restorations_before_click: window.mexcMonitor.restorations
                }};
                
            }} catch (error) {{
                return {{ success: false, error: error.message }};
            }}
        }}
        """
        
        try:
            result = self.page.evaluate(click_script)
            
            if result.get('success'):
                button_text = result.get('button_text', '')
                quantity_at_click = result.get('quantity_at_click', '')
                restorations = result.get('restorations_before_click', 0)
                
                self.logger.info("TRADE BUTTON CLICK SUCCESS:")
                self.logger.info(f"   Button: '{button_text}'")
                self.logger.info(f"   Quantity at click: '{quantity_at_click}'")
                self.logger.info(f"   Restorations before click: {restorations}")
                
                # Wait for response
                time.sleep(3)
                
                return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Trade button click failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Trade button click error: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop the continuous monitoring"""
        self.logger.info("Stopping continuous monitoring...")
        
        stop_script = """
        () => {
            if (window.mexcMonitor && window.mexcMonitor.intervalId) {
                clearInterval(window.mexcMonitor.intervalId);
                window.mexcMonitor.active = false;
                
                return {
                    success: true,
                    total_restorations: window.mexcMonitor.restorations,
                    final_value: window.mexcMonitor.targetField ? window.mexcMonitor.targetField.value : 'unknown'
                };
            }
            
            return { success: false, error: 'Monitor not found' };
        }
        """
        
        try:
            result = self.page.evaluate(stop_script)
            
            if result.get('success'):
                total_restorations = result.get('total_restorations', 0)
                final_value = result.get('final_value', '')
                
                self.logger.info("MONITORING STOPPED:")
                self.logger.info(f"   Total restorations: {total_restorations}")
                self.logger.info(f"   Final field value: '{final_value}'")
                
                return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Stop monitoring failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Stop monitoring error: {e}")
            return False
    
    def execute_monitored_trade(self):
        """Execute complete trade with continuous monitoring"""
        self.logger.info("EXECUTING MONITORED TRADE")
        self.logger.info("="*50)
        
        try:
            # Step 1: Connect
            if not self.connect_to_mexc():
                return False
            
            # Step 2: Setup continuous monitoring
            if not self.setup_continuous_monitoring():
                return False
            
            # Step 3: Monitor for 10 seconds to verify it's working
            self.logger.info("Testing monitoring system...")
            if not self.monitor_status(10):
                return False
            
            # Step 4: Click trade button with monitoring active
            if not self.click_trade_button_with_monitoring():
                return False
            
            # Step 5: Continue monitoring for 5 more seconds after click
            self.logger.info("Monitoring post-click response...")
            self.monitor_status(5)
            
            # Step 6: Stop monitoring
            self.stop_monitoring()
            
            self.logger.info("="*50)
            self.logger.info("MONITORED TRADE EXECUTION COMPLETED")
            return True
            
        except Exception as e:
            self.logger.error(f"Monitored trade error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Continuous Value Monitor")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
CONTINUOUS VALUE MONITOR
========================
SOLUTION: Continuous monitoring that restores values when MEXC clears them

APPROACH:
1. Find quantity field (position 668, 603)
2. Setup continuous monitoring (every 50ms)
3. Restore value immediately when cleared
4. Maintain value during trade execution
5. Click trade button with value guaranteed

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    monitor = ContinuousValueMonitor(args.symbol, args.side, args.quantity)
    
    try:
        success = monitor.execute_monitored_trade()
        
        print("\n" + "="*50)
        if success:
            print("SUCCESS: Monitored trade execution completed")
            print("Check logs for detailed monitoring data")
        else:
            print("FAILED: Monitored trade execution failed")
            print("Check logs for error details")
        print("="*50)
        
    except KeyboardInterrupt:
        print("\nMonitoring interrupted")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
    finally:
        monitor.cleanup()

if __name__ == "__main__":
    main()
