#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Targeted Button Click System
Based on analysis: <PERSON><PERSON> has CLICK and TOUCH event handlers, but NO mouse event handlers.

ANALYSIS RESULTS:
✅ <PERSON><PERSON> found: "Open Long" at position (659, 792)
✅ Button enabled: Not disabled
✅ Event handlers: click=True, touchstart=True, touchend=True
❌ Mouse handlers: mousedown=False, mouseup=False (This is why standard clicks fail!)

SOLUTION: Use touch events and direct click events that the button actually responds to.
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, Optional
from dataclasses import dataclass
from playwright.sync_api import sync_playwright

@dataclass
class TradeConfig:
    symbol: str = "TRU_USDT"
    side: str = "BUY"  # BUY or SELL
    quantity: float = 10.0
    execute_real_trade: bool = False

class MEXCTargetedButtonClick:
    """Targeted button click using the exact event types MEXC responds to"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.page = None
        
        # Interaction tracking
        self.screenshot_counter = 0
        
        self.logger.info(f"🎯 Targeted button click initialized: {config}")
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot for verification"""
        self.screenshot_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"targeted_{self.screenshot_counter:03d}_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"📸 {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return ""
    
    def connect_to_browser(self) -> bool:
        """Connect to browser"""
        self.logger.info("🔌 Connecting to browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("No MEXC page found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"✅ Connected to MEXC page: {self.page.url}")
            
            # Take initial screenshot
            self.take_screenshot("connected", "Connected to MEXC")
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def execute_targeted_button_click(self) -> bool:
        """Execute button click using the exact event types that work"""
        self.logger.info(f"🎯 Targeted {self.config.side} button click using TOUCH and CLICK events")
        
        # Take before screenshot
        before_screenshot = self.take_screenshot("before_targeted_click", f"Before targeted {self.config.side} click")
        
        # Determine button class
        if self.config.side == "BUY":
            button_class = "component_longBtn__eazYU"
            expected_text = "Open Long"
        else:
            button_class = "component_shortBtn__x5P3I"
            expected_text = "Open Short"
        
        # Targeted click script using only the event types that work
        click_script = f"""
        () => {{
            console.log('🎯 Starting TARGETED button click...');
            console.log('Using TOUCH and CLICK events (the ones that actually work)');
            
            const button = document.querySelector('button.{button_class}');
            
            if (!button) {{
                return {{ success: false, error: 'Button not found' }};
            }}
            
            console.log(`✅ Found button: "${{button.textContent}}" at (${{Math.round(button.getBoundingClientRect().x)}}, ${{Math.round(button.getBoundingClientRect().y)}})`);
            
            if (!{str(self.config.execute_real_trade).lower()}) {{
                console.log('🟡 SAFETY MODE: Button found and ready for targeted click');
                return {{
                    success: true,
                    mode: 'safety',
                    text: button.textContent,
                    position: {{
                        x: Math.round(button.getBoundingClientRect().x),
                        y: Math.round(button.getBoundingClientRect().y)
                    }}
                }};
            }}
            
            console.log('🔴 LIVE MODE: Executing TARGETED click with working event types...');
            
            // Strategy: Use only the event types that the button actually responds to
            let results = [];
            
            try {{
                // Step 1: Focus the button
                button.focus();
                results.push({{ step: 'focus', result: 'success' }});
                
                // Step 2: Touch events (button has touchstart=True, touchend=True)
                console.log('🤏 Executing touch events...');
                
                const touchStartEvent = new TouchEvent('touchstart', {{
                    bubbles: true,
                    cancelable: true,
                    touches: [{{
                        identifier: 0,
                        target: button,
                        clientX: button.getBoundingClientRect().x + button.getBoundingClientRect().width / 2,
                        clientY: button.getBoundingClientRect().y + button.getBoundingClientRect().height / 2
                    }}]
                }});
                
                const touchEndEvent = new TouchEvent('touchend', {{
                    bubbles: true,
                    cancelable: true,
                    changedTouches: [{{
                        identifier: 0,
                        target: button,
                        clientX: button.getBoundingClientRect().x + button.getBoundingClientRect().width / 2,
                        clientY: button.getBoundingClientRect().y + button.getBoundingClientRect().height / 2
                    }}]
                }});
                
                button.dispatchEvent(touchStartEvent);
                button.dispatchEvent(touchEndEvent);
                results.push({{ step: 'touch_events', result: 'dispatched' }});
                
                // Step 3: Direct click event (button has click=True)
                console.log('👆 Executing click event...');
                
                const clickEvent = new MouseEvent('click', {{
                    bubbles: true,
                    cancelable: true,
                    view: window,
                    button: 0,
                    buttons: 1,
                    clientX: button.getBoundingClientRect().x + button.getBoundingClientRect().width / 2,
                    clientY: button.getBoundingClientRect().y + button.getBoundingClientRect().height / 2
                }});
                
                button.dispatchEvent(clickEvent);
                results.push({{ step: 'click_event', result: 'dispatched' }});
                
                // Step 4: Native click as backup
                console.log('🖱️ Executing native click...');
                button.click();
                results.push({{ step: 'native_click', result: 'executed' }});
                
                // Step 5: Check for immediate UI response
                setTimeout(() => {{
                    const modal = document.querySelector('.ant-modal:not([style*="display: none"])');
                    const notification = document.querySelector('.ant-notification, .ant-message');
                    
                    if (modal) {{
                        results.push({{ step: 'ui_response', result: 'modal_appeared' }});
                    }} else if (notification) {{
                        results.push({{ step: 'ui_response', result: 'notification_appeared' }});
                    }} else {{
                        results.push({{ step: 'ui_response', result: 'no_immediate_response' }});
                    }}
                }}, 1000);
                
                console.log('✅ All targeted click methods executed');
                
                return {{
                    success: true,
                    mode: 'live',
                    text: button.textContent,
                    results: results,
                    position: {{
                        x: Math.round(button.getBoundingClientRect().x),
                        y: Math.round(button.getBoundingClientRect().y)
                    }}
                }};
                
            }} catch (error) {{
                console.log(`❌ Targeted click failed: ${{error.message}}`);
                return {{
                    success: false,
                    error: error.message,
                    results: results
                }};
            }}
        }}
        """
        
        try:
            result = self.page.evaluate(click_script)
            
            # Wait for UI changes
            time.sleep(5)
            
            # Take after screenshot
            after_screenshot = self.take_screenshot("after_targeted_click", f"After targeted {self.config.side} click")
            
            if result.get('success'):
                mode = result.get('mode')
                text = result.get('text', '')
                position = result.get('position', {})
                
                if mode == 'safety':
                    self.logger.info(f"✅ TARGETED {self.config.side} button ready!")
                    self.logger.info(f"   Text: '{text}'")
                    self.logger.info(f"   Position: {position}")
                    self.logger.info("🟡 SAFETY MODE: Ready for targeted live execution")
                    return True
                else:
                    results = result.get('results', [])
                    
                    self.logger.info(f"🔴 TARGETED {self.config.side} button click executed!")
                    self.logger.info(f"   Text: '{text}'")
                    self.logger.info(f"   Position: {position}")
                    self.logger.info(f"   Execution steps:")
                    
                    for step_result in results:
                        step = step_result.get('step', 'unknown')
                        result_type = step_result.get('result', 'unknown')
                        self.logger.info(f"     ✅ {step}: {result_type}")
                    
                    # Check for UI changes
                    self.check_for_ui_changes()
                    
                    return True
            else:
                error = result.get('error', 'Unknown error')
                results = result.get('results', [])
                self.logger.error(f"❌ Targeted button click failed: {error}")
                
                if results:
                    self.logger.info("Partial results:")
                    for step_result in results:
                        step = step_result.get('step', 'unknown')
                        result_type = step_result.get('result', 'unknown')
                        self.logger.info(f"     - {step}: {result_type}")
                
        except Exception as e:
            self.logger.error(f"❌ Targeted click script failed: {e}")
        
        self.take_screenshot("targeted_click_failed", f"Targeted {self.config.side} click failed")
        return False

    def check_for_ui_changes(self):
        """Check for UI changes after button click"""
        self.logger.info("🔍 Checking for UI changes...")

        ui_check_script = """
        () => {
            const changes = [];

            // Check for modals
            const modals = document.querySelectorAll('.ant-modal:not([style*="display: none"]), .modal:not([style*="display: none"])');
            if (modals.length > 0) {
                changes.push({
                    type: 'modal',
                    count: modals.length,
                    content: Array.from(modals).map(m => m.textContent?.substring(0, 100) || '').join('; ')
                });
            }

            // Check for notifications
            const notifications = document.querySelectorAll('.ant-notification, .ant-message');
            if (notifications.length > 0) {
                changes.push({
                    type: 'notification',
                    count: notifications.length,
                    content: Array.from(notifications).map(n => n.textContent?.substring(0, 100) || '').join('; ')
                });
            }

            return changes;
        }
        """

        try:
            ui_changes = self.page.evaluate(ui_check_script)

            if ui_changes:
                self.logger.info("📊 UI CHANGES DETECTED:")
                for change in ui_changes:
                    change_type = change.get('type', 'unknown')
                    count = change.get('count', 0)
                    content = change.get('content', '')

                    self.logger.info(f"   {change_type.upper()}: {count} elements")
                    if content:
                        self.logger.info(f"   Content: {content[:100]}...")
            else:
                self.logger.info("ℹ️ No UI changes detected")

        except Exception as e:
            self.logger.error(f"UI change check failed: {e}")

    def execute_targeted_test(self) -> Dict[str, Any]:
        """Execute targeted button click test"""
        self.logger.info("🎯 Starting targeted button click test")

        result = {
            "success": False,
            "steps_completed": [],
            "errors": [],
            "total_duration": 0
        }

        start_time = time.time()

        try:
            # Step 1: Connect to browser
            self.logger.info("📋 Step 1: Browser connection")
            if not self.connect_to_browser():
                result["errors"].append("Browser connection failed")
                return result
            result["steps_completed"].append("browser_connected")

            # Step 2: Execute targeted button click
            self.logger.info("📋 Step 2: Targeted button click")
            if not self.execute_targeted_button_click():
                result["errors"].append("Targeted button click failed")
                return result
            result["steps_completed"].append("targeted_button_click_executed")

            # Success!
            result["success"] = True
            self.logger.info("✅ Targeted button click test completed!")

        except Exception as e:
            self.logger.error(f"Test exception: {e}")
            result["errors"].append(str(e))

        finally:
            result["total_duration"] = time.time() - start_time

        return result

    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="MEXC Targeted Button Click System")

    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=10.0, help="Order quantity")
    parser.add_argument("--execute", action="store_true", help="🔴 EXECUTE REAL BUTTON CLICK")
    parser.add_argument("--confirm", action="store_true", help="Confirm real button execution")

    args = parser.parse_args()

    if args.execute and not args.confirm:
        print("❌ ERROR: For live button clicks, use both --execute AND --confirm flags")
        return

    config = TradeConfig(
        symbol=args.symbol,
        side=args.side,
        quantity=args.quantity,
        execute_real_trade=args.execute
    )

    print(f"""
🎯 MEXC Targeted Button Click System
====================================

BASED ON ANALYSIS RESULTS:
✅ Button: "{"Open Long" if config.side == "BUY" else "Open Short"}" at position (659, 792)
✅ Event handlers: click=True, touchstart=True, touchend=True
❌ Mouse handlers: mousedown=False, mouseup=False

TARGETED SOLUTION:
🤏 Touch events (touchstart, touchend) - Button responds to these
👆 Click events (click) - Button responds to these
🖱️ Native click - Backup method
❌ Skip mouse events - Button doesn't respond to these

Target Configuration:
  Symbol: {config.symbol}
  Side: {config.side} (Button: {"Open Long" if config.side == "BUY" else "Open Short"})
  Quantity: {config.quantity}

Execution Mode: {'🔴 LIVE TARGETED CLICKS' if args.execute else '🟡 SAFE MODE'}
    """)

    if args.execute:
        print("⚠️  WARNING: LIVE TARGETED BUTTON CLICK MODE")
        print("⚠️  This will use the exact event types the button responds to")
        print("⚠️  Based on analysis: TOUCH and CLICK events")

        confirmation = input("\nType 'EXECUTE' to proceed with targeted button click: ")
        if confirmation != 'EXECUTE':
            print("❌ Targeted button click cancelled")
            return

    print("\nStarting targeted button click system...")

    # Initialize targeted click system
    click_system = MEXCTargetedButtonClick(config)

    try:
        result = click_system.execute_targeted_test()

        print(f"""
📊 Targeted Click Results:
=========================
Success: {'✅' if result['success'] else '❌'}
Duration: {result['total_duration']:.2f}s
Steps: {', '.join(result['steps_completed'])}
        """)

        if result['errors']:
            print(f"Errors: {', '.join(result['errors'])}")

        if result['success']:
            if args.execute:
                print("🎉 TARGETED BUTTON CLICK EXECUTED!")
                print("🎯 Used exact event types the button responds to")
                print("🔍 Check screenshots for results")
            else:
                print("✅ TARGETED CLICK SYSTEM READY!")
                print("🎯 Will use TOUCH and CLICK events (the working ones)")
        else:
            print("❌ Targeted click failed")

    except KeyboardInterrupt:
        print("\n👋 Interrupted")
    finally:
        click_system.cleanup()

if __name__ == "__main__":
    main()
