"""
Database models and initialization for MEXC Trading System
SQLAlchemy models for trades, sessions, and system data
"""

import asyncio
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from enum import Enum

from sqlalchemy import (
    create_engine, Column, Integer, String, Float, DateTime, 
    Boolean, Text, JSON, ForeignKey, Index
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker

from src.config import settings
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Database base
Base = declarative_base()

# Database engines and sessions
engine = None
async_engine = None
SessionLocal = None
AsyncSessionLocal = None


class TradeStatus(str, Enum):
    """Trade execution status"""
    PENDING = "pending"
    EXECUTED = "executed"
    FAILED = "failed"
    CANCELLED = "cancelled"
    PARTIAL = "partial"


class TradeAction(str, Enum):
    """Trade action types"""
    BUY = "buy"
    SELL = "sell"
    CLOSE = "close"


class TradeSide(str, Enum):
    """Trade side"""
    LONG = "long"
    SHORT = "short"


class SessionStatus(str, Enum):
    """Session status"""
    ACTIVE = "active"
    EXPIRED = "expired"
    INVALID = "invalid"
    REFRESHING = "refreshing"


class Trade(Base):
    """Trade execution record"""
    __tablename__ = "trades"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Trade identification
    order_id = Column(String(50), unique=True, index=True)
    webhook_id = Column(String(100), index=True)
    
    # Trade details
    symbol = Column(String(20), nullable=False, index=True)
    action = Column(String(10), nullable=False)  # buy, sell, close
    side = Column(String(10), nullable=False)    # long, short
    quantity = Column(Float, nullable=False)
    price = Column(Float)
    leverage = Column(Integer, default=1)
    
    # Execution details
    status = Column(String(20), nullable=False, default=TradeStatus.PENDING)
    executed_price = Column(Float)
    executed_quantity = Column(Float)
    execution_time = Column(DateTime(timezone=True))
    
    # Risk management
    stop_loss = Column(Float)
    take_profit = Column(Float)
    
    # Financial tracking
    pnl = Column(Float)
    fees = Column(Float)
    
    # Session and system info
    session_id = Column(String(50), ForeignKey("sessions.id"))
    browser_instance = Column(String(50))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), default=datetime.now(timezone.utc))
    updated_at = Column(DateTime(timezone=True), default=datetime.now(timezone.utc), onupdate=datetime.now(timezone.utc))
    
    # Additional data
    webhook_data = Column(JSON)
    execution_data = Column(JSON)
    error_message = Column(Text)
    
    # Relationships
    session = relationship("Session", back_populates="trades")
    
    # Indexes
    __table_args__ = (
        Index('idx_trades_symbol_created', 'symbol', 'created_at'),
        Index('idx_trades_status_created', 'status', 'created_at'),
        Index('idx_trades_session_created', 'session_id', 'created_at'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert trade to dictionary"""
        return {
            "id": self.id,
            "order_id": self.order_id,
            "webhook_id": self.webhook_id,
            "symbol": self.symbol,
            "action": self.action,
            "side": self.side,
            "quantity": self.quantity,
            "price": self.price,
            "leverage": self.leverage,
            "status": self.status,
            "executed_price": self.executed_price,
            "executed_quantity": self.executed_quantity,
            "execution_time": self.execution_time.isoformat() if self.execution_time else None,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            "pnl": self.pnl,
            "fees": self.fees,
            "session_id": self.session_id,
            "browser_instance": self.browser_instance,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "error_message": self.error_message
        }


class Session(Base):
    """Browser session record"""
    __tablename__ = "sessions"
    
    id = Column(String(50), primary_key=True, index=True)
    
    # Session details
    status = Column(String(20), nullable=False, default=SessionStatus.ACTIVE)
    browser_instance = Column(String(50))
    user_agent = Column(String(500))
    
    # Timing
    created_at = Column(DateTime(timezone=True), default=datetime.now(timezone.utc))
    last_used_at = Column(DateTime(timezone=True), default=datetime.now(timezone.utc))
    expires_at = Column(DateTime(timezone=True))
    last_health_check = Column(DateTime(timezone=True))
    
    # Health metrics
    health_score = Column(Float, default=1.0)
    success_rate = Column(Float, default=1.0)
    total_trades = Column(Integer, default=0)
    successful_trades = Column(Integer, default=0)
    failed_trades = Column(Integer, default=0)
    
    # Session data (encrypted)
    cookies_data = Column(Text)  # Encrypted cookie data
    storage_data = Column(Text)  # Encrypted localStorage data
    
    # Metadata
    updated_at = Column(DateTime(timezone=True), default=datetime.now(timezone.utc), onupdate=datetime.now(timezone.utc))
    session_metadata = Column(JSON)
    
    # Relationships
    trades = relationship("Trade", back_populates="session")
    
    # Indexes
    __table_args__ = (
        Index('idx_sessions_status_expires', 'status', 'expires_at'),
        Index('idx_sessions_health_check', 'last_health_check'),
    )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary"""
        return {
            "id": self.id,
            "status": self.status,
            "browser_instance": self.browser_instance,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "last_used_at": self.last_used_at.isoformat() if self.last_used_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None,
            "health_score": self.health_score,
            "success_rate": self.success_rate,
            "total_trades": self.total_trades,
            "successful_trades": self.successful_trades,
            "failed_trades": self.failed_trades
        }


class SystemMetric(Base):
    """System performance metrics"""
    __tablename__ = "system_metrics"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Metric details
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Float, nullable=False)
    metric_unit = Column(String(20))
    
    # Context
    component = Column(String(50))  # session_manager, trading_engine, etc.
    instance_id = Column(String(50))
    
    # Timing
    timestamp = Column(DateTime(timezone=True), default=datetime.now(timezone.utc), index=True)
    
    # Additional data
    metric_metadata = Column(JSON)
    
    # Indexes
    __table_args__ = (
        Index('idx_metrics_name_timestamp', 'metric_name', 'timestamp'),
        Index('idx_metrics_component_timestamp', 'component', 'timestamp'),
    )


class SystemEvent(Base):
    """System events and alerts"""
    __tablename__ = "system_events"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Event details
    event_type = Column(String(50), nullable=False, index=True)
    event_level = Column(String(20), nullable=False)  # info, warning, error, critical
    title = Column(String(200), nullable=False)
    description = Column(Text)
    
    # Context
    component = Column(String(50))
    session_id = Column(String(50))
    trade_id = Column(Integer)
    
    # Status
    acknowledged = Column(Boolean, default=False)
    resolved = Column(Boolean, default=False)
    
    # Timing
    timestamp = Column(DateTime(timezone=True), default=datetime.now(timezone.utc), index=True)
    acknowledged_at = Column(DateTime(timezone=True))
    resolved_at = Column(DateTime(timezone=True))
    
    # Additional data
    event_metadata = Column(JSON)
    
    # Indexes
    __table_args__ = (
        Index('idx_events_type_timestamp', 'event_type', 'timestamp'),
        Index('idx_events_level_timestamp', 'event_level', 'timestamp'),
        Index('idx_events_unresolved', 'resolved', 'timestamp'),
    )


async def init_database():
    """Initialize database connection and create tables"""
    global engine, async_engine, SessionLocal, AsyncSessionLocal
    
    try:
        # Create database directory if using SQLite
        db_url = settings.get_database_url()
        
        # Create synchronous engine
        engine = create_engine(
            db_url,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            echo=settings.DEBUG_MODE
        )
        
        # Create async engine for async operations
        if db_url.startswith("sqlite"):
            async_db_url = db_url.replace("sqlite:///", "sqlite+aiosqlite:///")
        else:
            async_db_url = db_url.replace("postgresql://", "postgresql+asyncpg://")
        
        async_engine = create_async_engine(
            async_db_url,
            pool_size=settings.DATABASE_POOL_SIZE,
            max_overflow=settings.DATABASE_MAX_OVERFLOW,
            echo=settings.DEBUG_MODE
        )
        
        # Create session makers
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        AsyncSessionLocal = async_sessionmaker(
            async_engine, class_=AsyncSession, expire_on_commit=False
        )
        
        # Create tables
        Base.metadata.create_all(bind=engine)
        
        logger.info(
            "Database initialized successfully",
            database_url=db_url,
            tables_created=len(Base.metadata.tables)
        )
        
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


def get_db():
    """Get database session (synchronous)"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def get_async_db():
    """Get async database session"""
    async with AsyncSessionLocal() as session:
        yield session


async def create_trade(trade_data: Dict[str, Any]) -> Trade:
    """Create a new trade record"""
    async with AsyncSessionLocal() as session:
        trade = Trade(**trade_data)
        session.add(trade)
        await session.commit()
        await session.refresh(trade)
        return trade


async def update_trade(trade_id: int, update_data: Dict[str, Any]) -> Optional[Trade]:
    """Update a trade record"""
    async with AsyncSessionLocal() as session:
        trade = await session.get(Trade, trade_id)
        if trade:
            for key, value in update_data.items():
                setattr(trade, key, value)
            trade.updated_at = datetime.now(timezone.utc)
            await session.commit()
            await session.refresh(trade)
        return trade


async def get_recent_trades(limit: int = 50) -> List[Trade]:
    """Get recent trades"""
    async with AsyncSessionLocal() as session:
        result = await session.execute(
            Trade.__table__.select()
            .order_by(Trade.created_at.desc())
            .limit(limit)
        )
        return result.fetchall()


async def create_session_record(session_data: Dict[str, Any]) -> Session:
    """Create a new session record"""
    async with AsyncSessionLocal() as session:
        session_record = Session(**session_data)
        session.add(session_record)
        await session.commit()
        await session.refresh(session_record)
        return session_record


async def log_system_event(
    event_type: str,
    event_level: str,
    title: str,
    description: str = None,
    **kwargs
):
    """Log a system event"""
    async with AsyncSessionLocal() as session:
        event = SystemEvent(
            event_type=event_type,
            event_level=event_level,
            title=title,
            description=description,
            **kwargs
        )
        session.add(event)
        await session.commit()


async def record_metric(
    metric_name: str,
    metric_value: float,
    component: str = None,
    metric_unit: str = None,
    **kwargs
):
    """Record a system metric"""
    async with AsyncSessionLocal() as session:
        metric = SystemMetric(
            metric_name=metric_name,
            metric_value=metric_value,
            component=component,
            metric_unit=metric_unit,
            metric_metadata=kwargs
        )
        session.add(metric)
        await session.commit()
