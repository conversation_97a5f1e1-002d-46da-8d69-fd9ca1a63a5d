#!/usr/bin/env python3
"""
Option C: DevTools Protocol Network Capture
Uses Chrome DevTools Protocol to capture real network requests and signatures
"""

import json
import time
import asyncio
from playwright.async_api import async_playwright
from curl_cffi import requests
from dotenv import dotenv_values

class DevToolsNetworkCapture:
    """DevTools Protocol based network capture for real signature extraction"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        self.captured_requests = []
        
        print("🔧 DevTools Protocol Network Capture System")
        print("="*50)
        print("🎯 Strategy: Capture real browser network requests via DevTools")
    
    async def setup_devtools_capture(self):
        """Setup DevTools Protocol network capture"""
        
        print("\n🌐 Setting up DevTools network capture...")
        
        try:
            self.playwright = await async_playwright().start()
            
            # Connect to existing Chrome instance
            self.browser = await self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find or create MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = await self.context.new_page()
                await mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Enable network domain in DevTools
            cdp_session = await self.context.new_cdp_session(self.page)
            await cdp_session.send('Network.enable')
            await cdp_session.send('Runtime.enable')
            
            # Set up request interception
            await cdp_session.send('Network.setRequestInterception', {'patterns': [{'urlPattern': '*'}]})
            
            # Handle network events
            cdp_session.on('Network.requestWillBeSent', self._handle_request)
            cdp_session.on('Network.responseReceived', self._handle_response)
            
            self.cdp_session = cdp_session
            
            # Inject session tokens
            await self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            # Reload to apply session
            await self.page.reload(wait_until='domcontentloaded')
            await asyncio.sleep(3)
            
            print("✅ DevTools network capture setup completed")
            return True
            
        except Exception as e:
            print(f"❌ DevTools setup failed: {e}")
            return False
    
    def _handle_request(self, event):
        """Handle network request events"""
        request = event['request']
        
        # Check if this is an order-related request
        if any(keyword in request['url'] for keyword in ['order/create', 'order/submit', 'order/place']):
            print(f"🎯 Captured order request: {request['url']}")
            
            # Extract headers and body
            headers = request.get('headers', {})
            post_data = request.get('postData', '')
            
            captured_request = {
                'url': request['url'],
                'method': request['method'],
                'headers': headers,
                'post_data': post_data,
                'timestamp': time.time()
            }
            
            self.captured_requests.append(captured_request)
            
            print(f"🔐 Signature: {headers.get('x-mxc-sign', 'None')[:16]}...")
            print(f"🔢 Nonce: {headers.get('x-mxc-nonce', 'None')}")
            print(f"🎫 Token: {headers.get('mtoken', 'None')[:16]}...")
    
    def _handle_response(self, event):
        """Handle network response events"""
        response = event['response']
        
        if any(keyword in response['url'] for keyword in ['order/create', 'order/submit', 'order/place']):
            print(f"📥 Order response: {response['status']} - {response['url']}")
    
    async def trigger_real_order_request(self, symbol: str = 'BTC_USDT', side: int = 1, price: float = 15000.0, volume: int = 1):
        """Trigger a real order request to capture parameters"""
        
        print(f"\n🎯 Triggering real order request for {symbol}...")
        
        try:
            # Clear previous captures
            self.captured_requests = []
            
            # Navigate to trading page
            await self.page.goto(f'https://futures.mexc.com/exchange/{symbol}', wait_until='domcontentloaded')
            await asyncio.sleep(5)
            
            # Try to interact with the order form
            print("🔍 Looking for order form elements...")
            
            # Method 1: Try to find and fill order form
            try:
                # Look for price input
                price_selector = 'input[placeholder*="Price"], input[placeholder*="price"], input[data-testid*="price"]'
                price_input = await self.page.query_selector(price_selector)
                if price_input:
                    await price_input.fill(str(price))
                    print(f"✅ Set price: {price}")
                
                # Look for volume input
                volume_selector = 'input[placeholder*="Amount"], input[placeholder*="amount"], input[placeholder*="Quantity"]'
                volume_input = await self.page.query_selector(volume_selector)
                if volume_input:
                    await volume_input.fill(str(volume))
                    print(f"✅ Set volume: {volume}")
                
                # Look for buy/sell button
                button_text = "Buy" if side == 1 else "Sell"
                button_selector = f'button:has-text("{button_text}"), button[data-testid*="{button_text.lower()}"]'
                order_button = await self.page.query_selector(button_selector)
                
                if order_button:
                    print(f"🎯 Found {button_text} button, clicking...")
                    
                    # Click the button to trigger order preparation
                    await order_button.click()
                    await asyncio.sleep(2)
                    
                    # Look for confirmation dialog or submit button
                    confirm_selectors = [
                        'button:has-text("Confirm")',
                        'button:has-text("Submit")',
                        'button:has-text("Place Order")',
                        'button[data-testid*="confirm"]',
                        'button[data-testid*="submit"]'
                    ]
                    
                    for selector in confirm_selectors:
                        confirm_button = await self.page.query_selector(selector)
                        if confirm_button:
                            print(f"🎯 Found confirmation button: {selector}")
                            # Click to trigger the actual API request
                            await confirm_button.click()
                            await asyncio.sleep(1)
                            break
                
            except Exception as e:
                print(f"⚠️ Form interaction failed: {e}")
            
            # Method 2: Try to trigger via JavaScript
            print("🔧 Trying JavaScript approach...")
            
            try:
                # Inject order triggering script
                await self.page.evaluate(f"""
                    async () => {{
                        // Try to find and trigger order functions
                        const orderData = {{
                            symbol: '{symbol}',
                            side: {side},
                            openType: 1,
                            type: '2',
                            vol: {volume},
                            leverage: 1,
                            marketCeiling: false,
                            price: '{price}',
                            priceProtect: '0'
                        }};
                        
                        const nonce = Date.now().toString();
                        const auth = localStorage.getItem('authorization');
                        
                        // Try to make a direct API call
                        try {{
                            const response = await fetch('/api/v1/private/order/create', {{
                                method: 'POST',
                                headers: {{
                                    'Content-Type': 'application/json',
                                    'authorization': auth,
                                    'x-mxc-nonce': nonce,
                                    'x-mxc-sign': 'dummy_signature_for_capture',
                                    'mtoken': localStorage.getItem('uc_token')
                                }},
                                body: JSON.stringify(orderData)
                            }});
                            
                            console.log('Order request triggered:', response.status);
                        }} catch (e) {{
                            console.log('Order request error (expected):', e.message);
                        }}
                    }}
                """)
                
                await asyncio.sleep(2)
                
            except Exception as e:
                print(f"⚠️ JavaScript trigger failed: {e}")
            
            # Check captured requests
            if self.captured_requests:
                print(f"✅ Captured {len(self.captured_requests)} real requests!")
                return self.captured_requests[0]  # Return the first captured request
            else:
                print("❌ No requests captured")
                return None
                
        except Exception as e:
            print(f"❌ Order trigger error: {e}")
            return None
    
    async def test_complete_workflow(self):
        """Test the complete DevTools capture workflow"""
        
        print("="*60)
        print("DEVTOOLS PROTOCOL NETWORK CAPTURE TEST")
        print("="*60)
        
        # Setup DevTools capture
        if not await self.setup_devtools_capture():
            return False
        
        try:
            # Trigger real order request
            captured_request = await self.trigger_real_order_request()
            
            if captured_request:
                print("\n🎉 REAL REQUEST CAPTURED!")
                print("="*40)
                print(f"URL: {captured_request['url']}")
                print(f"Method: {captured_request['method']}")
                print("Headers:")
                for key, value in captured_request['headers'].items():
                    if 'sign' in key.lower() or 'nonce' in key.lower() or 'token' in key.lower():
                        print(f"  {key}: {value}")
                print(f"Body: {captured_request['post_data']}")
                
                # Now we can use this real signature for actual orders
                print("\n🚀 SUCCESS! Real signature captured and ready for use!")
                return True
            else:
                print("\n❌ Failed to capture real request")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()

async def main():
    """Main async function"""
    
    capture = DevToolsNetworkCapture()
    success = await capture.test_complete_workflow()
    
    if success:
        print("\n🚀 OPTION C SUCCESSFUL!")
        print("DevTools Protocol capture is ready for production!")
    else:
        print("\n❌ OPTION C NEEDS REFINEMENT")
        print("Further development required for production use.")

if __name__ == '__main__':
    asyncio.run(main())
