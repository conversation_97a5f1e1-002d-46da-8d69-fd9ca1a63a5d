#!/usr/bin/env python3
"""
Real-time Signature Cracker
Capture real signatures and reverse engineer the algorithm in real-time
"""

import json
import time
import hashlib
import hmac
import base64
import re
from playwright.sync_api import sync_playwright
from curl_cffi import requests
from dotenv import dotenv_values

class RealtimeSignatureCracker:
    """Real-time signature capture and reverse engineering"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        print("🎯 Real-time MEXC Signature Cracker")
        print("="*40)
    
    def setup_realtime_capture(self):
        """Setup real-time signature capture"""
        
        print("🌐 Setting up real-time signature capture...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            if context.pages:
                self.page = context.pages[0]
            else:
                self.page = context.new_page()
            
            # Navigate to MEXC
            if 'mexc.com' not in self.page.url:
                self.page.goto('https://www.mexc.com/futures/BTC_USDT', wait_until='domcontentloaded')
                time.sleep(5)
            
            # Inject session tokens
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            # Reload to apply session
            self.page.reload(wait_until='domcontentloaded')
            time.sleep(5)
            
            # Setup real-time capture system
            self._setup_signature_capture()
            
            print("✅ Real-time capture system ready")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def _setup_signature_capture(self):
        """Setup signature capture system"""
        
        capture_code = """
            window.signatureCracker = {
                capturedSignatures: [],
                realSignatures: [],
                
                init() {
                    console.log('🎯 Signature cracker initializing...');
                    
                    // Override all possible request methods
                    this.overrideFetch();
                    this.overrideXHR();
                    this.overrideWebSocket();
                    this.monitorFormSubmissions();
                    this.monitorButtonClicks();
                    
                    console.log('✅ Signature cracker ready');
                },
                
                overrideFetch() {
                    const self = this;
                    const originalFetch = window.fetch;
                    
                    window.fetch = function(...args) {
                        const [url, options] = args;
                        
                        if (options && options.headers) {
                            const signature = options.headers['x-mxc-sign'];
                            const nonce = options.headers['x-mxc-nonce'];
                            
                            if (signature && signature !== 'analysis_trigger' && signature !== 'dummy_signature_for_interception') {
                                console.log('🎯 REAL SIGNATURE CAPTURED via fetch!');
                                console.log('Signature:', signature);
                                console.log('Nonce:', nonce);
                                console.log('URL:', url);
                                console.log('Body:', options.body);
                                
                                self.realSignatures.push({
                                    method: 'fetch',
                                    signature: signature,
                                    nonce: nonce,
                                    url: url,
                                    body: options.body,
                                    headers: options.headers,
                                    timestamp: Date.now(),
                                    stackTrace: new Error().stack
                                });
                                
                                // Immediately analyze this signature
                                self.analyzeRealSignature(signature, nonce, options.body);
                            }
                        }
                        
                        return originalFetch.apply(this, args);
                    };
                },
                
                overrideXHR() {
                    const self = this;
                    const OriginalXHR = window.XMLHttpRequest;
                    
                    window.XMLHttpRequest = function() {
                        const xhr = new OriginalXHR();
                        const originalSetRequestHeader = xhr.setRequestHeader;
                        const originalSend = xhr.send;
                        
                        let headers = {};
                        let body = null;
                        let url = '';
                        
                        xhr.setRequestHeader = function(name, value) {
                            headers[name] = value;
                            
                            if (name === 'x-mxc-sign' && value && value !== 'analysis_trigger') {
                                console.log('🎯 REAL SIGNATURE CAPTURED via XHR!');
                                console.log('Signature:', value);
                                
                                self.realSignatures.push({
                                    method: 'xhr',
                                    signature: value,
                                    nonce: headers['x-mxc-nonce'],
                                    url: url,
                                    headers: headers,
                                    timestamp: Date.now(),
                                    stackTrace: new Error().stack
                                });
                            }
                            
                            return originalSetRequestHeader.apply(this, [name, value]);
                        };
                        
                        const originalOpen = xhr.open;
                        xhr.open = function(method, u, ...args) {
                            url = u;
                            return originalOpen.apply(this, [method, u, ...args]);
                        };
                        
                        xhr.send = function(data) {
                            body = data;
                            return originalSend.apply(this, [data]);
                        };
                        
                        return xhr;
                    };
                },
                
                overrideWebSocket() {
                    // Monitor WebSocket for any signature-related messages
                    const originalWebSocket = window.WebSocket;
                    const self = this;
                    
                    window.WebSocket = function(...args) {
                        const ws = new originalWebSocket(...args);
                        
                        const originalSend = ws.send;
                        ws.send = function(data) {
                            if (typeof data === 'string' && data.includes('sign')) {
                                console.log('🎯 WebSocket signature data:', data);
                                self.capturedSignatures.push({
                                    method: 'websocket',
                                    data: data,
                                    timestamp: Date.now()
                                });
                            }
                            return originalSend.apply(this, [data]);
                        };
                        
                        return ws;
                    };
                },
                
                monitorFormSubmissions() {
                    const self = this;
                    
                    // Monitor all form submissions
                    document.addEventListener('submit', function(event) {
                        console.log('📝 Form submission detected:', event.target);
                        
                        // Check if this might trigger an order
                        const form = event.target;
                        const formData = new FormData(form);
                        const formObj = {};
                        
                        for (let [key, value] of formData.entries()) {
                            formObj[key] = value;
                        }
                        
                        if (Object.keys(formObj).some(key => 
                            key.includes('price') || key.includes('amount') || 
                            key.includes('volume') || key.includes('side'))) {
                            
                            console.log('🎯 Order form submission detected!');
                            self.capturedSignatures.push({
                                method: 'form',
                                formData: formObj,
                                timestamp: Date.now()
                            });
                        }
                    }, true);
                },
                
                monitorButtonClicks() {
                    const self = this;
                    
                    // Monitor button clicks that might trigger orders
                    document.addEventListener('click', function(event) {
                        const target = event.target;
                        const text = target.textContent?.toLowerCase() || '';
                        const className = target.className?.toLowerCase() || '';
                        
                        if (text.includes('buy') || text.includes('sell') || 
                            text.includes('long') || text.includes('short') ||
                            className.includes('buy') || className.includes('sell') ||
                            className.includes('order') || className.includes('trade')) {
                            
                            console.log('🎯 Order button clicked:', target);
                            
                            // Set a flag to capture the next signature
                            self.expectingSignature = true;
                            
                            setTimeout(() => {
                                self.expectingSignature = false;
                            }, 5000); // 5 second window
                        }
                    }, true);
                },
                
                analyzeRealSignature(signature, nonce, body) {
                    console.log('🔍 Analyzing real signature...');
                    
                    const auth = localStorage.getItem('authorization') || '';
                    let orderData = null;
                    
                    try {
                        orderData = typeof body === 'string' ? JSON.parse(body) : body;
                    } catch (e) {
                        orderData = body;
                    }
                    
                    console.log('📋 Order data:', orderData);
                    console.log('🔑 Auth:', auth.substring(0, 10) + '...');
                    console.log('🔢 Nonce:', nonce);
                    console.log('🔐 Signature:', signature);
                    
                    // Try to reverse engineer
                    this.attemptReverseEngineering(signature, nonce, orderData, auth);
                },
                
                attemptReverseEngineering(signature, nonce, orderData, auth) {
                    console.log('🧪 Attempting reverse engineering...');
                    
                    if (!orderData || !signature || !nonce || !auth) {
                        console.log('❌ Missing data for reverse engineering');
                        return;
                    }
                    
                    // Try different combinations
                    const attempts = [];
                    
                    // Method 1: JSON + nonce + auth
                    const json1 = JSON.stringify(orderData, Object.keys(orderData).sort());
                    const content1 = json1 + nonce + auth;
                    attempts.push({name: 'JSON+nonce+auth', content: content1});
                    
                    // Method 2: auth + nonce + JSON
                    const content2 = auth + nonce + json1;
                    attempts.push({name: 'auth+nonce+JSON', content: content2});
                    
                    // Method 3: Query string
                    const params = [];
                    for (const key in orderData) {
                        params.push(`${key}=${orderData[key]}`);
                    }
                    const queryString = params.sort().join('&');
                    const content3 = auth + nonce + queryString;
                    attempts.push({name: 'auth+nonce+query', content: content3});
                    
                    // Method 4: Nonce + query + auth
                    const content4 = nonce + queryString + auth;
                    attempts.push({name: 'nonce+query+auth', content: content4});
                    
                    // Test each method
                    attempts.forEach((attempt, index) => {
                        // Try different hash functions
                        const hashes = {
                            'SHA256': this.sha256(attempt.content),
                            'MD5': this.md5(attempt.content),
                            'HMAC-SHA256': this.hmacSha256(attempt.content, auth),
                            'HMAC-SHA256-nonce': this.hmacSha256(attempt.content, nonce)
                        };
                        
                        Object.entries(hashes).forEach(([hashType, hash]) => {
                            // Check different lengths
                            const lengths = [32, 40, 64];
                            lengths.forEach(len => {
                                const truncated = hash.substring(0, len);
                                if (truncated === signature) {
                                    console.log(`🎉 SIGNATURE ALGORITHM FOUND!`);
                                    console.log(`Method: ${attempt.name}`);
                                    console.log(`Hash: ${hashType}`);
                                    console.log(`Length: ${len}`);
                                    console.log(`Content: ${attempt.content}`);
                                    
                                    // Store the working algorithm
                                    window.workingAlgorithm = {
                                        method: attempt.name,
                                        hashType: hashType,
                                        length: len,
                                        content: attempt.content,
                                        signature: signature
                                    };
                                }
                            });
                        });
                    });
                },
                
                // Hash functions
                sha256(str) {
                    // Simple implementation for testing
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }
                    return Math.abs(hash).toString(16).padStart(64, '0');
                },
                
                md5(str) {
                    // Simple implementation for testing
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {
                        const char = str.charCodeAt(i);
                        hash = ((hash << 3) - hash) + char;
                        hash = hash & hash;
                    }
                    return Math.abs(hash).toString(16).padStart(32, '0');
                },
                
                hmacSha256(message, secret) {
                    // Simple HMAC implementation for testing
                    const combined = secret + message + secret;
                    return this.sha256(combined);
                },
                
                getRealSignatures() {
                    return this.realSignatures;
                },
                
                getWorkingAlgorithm() {
                    return window.workingAlgorithm || null;
                }
            };
            
            // Initialize signature cracker
            window.signatureCracker.init();
        """
        
        self.page.evaluate(capture_code)
        print("✅ Signature capture system configured")
    
    def wait_for_real_signature(self, timeout_seconds=300):
        """Wait for real signature capture"""
        
        print(f"⏳ Waiting for real signature capture (timeout: {timeout_seconds}s)...")
        print("💡 Please manually place an order in the browser to capture the signature")
        print("   1. Go to the trading interface")
        print("   2. Set a price and amount")
        print("   3. Click Buy or Sell button")
        print("   4. The signature will be captured automatically")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            try:
                # Check for captured signatures
                signatures = self.page.evaluate("() => window.signatureCracker.getRealSignatures()")
                
                if signatures and len(signatures) > 0:
                    print(f"🎉 CAPTURED {len(signatures)} REAL SIGNATURES!")
                    
                    # Check if algorithm was found
                    working_algorithm = self.page.evaluate("() => window.signatureCracker.getWorkingAlgorithm()")
                    
                    if working_algorithm:
                        print(f"🎉 SIGNATURE ALGORITHM CRACKED!")
                        return signatures, working_algorithm
                    else:
                        print(f"📋 Signatures captured, analyzing...")
                        return signatures, None
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0:  # Every 30 seconds
                    print(f"⏳ Still waiting... ({elapsed}s elapsed)")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error during capture: {e}")
                time.sleep(5)
        
        print(f"⏰ Timeout reached ({timeout_seconds}s)")
        return None, None
    
    def analyze_captured_signatures(self, signatures):
        """Analyze captured signatures"""
        
        if not signatures:
            print("❌ No signatures to analyze")
            return None
        
        print(f"\n🔍 ANALYZING {len(signatures)} CAPTURED SIGNATURES")
        print("="*50)
        
        for i, sig_data in enumerate(signatures):
            print(f"\n📋 Signature {i+1}:")
            print(f"   Method: {sig_data.get('method', 'Unknown')}")
            print(f"   Signature: {sig_data.get('signature', 'None')}")
            print(f"   Nonce: {sig_data.get('nonce', 'None')}")
            print(f"   URL: {sig_data.get('url', 'None')}")
            
            # Try manual reverse engineering
            signature = sig_data.get('signature')
            nonce = sig_data.get('nonce')
            body = sig_data.get('body')
            
            if signature and nonce and body:
                print(f"   🧪 Attempting manual reverse engineering...")
                
                try:
                    order_data = json.loads(body) if isinstance(body, str) else body
                    
                    # Test various algorithms
                    working_algo = self._test_signature_algorithms(signature, nonce, order_data)
                    
                    if working_algo:
                        print(f"   🎉 FOUND WORKING ALGORITHM: {working_algo}")
                        return working_algo
                    else:
                        print(f"   ❌ No matching algorithm found")
                        
                except Exception as e:
                    print(f"   ❌ Analysis error: {e}")
        
        return None
    
    def _test_signature_algorithms(self, target_signature, nonce, order_data):
        """Test various signature algorithms against captured signature"""
        
        algorithms = [
            ('JSON+nonce+auth', lambda: json.dumps(order_data, sort_keys=True) + nonce + self.auth),
            ('auth+nonce+JSON', lambda: self.auth + nonce + json.dumps(order_data, sort_keys=True)),
            ('query+nonce+auth', lambda: self._to_query_string(order_data) + nonce + self.auth),
            ('auth+nonce+query', lambda: self.auth + nonce + self._to_query_string(order_data)),
        ]
        
        hash_functions = [
            ('SHA256', lambda x: hashlib.sha256(x.encode()).hexdigest()),
            ('MD5', lambda x: hashlib.md5(x.encode()).hexdigest()),
            ('HMAC-SHA256-auth', lambda x: hmac.new(self.auth.encode(), x.encode(), hashlib.sha256).hexdigest()),
            ('HMAC-SHA256-nonce', lambda x: hmac.new(nonce.encode(), x.encode(), hashlib.sha256).hexdigest()),
        ]
        
        for algo_name, content_func in algorithms:
            try:
                content = content_func()
                
                for hash_name, hash_func in hash_functions:
                    try:
                        hash_result = hash_func(content)
                        
                        # Test different lengths
                        for length in [16, 24, 32, 40, 48, 64]:
                            truncated = hash_result[:length]
                            
                            if truncated.lower() == target_signature.lower():
                                return {
                                    'algorithm': algo_name,
                                    'hash_function': hash_name,
                                    'length': length,
                                    'content': content,
                                    'signature': truncated
                                }
                    except Exception as e:
                        continue
            except Exception as e:
                continue
        
        return None
    
    def _to_query_string(self, data):
        """Convert dict to query string"""
        params = []
        for key in sorted(data.keys()):
            params.append(f"{key}={data[key]}")
        return '&'.join(params)
    
    def cleanup(self):
        """Cleanup resources"""
        if hasattr(self, 'browser'):
            self.browser.close()
        if hasattr(self, 'playwright'):
            self.playwright.stop()

def main():
    """Main signature cracking function"""
    
    cracker = RealtimeSignatureCracker()
    
    try:
        # Setup real-time capture
        if not cracker.setup_realtime_capture():
            print("❌ Failed to setup real-time capture")
            return
        
        # Wait for real signature
        signatures, working_algorithm = cracker.wait_for_real_signature(timeout_seconds=300)
        
        if working_algorithm:
            print(f"\n🎉 SIGNATURE ALGORITHM SUCCESSFULLY CRACKED!")
            print(f"Algorithm: {working_algorithm}")
            print(f"\n🚀 Ready to implement in production trader!")
        elif signatures:
            print(f"\n📋 Signatures captured, analyzing...")
            working_algo = cracker.analyze_captured_signatures(signatures)
            
            if working_algo:
                print(f"\n🎉 SIGNATURE ALGORITHM FOUND!")
                print(f"Algorithm: {working_algo}")
            else:
                print(f"\n❌ Could not crack the signature algorithm")
                print(f"Need more analysis or different approach")
        else:
            print(f"\n⏰ No signatures captured within timeout")
            print(f"Try manually placing an order while the script runs")
        
    finally:
        cracker.cleanup()

if __name__ == '__main__':
    main()
