#!/usr/bin/env python3
"""
Trading Executor
Uses parameters from Parameter Extractor Service to execute trades directly via API
"""

import json
import time
from curl_cffi import requests
from typing import Dict, Optional
from dotenv import dotenv_values

class MEXCTradingExecutor:
    """Executes trades using parameters from extractor service"""
    
    def __init__(self, extractor_service_url: str = "http://localhost:8888"):
        self.extractor_url = extractor_service_url
        self.session = requests.Session(impersonate='chrome124')
        
        # Load session tokens
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        
        print(f"🚀 Trading Executor initialized")
        print(f"🔗 Extractor service: {extractor_service_url}")
        print(f"🔑 Session tokens: AUTH={'✓' if self.auth else '✗'}, UC_TOKEN={'✓' if self.uc_token else '✗'}")
    
    def check_extractor_service(self) -> bool:
        """Check if parameter extractor service is running"""
        try:
            response = requests.get(f"{self.extractor_url}/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Extractor service status: {data.get('status', 'unknown')}")
                print(f"🌐 Browser connected: {data.get('browser_connected', False)}")
                return data.get('browser_connected', False)
            else:
                print(f"❌ Extractor service returned {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to extractor service: {e}")
            return False
    
    def get_market_price(self, symbol: str) -> float:
        """Get market price from extractor service"""
        try:
            response = requests.get(f"{self.extractor_url}/price",
                                  params={'symbol': symbol}, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    price = data.get('price', 0)
                    print(f"📊 {symbol} price: ${price:,.2f}")
                    return price

            print(f"❌ Failed to get market price for {symbol}")
            return 0.0

        except Exception as e:
            print(f"❌ Market price error: {e}")
            return 0.0
    
    def extract_parameters(self, symbol: str, side: int, price: float, volume: int = 1) -> Optional[Dict]:
        """Extract trading parameters from extractor service"""
        try:
            print(f"🔍 Requesting parameters for {symbol} @ ${price}")
            
            params = {
                'symbol': symbol,
                'side': side,
                'price': price,
                'volume': volume
            }
            
            response = requests.get(f"{self.extractor_url}/extract",
                                  params=params, timeout=15)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('data'):
                    extracted_params = data['data']
                    print(f"✅ Parameters extracted successfully")
                    print(f"🔐 Has real signature: {extracted_params.get('hasRealSignature', False)}")
                    return extracted_params
                else:
                    print(f"❌ Parameter extraction failed")
                    return None
            else:
                print(f"❌ Extractor service returned {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Parameter extraction error: {e}")
            return None
    
    def execute_order(self, params: Dict) -> Dict:
        """Execute order using extracted parameters"""
        
        if not params or not params.get('success'):
            return {'success': False, 'error': 'Invalid parameters'}
        
        print(f"🚀 Executing order with extracted parameters...")
        
        # Prepare headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange',
            'Sec-Fetch-Site': 'same-origin',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'User-Agent': params.get('userAgent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
            'Content-Type': 'application/json',
            'authorization': params.get('auth') or self.auth,
            'x-language': 'en_US',
        }
        
        # Add extracted signature and nonce
        if params.get('nonce'):
            headers['x-mxc-nonce'] = params.get('nonce')
        
        if params.get('signature'):
            headers['x-mxc-sign'] = params.get('signature')
        
        # Add mtoken if available
        if params.get('mtoken') or self.uc_token:
            headers['mtoken'] = params.get('mtoken') or self.uc_token
        
        # Prepare order data with extracted opaque parameters
        order_data = params.get('orderData', {}).copy()
        
        if params.get('p0'):
            order_data['p0'] = params.get('p0')
        if params.get('k0'):
            order_data['k0'] = params.get('k0')
        
        print(f"📋 Order data: {json.dumps(order_data, indent=2)}")
        print(f"🔐 Using signature: {params.get('signature', 'None')[:16]}...")
        print(f"🎫 Using nonce: {params.get('nonce', 'None')}")
        
        # Try different endpoints
        endpoints = [
            ('create', 'https://futures.mexc.com/api/v1/private/order/create'),
            ('submit', 'https://futures.mexc.com/api/v1/private/order/submit')
        ]
        
        for endpoint_name, base_url in endpoints:
            try:
                print(f"🎯 Trying {endpoint_name} endpoint...")
                
                # Add mhash for create endpoint
                url = base_url
                if endpoint_name == 'create':
                    import random, string
                    mhash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
                    url += f'?mhash={mhash}'
                
                r = self.session.post(url, json=order_data, headers=headers)
                
                print(f"Response status: {r.status_code}")
                
                if r.status_code == 200:
                    result = r.json()
                    print(f"Response: {json.dumps(result, indent=2)}")
                    
                    if result.get('success') and result.get('code') == 0:
                        return {
                            'success': True,
                            'endpoint': endpoint_name,
                            'result': result,
                            'order_id': result.get('data', {}).get('orderId')
                        }
                    else:
                        error_code = result.get('code')
                        error_msg = result.get('message', '')
                        print(f"❌ {endpoint_name} failed: {error_code} - {error_msg}")
                        
                        if error_code == 602:
                            print("   Signature verification failed")
                        elif error_code == 401:
                            print("   Authentication failed")
                else:
                    print(f"❌ HTTP {r.status_code}: {r.text[:200]}")
                    
            except Exception as e:
                print(f"❌ Exception with {endpoint_name}: {e}")
        
        return {'success': False, 'error': 'All endpoints failed'}
    
    def cancel_order(self, order_id: str, params: Dict = None) -> Dict:
        """Cancel order"""
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'authorization': self.auth,
            'x-mxc-nonce': str(int(time.time() * 1000)),
        }
        
        # Use extracted signature if available
        if params and params.get('signature'):
            headers['x-mxc-sign'] = params.get('signature')
        
        if self.uc_token:
            headers['mtoken'] = self.uc_token
        
        try:
            r = self.session.post('https://futures.mexc.com/api/v1/private/order/cancel',
                                json=[order_id], headers=headers)
            
            if r.status_code == 200:
                result = r.json()
                return {
                    'success': result.get('success', False),
                    'result': result
                }
            else:
                return {'success': False, 'error': f'HTTP {r.status_code}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def place_order(self, symbol: str, side: int, price: float, volume: int = 1) -> Dict:
        """Complete order placement workflow"""
        
        print(f"\n{'='*60}")
        print(f"PLACING ORDER: {symbol}")
        print(f"Side: {'LONG' if side == 1 else 'SHORT'}")
        print(f"Price: ${price:,.2f}")
        print(f"Volume: {volume}")
        print(f"{'='*60}")
        
        # Step 1: Extract parameters
        params = self.extract_parameters(symbol, side, price, volume)
        if not params:
            return {'success': False, 'error': 'Parameter extraction failed'}
        
        # Step 2: Execute order
        result = self.execute_order(params)
        
        if result.get('success'):
            print(f"✅ Order placed successfully!")
            print(f"📋 Order ID: {result.get('order_id')}")
            print(f"🔗 Endpoint: {result.get('endpoint')}")
            
            # Return both result and params for potential cancellation
            result['params'] = params
            
        return result

def test_trading_executor():
    """Test the trading executor"""
    
    print("="*60)
    print("MEXC TRADING EXECUTOR TEST")
    print("="*60)
    
    executor = MEXCTradingExecutor()
    
    # Check extractor service
    if not executor.check_extractor_service():
        print("❌ Extractor service not available")
        print("Please start the parameter extractor service first:")
        print("python parameter_extractor_service.py")
        return
    
    # Get market price
    symbol = 'BTC_USDT'
    market_price = executor.get_market_price(symbol)
    
    if market_price <= 0:
        print(f"❌ Could not get market price for {symbol}")
        return
    
    # Calculate test order price (70% below market)
    test_price = round(market_price * 0.3, 2)
    
    print(f"\n📊 Market Analysis:")
    print(f"Current Price: ${market_price:,.2f}")
    print(f"Test Order Price: ${test_price:,.2f} (70% below market)")
    
    # Place order
    order_result = executor.place_order(symbol, 1, test_price, 1)
    
    if order_result.get('success'):
        order_id = order_result.get('order_id')
        params = order_result.get('params')
        
        if order_id:
            print(f"\n⏳ Waiting 3 seconds before canceling...")
            time.sleep(3)
            
            # Cancel order
            print(f"\n🔄 Canceling order {order_id}...")
            cancel_result = executor.cancel_order(str(order_id), params)
            
            if cancel_result.get('success'):
                print("✅ Order canceled successfully!")
                print("\n🎉 REAL-TIME PARAMETER EXTRACTION SYSTEM WORKS!")
                print("\nSUMMARY:")
                print("✅ Parameter extraction service: Working")
                print("✅ Real-time parameter generation: Working")
                print("✅ Direct API execution: Working")
                print("✅ Order placement: Working")
                print("✅ Order cancellation: Working")
                print("\n🚀 System ready for production use!")
            else:
                print("❌ Order cancellation failed")
                print("⚠️ Manual cancellation may be required")
        else:
            print("❌ No order ID returned")
    else:
        print("❌ Order placement failed")
        print(f"Error: {order_result.get('error', 'Unknown error')}")
        
        print("\n💡 TROUBLESHOOTING:")
        print("1. Ensure parameter extractor service is running")
        print("2. Check browser session is logged in to MEXC")
        print("3. Verify session tokens are valid")
        print("4. Check if signature extraction is working")

if __name__ == '__main__':
    test_trading_executor()
