#!/usr/bin/env python3
"""
Instant Signature Executor
Execute orders INSTANTLY when signature is captured (within 1-2 seconds)
"""

import json
import time
import random
import string
import hashlib
from playwright.sync_api import sync_playwright
from curl_cffi import requests
from dotenv import dotenv_values
from typing import Dict, Optional

class InstantSignatureExecutor:
    """Execute orders instantly when signature is captured"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        self.session = requests.Session(impersonate='chrome124')
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.page = None
        
        print("⚡ Instant Signature Executor")
        print("="*35)
    
    def setup_instant_system(self):
        """Setup instant execution system"""
        
        print("🌐 Setting up instant execution system...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            if context.pages:
                self.page = context.pages[0]
            else:
                self.page = context.new_page()
            
            # Navigate to TRU_USDT
            if 'TRU_USDT' not in self.page.url:
                self.page.goto('https://www.mexc.com/futures/TRU_USDT', wait_until='domcontentloaded')
                time.sleep(3)
            
            # Inject session tokens
            self.page.evaluate(f"""
                () => {{
                    localStorage.setItem('authorization', '{self.auth}');
                    localStorage.setItem('u_id', '{self.auth}');
                    {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                }}
            """)
            
            # Setup instant capture and execution
            self._setup_instant_capture()
            
            print("✅ Instant execution system ready")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def _setup_instant_capture(self):
        """Setup instant capture and execution"""
        
        instant_code = f"""
            window.instantExecutor = {{
                auth: '{self.auth}',
                uc_token: '{self.uc_token}',
                
                init() {{
                    console.log('⚡ Instant executor initializing...');
                    this.setupInstantCapture();
                    console.log('✅ Instant executor ready');
                }},
                
                setupInstantCapture() {{
                    const self = this;
                    const originalFetch = window.fetch;
                    
                    window.fetch = function(...args) {{
                        const [url, options] = args;
                        
                        // Check for order creation
                        if (url.includes('/order/create') && options && options.method === 'POST') {{
                            const signature = options.headers['x-mxc-sign'];
                            const nonce = options.headers['x-mxc-nonce'];
                            
                            if (signature && nonce) {{
                                console.log('⚡ ORDER SIGNATURE CAPTURED - EXECUTING INSTANTLY!');
                                console.log('Signature:', signature);
                                console.log('Nonce:', nonce);
                                
                                // Execute instantly (within 1-2 seconds)
                                setTimeout(() => {{
                                    self.executeInstantOrder(signature, nonce, options.headers, options.body);
                                }}, 1000); // 1 second delay
                            }}
                        }}
                        
                        return originalFetch.apply(this, args);
                    }};
                }},
                
                async executeInstantOrder(signature, nonce, originalHeaders, originalBody) {{
                    console.log('🚀 Executing instant order...');
                    
                    try {{
                        // Parse original order data
                        let originalOrder = null;
                        try {{
                            originalOrder = typeof originalBody === 'string' ? JSON.parse(originalBody) : originalBody;
                        }} catch (e) {{
                            console.log('❌ Could not parse original order');
                            return;
                        }}
                        
                        // Create new order data (different price to avoid conflict)
                        const newOrder = {{
                            symbol: 'TRU_USDT',
                            side: 1, // Long
                            openType: 1,
                            type: '2',
                            vol: 1,
                            leverage: 1,
                            marketCeiling: false,
                            price: '0.03', // Safe test price
                            priceProtect: '0'
                        }};
                        
                        // Generate new opaque parameters
                        const newNonce = Date.now().toString();
                        const p0 = this.generateHash(newNonce + JSON.stringify(newOrder) + this.auth).substring(0, 32);
                        const k0 = this.generateHash(Date.now().toString() + Math.random().toString()).substring(0, 16);
                        
                        newOrder.p0 = p0;
                        newOrder.k0 = k0;
                        
                        // Prepare headers (copy from original)
                        const headers = {{
                            'Accept': originalHeaders['Accept'] || 'application/json, text/plain, */*',
                            'Accept-Language': originalHeaders['Accept-Language'] || 'en-US,en;q=0.9',
                            'Accept-Encoding': originalHeaders['Accept-Encoding'] || 'gzip, deflate, br, zstd',
                            'Origin': originalHeaders['Origin'] || 'https://futures.mexc.com',
                            'Referer': originalHeaders['Referer'] || 'https://futures.mexc.com/exchange',
                            'Content-Type': originalHeaders['Content-Type'] || 'application/json',
                            'User-Agent': originalHeaders['User-Agent'] || 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                            'authorization': originalHeaders['authorization'] || this.auth,
                            'x-mxc-nonce': newNonce, // Use new nonce
                            'x-mxc-sign': signature, // Use captured signature
                            'x-language': originalHeaders['x-language'] || 'en_US'
                        }};
                        
                        if (this.uc_token) {{
                            headers['mtoken'] = this.uc_token;
                        }}
                        
                        // Generate mhash
                        const mhash = this.generateHash(Date.now().toString()).substring(0, 32);
                        const url = `https://futures.mexc.com/api/v1/private/order/create?mhash=${{mhash}}`;
                        
                        console.log('📡 Sending instant order...');
                        console.log('Order:', newOrder);
                        console.log('Headers:', headers);
                        
                        // Execute order
                        const response = await fetch(url, {{
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify(newOrder)
                        }});
                        
                        const result = await response.json();
                        
                        console.log('📡 Response status:', response.status);
                        console.log('📡 Response:', result);
                        
                        if (response.status === 200 && result.success && result.code === 0) {{
                            const orderId = result.data?.orderId;
                            console.log('🎉 INSTANT ORDER SUCCESS! ID:', orderId);
                            
                            // Auto-cancel after 3 seconds
                            if (orderId) {{
                                setTimeout(() => {{
                                    this.cancelOrder(orderId);
                                }}, 3000);
                            }}
                        }} else {{
                            console.log('❌ Instant order failed:', result.code, result.message);
                            
                            if (result.code === 602) {{
                                console.log('💡 Signature verification failed - may be expired');
                            }}
                        }}
                        
                    }} catch (error) {{
                        console.log('❌ Instant execution error:', error);
                    }}
                }},
                
                async cancelOrder(orderId) {{
                    console.log('🔄 Auto-canceling order:', orderId);
                    
                    try {{
                        const headers = {{
                            'Accept': 'application/json, text/plain, */*',
                            'Content-Type': 'application/json',
                            'authorization': this.auth,
                            'x-mxc-nonce': Date.now().toString()
                        }};
                        
                        if (this.uc_token) {{
                            headers['mtoken'] = this.uc_token;
                        }}
                        
                        const response = await fetch('https://futures.mexc.com/api/v1/private/order/cancel', {{
                            method: 'POST',
                            headers: headers,
                            body: JSON.stringify([orderId])
                        }});
                        
                        const result = await response.json();
                        
                        if (response.status === 200 && result.success && result.code === 0) {{
                            console.log('✅ Order auto-canceled successfully');
                        }} else {{
                            console.log('⚠️ Auto-cancel failed:', result.message);
                        }}
                        
                    }} catch (error) {{
                        console.log('❌ Auto-cancel error:', error);
                    }}
                }},
                
                generateHash(str) {{
                    let hash = 0;
                    for (let i = 0; i < str.length; i++) {{
                        const char = str.charCodeAt(i);
                        hash = ((hash << 5) - hash) + char;
                        hash = hash & hash;
                    }}
                    return Math.abs(hash).toString(16).padStart(32, '0');
                }}
            }};
            
            // Initialize instant executor
            window.instantExecutor.init();
        """
        
        self.page.evaluate(instant_code)
        print("✅ Instant capture and execution system configured")
    
    def wait_for_instant_execution(self, timeout_seconds=300):
        """Wait for instant execution to happen"""
        
        print(f"\n{'='*60}")
        print(f"INSTANT SIGNATURE EXECUTION SYSTEM")
        print(f"{'='*60}")
        print(f"⏳ Waiting for you to place an order (timeout: {timeout_seconds}s)...")
        print(f"💡 When you place an order:")
        print(f"   1. Signature will be captured instantly")
        print(f"   2. New order will be executed within 1-2 seconds")
        print(f"   3. Order will be auto-canceled after 3 seconds")
        print(f"   4. You'll see all results in browser console")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout_seconds:
            try:
                # Check browser console for results
                console_logs = self.page.evaluate("""
                    () => {
                        // This is just to keep the connection alive
                        return window.instantExecutor ? 'ready' : 'not ready';
                    }
                """)
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:
                    print(f"⏳ Still waiting for order placement... ({elapsed}s elapsed)")
                    print(f"   💡 Place an order in the browser to see instant execution")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"❌ Error: {e}")
                time.sleep(2)
        
        print(f"⏰ Timeout reached")
        print(f"💡 Check browser console for execution results")
    
    def cleanup(self):
        """Cleanup resources"""
        if self.browser:
            self.browser.close()
        if self.playwright:
            self.playwright.stop()

def main():
    """Main instant execution"""
    
    executor = InstantSignatureExecutor()
    
    try:
        # Setup instant system
        if not executor.setup_instant_system():
            print("❌ Failed to setup instant system")
            return
        
        # Wait for instant execution
        executor.wait_for_instant_execution(timeout_seconds=300)
        
        print(f"\n🎯 INSTANT EXECUTION SYSTEM COMPLETE!")
        print(f"✅ System captures signatures and executes instantly")
        print(f"💡 Check browser console (F12) for detailed results")
        
    finally:
        executor.cleanup()

if __name__ == '__main__':
    main()
