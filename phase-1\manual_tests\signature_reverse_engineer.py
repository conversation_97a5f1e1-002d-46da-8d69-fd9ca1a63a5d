#!/usr/bin/env python3
"""
MEXC Signature Reverse Engineering
Attempts to reverse engineer the x-mxc-sign algorithm from frontend JavaScript
"""

import json
import time
import re
from curl_cffi import requests
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class MEXCSignatureReverseEngineer:
    """Reverse engineer MEXC's signing algorithm"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        
        print("🔍 MEXC Signature Reverse Engineering")
        print("="*50)
    
    def analyze_frontend_javascript(self):
        """Analyze frontend JavaScript to find signing functions"""
        
        print("\n📋 STEP 1: Analyzing Frontend JavaScript...")
        
        try:
            with sync_playwright() as p:
                browser = p.chromium.connect_over_cdp('http://127.0.0.1:9222')
                
                if not browser.contexts:
                    print("❌ No browser contexts found")
                    return None
                
                context = browser.contexts[0]
                
                # Find MEXC page
                mexc_page = None
                for page in context.pages:
                    if 'mexc.com' in (page.url or ''):
                        mexc_page = page
                        break
                
                if not mexc_page:
                    mexc_page = context.new_page()
                    mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
                
                # Inject session tokens
                mexc_page.evaluate(f"""
                    () => {{
                        localStorage.setItem('authorization', '{self.auth}');
                        localStorage.setItem('u_id', '{self.auth}');
                        {f"localStorage.setItem('uc_token', '{self.uc_token}');" if self.uc_token else ""}
                    }}
                """)
                
                mexc_page.reload(wait_until='domcontentloaded')
                time.sleep(5)
                
                # Analyze JavaScript environment
                analysis = mexc_page.evaluate("""
                    () => {
                        const results = {
                            globalFunctions: [],
                            cryptoFunctions: [],
                            signFunctions: [],
                            mexcObjects: [],
                            scripts: [],
                            possibleSigningCode: []
                        };
                        
                        // 1. Scan global functions
                        for (const key in window) {
                            try {
                                const value = window[key];
                                if (typeof value === 'function') {
                                    const funcStr = value.toString();
                                    const keyLower = key.toLowerCase();
                                    
                                    // Look for signing-related functions
                                    if (keyLower.includes('sign') || 
                                        keyLower.includes('hash') || 
                                        keyLower.includes('crypto') ||
                                        keyLower.includes('auth') ||
                                        keyLower.includes('token')) {
                                        results.signFunctions.push({
                                            name: key,
                                            code: funcStr.substring(0, 200)
                                        });
                                    }
                                    
                                    // Look for crypto functions
                                    if (funcStr.includes('sha256') || 
                                        funcStr.includes('md5') || 
                                        funcStr.includes('hmac') ||
                                        funcStr.includes('encrypt')) {
                                        results.cryptoFunctions.push({
                                            name: key,
                                            code: funcStr.substring(0, 200)
                                        });
                                    }
                                }
                            } catch (e) {}
                        }
                        
                        // 2. Look for MEXC-specific objects
                        ['mexc', 'MEXC', 'app', 'App', 'api', 'API', 'request', 'Request'].forEach(name => {
                            if (window[name] && typeof window[name] === 'object') {
                                try {
                                    const obj = window[name];
                                    const methods = Object.getOwnPropertyNames(obj).filter(prop => 
                                        typeof obj[prop] === 'function'
                                    );
                                    results.mexcObjects.push({
                                        name: name,
                                        methods: methods
                                    });
                                } catch (e) {}
                            }
                        });
                        
                        // 3. Scan all script tags for signing code
                        const scripts = document.querySelectorAll('script');
                        scripts.forEach((script, index) => {
                            if (script.src) {
                                results.scripts.push({
                                    type: 'external',
                                    src: script.src,
                                    index: index
                                });
                            } else if (script.textContent) {
                                const content = script.textContent;
                                // Look for signing patterns
                                if (content.includes('x-mxc-sign') || 
                                    content.includes('signature') ||
                                    content.includes('sign') ||
                                    content.includes('crypto')) {
                                    results.possibleSigningCode.push({
                                        index: index,
                                        snippet: content.substring(0, 500)
                                    });
                                }
                            }
                        });
                        
                        return results;
                    }
                """)
                
                browser.close()
                return analysis
                
        except Exception as e:
            print(f"❌ JavaScript analysis failed: {e}")
            return None
    
    def intercept_network_requests(self):
        """Intercept network requests to capture signing process"""
        
        print("\n📡 STEP 2: Intercepting Network Requests...")
        
        captured_requests = []
        
        try:
            with sync_playwright() as p:
                browser = p.chromium.connect_over_cdp('http://127.0.0.1:9222')
                context = browser.contexts[0]
                
                # Find MEXC page
                mexc_page = None
                for page in context.pages:
                    if 'mexc.com' in (page.url or ''):
                        mexc_page = page
                        break
                
                if not mexc_page:
                    print("❌ No MEXC page found")
                    return None
                
                # Set up request interception
                def handle_request(request):
                    if 'order' in request.url and request.method == 'POST':
                        headers = dict(request.headers)
                        post_data = request.post_data
                        
                        captured_requests.append({
                            'url': request.url,
                            'method': request.method,
                            'headers': headers,
                            'post_data': post_data,
                            'timestamp': time.time()
                        })
                        
                        print(f"🎯 Captured request to: {request.url}")
                        if 'x-mxc-sign' in headers:
                            print(f"   Signature: {headers['x-mxc-sign'][:16]}...")
                        if 'x-mxc-nonce' in headers:
                            print(f"   Nonce: {headers['x-mxc-nonce']}")
                
                mexc_page.on('request', handle_request)
                
                # Try to trigger a request by simulating order preparation
                print("🔧 Attempting to trigger signing process...")
                
                trigger_result = mexc_page.evaluate("""
                    async () => {
                        try {
                            // Try to find and call order preparation functions
                            const testOrder = {
                                symbol: 'BTC_USDT',
                                side: 1,
                                openType: 1,
                                type: '2',
                                vol: 1,
                                leverage: 1,
                                price: '50000',
                                priceProtect: '0'
                            };
                            
                            // Look for common order preparation functions
                            const prepFunctions = [
                                'prepareOrder', 'createOrder', 'signOrder', 
                                'submitOrder', 'placeOrder', 'processOrder'
                            ];
                            
                            for (const funcName of prepFunctions) {
                                if (typeof window[funcName] === 'function') {
                                    try {
                                        const result = window[funcName](testOrder);
                                        return { success: true, function: funcName, result: result };
                                    } catch (e) {
                                        console.log(`Failed with ${funcName}:`, e);
                                    }
                                }
                            }
                            
                            return { success: false, message: 'No order functions found' };
                            
                        } catch (error) {
                            return { success: false, error: error.toString() };
                        }
                    }
                """)
                
                print(f"Trigger result: {trigger_result}")
                
                # Wait for potential requests
                time.sleep(3)
                
                browser.close()
                return captured_requests
                
        except Exception as e:
            print(f"❌ Network interception failed: {e}")
            return []
    
    def analyze_external_scripts(self):
        """Download and analyze external JavaScript files"""
        
        print("\n📥 STEP 3: Analyzing External Scripts...")
        
        # Common MEXC script patterns
        script_patterns = [
            'https://futures.mexc.com/static/js/',
            'https://www.mexc.com/static/js/',
            'https://assets.mexc.com/',
        ]
        
        try:
            with sync_playwright() as p:
                browser = p.chromium.connect_over_cdp('http://127.0.0.1:9222')
                context = browser.contexts[0]
                
                mexc_page = None
                for page in context.pages:
                    if 'mexc.com' in (page.url or ''):
                        mexc_page = page
                        break
                
                if not mexc_page:
                    print("❌ No MEXC page found")
                    return None
                
                # Get all script URLs
                script_urls = mexc_page.evaluate("""
                    () => {
                        const scripts = Array.from(document.querySelectorAll('script[src]'));
                        return scripts.map(script => script.src).filter(src => 
                            src.includes('mexc.com') || src.includes('static')
                        );
                    }
                """)
                
                browser.close()
                
                print(f"Found {len(script_urls)} external scripts")
                
                # Analyze scripts for signing patterns
                signing_patterns = []
                
                for i, url in enumerate(script_urls[:5]):  # Limit to first 5 scripts
                    try:
                        print(f"📄 Analyzing script {i+1}: {url[:50]}...")
                        
                        response = requests.get(url, timeout=10)
                        if response.status_code == 200:
                            content = response.text
                            
                            # Look for signing patterns
                            patterns = [
                                r'x-mxc-sign["\']?\s*:\s*([^,\}]+)',
                                r'signature["\']?\s*:\s*([^,\}]+)',
                                r'sign\s*\([^)]*\)\s*{[^}]*}',
                                r'crypto\.[a-zA-Z]+\([^)]*\)',
                                r'hmac[^(]*\([^)]*\)',
                                r'sha256[^(]*\([^)]*\)'
                            ]
                            
                            for pattern in patterns:
                                matches = re.findall(pattern, content, re.IGNORECASE)
                                if matches:
                                    signing_patterns.append({
                                        'url': url,
                                        'pattern': pattern,
                                        'matches': matches[:3]  # First 3 matches
                                    })
                        
                    except Exception as e:
                        print(f"   ❌ Failed to analyze {url}: {e}")
                
                return signing_patterns
                
        except Exception as e:
            print(f"❌ Script analysis failed: {e}")
            return []
    
    def attempt_signature_recreation(self):
        """Attempt to recreate the signature algorithm based on analysis"""
        
        print("\n🔧 STEP 4: Attempting Signature Recreation...")
        
        # Test data
        test_order = {
            'symbol': 'BTC_USDT',
            'side': 1,
            'openType': 1,
            'type': '2',
            'vol': 1,
            'leverage': 1,
            'price': '50000',
            'priceProtect': '0'
        }
        
        nonce = str(int(time.time() * 1000))
        
        print(f"Test order: {json.dumps(test_order, indent=2)}")
        print(f"Nonce: {nonce}")
        
        # Try different signing algorithms
        algorithms = [
            self._try_simple_hash,
            self._try_hmac_sha256,
            self._try_md5_combination,
            self._try_complex_combination
        ]
        
        results = []
        
        for algo in algorithms:
            try:
                signature = algo(test_order, nonce)
                results.append({
                    'algorithm': algo.__name__,
                    'signature': signature,
                    'length': len(signature) if signature else 0
                })
                print(f"✅ {algo.__name__}: {signature[:16] if signature else 'None'}...")
            except Exception as e:
                print(f"❌ {algo.__name__}: {e}")
        
        return results
    
    def _try_simple_hash(self, order_data, nonce):
        """Try simple hash-based signature"""
        import hashlib
        content = json.dumps(order_data, sort_keys=True) + nonce + (self.auth or '')
        return hashlib.sha256(content.encode()).hexdigest()[:32]
    
    def _try_hmac_sha256(self, order_data, nonce):
        """Try HMAC-SHA256 signature"""
        import hmac, hashlib
        message = json.dumps(order_data, sort_keys=True) + nonce
        secret = (self.auth or '').encode()
        return hmac.new(secret, message.encode(), hashlib.sha256).hexdigest()[:32]
    
    def _try_md5_combination(self, order_data, nonce):
        """Try MD5-based combination"""
        import hashlib
        parts = [
            json.dumps(order_data, sort_keys=True),
            nonce,
            self.auth or '',
            self.uc_token or ''
        ]
        content = ''.join(parts)
        return hashlib.md5(content.encode()).hexdigest()
    
    def _try_complex_combination(self, order_data, nonce):
        """Try complex combination algorithm"""
        import hashlib, hmac
        
        # Create query string
        query_parts = []
        for key, value in sorted(order_data.items()):
            query_parts.append(f"{key}={value}")
        query_string = '&'.join(query_parts)
        
        # Combine with auth and nonce
        message = f"{self.auth}{nonce}{query_string}"
        
        # Double hash
        first_hash = hashlib.sha256(message.encode()).hexdigest()
        second_hash = hashlib.md5(first_hash.encode()).hexdigest()
        
        return second_hash
    
    def run_complete_analysis(self):
        """Run complete reverse engineering analysis"""
        
        print("🚀 Starting Complete Signature Reverse Engineering...")
        print("="*60)
        
        # Step 1: Analyze JavaScript
        js_analysis = self.analyze_frontend_javascript()
        if js_analysis:
            print(f"✅ Found {len(js_analysis.get('signFunctions', []))} signing functions")
            print(f"✅ Found {len(js_analysis.get('cryptoFunctions', []))} crypto functions")
            print(f"✅ Found {len(js_analysis.get('mexcObjects', []))} MEXC objects")
        
        # Step 2: Intercept requests
        captured_requests = self.intercept_network_requests()
        print(f"✅ Captured {len(captured_requests)} network requests")
        
        # Step 3: Analyze scripts
        script_patterns = self.analyze_external_scripts()
        print(f"✅ Found {len(script_patterns)} signing patterns in scripts")
        
        # Step 4: Attempt recreation
        signature_attempts = self.attempt_signature_recreation()
        print(f"✅ Tested {len(signature_attempts)} signature algorithms")
        
        # Final analysis
        print("\n" + "="*60)
        print("REVERSE ENGINEERING RESULTS")
        print("="*60)
        
        if js_analysis and js_analysis.get('signFunctions'):
            print("🔍 SIGNING FUNCTIONS FOUND:")
            for func in js_analysis['signFunctions'][:3]:
                print(f"   - {func['name']}: {func['code'][:100]}...")
        
        if captured_requests:
            print("📡 CAPTURED SIGNATURES:")
            for req in captured_requests[:2]:
                headers = req.get('headers', {})
                if 'x-mxc-sign' in headers:
                    print(f"   - {headers['x-mxc-sign'][:16]}...")
        
        if script_patterns:
            print("📄 SCRIPT PATTERNS:")
            for pattern in script_patterns[:3]:
                print(f"   - {pattern['pattern']}: {len(pattern['matches'])} matches")
        
        print("🔧 SIGNATURE ATTEMPTS:")
        for attempt in signature_attempts:
            print(f"   - {attempt['algorithm']}: {attempt['signature'][:16]}... (len: {attempt['length']})")
        
        # Conclusion
        feasible = (
            len(js_analysis.get('signFunctions', [])) > 0 or
            len(captured_requests) > 0 or
            len(script_patterns) > 0
        )
        
        print(f"\n💡 FEASIBILITY ASSESSMENT:")
        print(f"Reverse Engineering Feasible: {'✅ YES' if feasible else '❌ NO'}")
        
        if feasible:
            print("🎯 NEXT STEPS:")
            print("1. Deep dive into identified signing functions")
            print("2. Analyze captured request patterns")
            print("3. Implement the real algorithm")
        else:
            print("🔄 RECOMMENDATION:")
            print("Switch to Option B: Real-time Browser Integration")
        
        return feasible

def main():
    """Main reverse engineering function"""
    
    engineer = MEXCSignatureReverseEngineer()
    feasible = engineer.run_complete_analysis()
    
    if not feasible:
        print("\n🔄 SWITCHING TO OPTION B...")
        print("Real-time Browser Integration is the recommended approach.")

if __name__ == '__main__':
    main()
