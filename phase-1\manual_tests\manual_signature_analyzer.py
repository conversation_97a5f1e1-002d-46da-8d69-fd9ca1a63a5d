#!/usr/bin/env python3
"""
Manual Signature Analyzer
Manually analyze the captured signature to crack the algorithm
"""

import json
import hashlib
import hmac
import base64
import time
from dotenv import dotenv_values

class ManualSignatureAnalyzer:
    """Manually analyze captured signatures"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.uc_token = vals.get('uc_token')
        
        print("🔍 Manual Signature Analyzer")
        print("="*35)
    
    def analyze_signature(self, signature: str, nonce: str, order_data: dict):
        """Manually analyze a signature"""
        
        print(f"🔍 Analyzing signature: {signature}")
        print(f"🔢 Nonce: {nonce}")
        print(f"📋 Order data: {json.dumps(order_data, indent=2)}")
        print(f"🔑 Auth: {self.auth[:10]}...")
        
        # Test comprehensive algorithms
        working_algorithms = []
        
        # Test different content generation methods
        content_methods = [
            ("JSON", lambda: json.dumps(order_data)),
            ("JSON-sorted", lambda: json.dumps(order_data, sort_keys=True)),
            ("JSON-compact", lambda: json.dumps(order_data, separators=(',', ':'))),
            ("JSON-compact-sorted", lambda: json.dumps(order_data, separators=(',', ':'), sort_keys=True)),
            ("Query", lambda: self._to_query_string(order_data)),
            ("Query-sorted", lambda: self._to_query_string_sorted(order_data)),
            
            # Combined with auth and nonce
            ("auth+nonce+JSON", lambda: self.auth + nonce + json.dumps(order_data, sort_keys=True)),
            ("nonce+auth+JSON", lambda: nonce + self.auth + json.dumps(order_data, sort_keys=True)),
            ("JSON+nonce+auth", lambda: json.dumps(order_data, sort_keys=True) + nonce + self.auth),
            ("JSON+auth+nonce", lambda: json.dumps(order_data, sort_keys=True) + self.auth + nonce),
            
            ("auth+nonce+query", lambda: self.auth + nonce + self._to_query_string_sorted(order_data)),
            ("nonce+auth+query", lambda: nonce + self.auth + self._to_query_string_sorted(order_data)),
            ("query+nonce+auth", lambda: self._to_query_string_sorted(order_data) + nonce + self.auth),
            ("query+auth+nonce", lambda: self._to_query_string_sorted(order_data) + self.auth + nonce),
            
            # With separators
            ("auth|nonce|JSON", lambda: self.auth + "|" + nonce + "|" + json.dumps(order_data, sort_keys=True)),
            ("auth&nonce&query", lambda: self.auth + "&" + nonce + "&" + self._to_query_string_sorted(order_data)),
            
            # With timestamps
            ("auth+nonce+timestamp+JSON", lambda: self.auth + nonce + str(int(time.time())) + json.dumps(order_data, sort_keys=True)),
            ("auth+nonce+ms+JSON", lambda: self.auth + nonce + str(int(time.time() * 1000)) + json.dumps(order_data, sort_keys=True)),
        ]
        
        # Test different hash functions
        hash_methods = [
            ("MD5", lambda x: hashlib.md5(x.encode()).hexdigest()),
            ("SHA1", lambda x: hashlib.sha1(x.encode()).hexdigest()),
            ("SHA256", lambda x: hashlib.sha256(x.encode()).hexdigest()),
            ("SHA512", lambda x: hashlib.sha512(x.encode()).hexdigest()),
            
            # HMAC variants
            ("HMAC-MD5-auth", lambda x: hmac.new(self.auth.encode(), x.encode(), hashlib.md5).hexdigest()),
            ("HMAC-SHA1-auth", lambda x: hmac.new(self.auth.encode(), x.encode(), hashlib.sha1).hexdigest()),
            ("HMAC-SHA256-auth", lambda x: hmac.new(self.auth.encode(), x.encode(), hashlib.sha256).hexdigest()),
            ("HMAC-SHA512-auth", lambda x: hmac.new(self.auth.encode(), x.encode(), hashlib.sha512).hexdigest()),
            
            ("HMAC-MD5-nonce", lambda x: hmac.new(nonce.encode(), x.encode(), hashlib.md5).hexdigest()),
            ("HMAC-SHA1-nonce", lambda x: hmac.new(nonce.encode(), x.encode(), hashlib.sha1).hexdigest()),
            ("HMAC-SHA256-nonce", lambda x: hmac.new(nonce.encode(), x.encode(), hashlib.sha256).hexdigest()),
            ("HMAC-SHA512-nonce", lambda x: hmac.new(nonce.encode(), x.encode(), hashlib.sha512).hexdigest()),
            
            # Double hash
            ("Double-MD5", lambda x: hashlib.md5(hashlib.md5(x.encode()).hexdigest().encode()).hexdigest()),
            ("Double-SHA256", lambda x: hashlib.sha256(hashlib.sha256(x.encode()).hexdigest().encode()).hexdigest()),
            
            # Base64 variants
            ("Base64-MD5", lambda x: hashlib.md5(base64.b64encode(x.encode())).hexdigest()),
            ("Base64-SHA256", lambda x: hashlib.sha256(base64.b64encode(x.encode())).hexdigest()),
        ]
        
        print(f"\n🧪 Testing {len(content_methods)} content methods × {len(hash_methods)} hash methods...")
        
        total_tests = 0
        
        for content_name, content_func in content_methods:
            try:
                content = content_func()
                
                for hash_name, hash_func in hash_methods:
                    try:
                        hash_result = hash_func(content)
                        
                        # Test different lengths
                        for length in [16, 24, 32, 40, 48, 64]:
                            total_tests += 1
                            truncated = hash_result[:length]
                            
                            if truncated.lower() == signature.lower():
                                working_algorithm = {
                                    'content_method': content_name,
                                    'hash_method': hash_name,
                                    'length': length,
                                    'content': content,
                                    'hash_result': hash_result,
                                    'signature': truncated
                                }
                                working_algorithms.append(working_algorithm)
                                
                                print(f"\n🎉 FOUND WORKING ALGORITHM!")
                                print(f"   Content: {content_name}")
                                print(f"   Hash: {hash_name}")
                                print(f"   Length: {length}")
                                print(f"   Content preview: {content[:100]}...")
                                print(f"   Generated: {truncated}")
                                print(f"   Target: {signature}")
                    
                    except Exception as e:
                        continue
            
            except Exception as e:
                continue
        
        print(f"\n📊 Tested {total_tests} combinations")
        print(f"🎯 Found {len(working_algorithms)} working algorithms")
        
        return working_algorithms
    
    def _to_query_string(self, data: dict) -> str:
        """Convert dict to query string"""
        params = []
        for key, value in data.items():
            params.append(f"{key}={value}")
        return '&'.join(params)
    
    def _to_query_string_sorted(self, data: dict) -> str:
        """Convert dict to sorted query string"""
        params = []
        for key in sorted(data.keys()):
            params.append(f"{key}={data[key]}")
        return '&'.join(params)
    
    def test_algorithm(self, algorithm: dict, test_order_data: dict, test_nonce: str) -> str:
        """Test an algorithm with new data"""
        
        print(f"🧪 Testing algorithm: {algorithm['content_method']} + {algorithm['hash_method']}")
        
        try:
            # Recreate content based on method
            content_method = algorithm['content_method']
            
            if content_method == "JSON":
                content = json.dumps(test_order_data)
            elif content_method == "JSON-sorted":
                content = json.dumps(test_order_data, sort_keys=True)
            elif content_method == "JSON-compact":
                content = json.dumps(test_order_data, separators=(',', ':'))
            elif content_method == "JSON-compact-sorted":
                content = json.dumps(test_order_data, separators=(',', ':'), sort_keys=True)
            elif content_method == "Query":
                content = self._to_query_string(test_order_data)
            elif content_method == "Query-sorted":
                content = self._to_query_string_sorted(test_order_data)
            elif content_method == "auth+nonce+JSON":
                content = self.auth + test_nonce + json.dumps(test_order_data, sort_keys=True)
            elif content_method == "nonce+auth+JSON":
                content = test_nonce + self.auth + json.dumps(test_order_data, sort_keys=True)
            elif content_method == "JSON+nonce+auth":
                content = json.dumps(test_order_data, sort_keys=True) + test_nonce + self.auth
            elif content_method == "JSON+auth+nonce":
                content = json.dumps(test_order_data, sort_keys=True) + self.auth + test_nonce
            elif content_method == "auth+nonce+query":
                content = self.auth + test_nonce + self._to_query_string_sorted(test_order_data)
            elif content_method == "nonce+auth+query":
                content = test_nonce + self.auth + self._to_query_string_sorted(test_order_data)
            elif content_method == "query+nonce+auth":
                content = self._to_query_string_sorted(test_order_data) + test_nonce + self.auth
            elif content_method == "query+auth+nonce":
                content = self._to_query_string_sorted(test_order_data) + self.auth + test_nonce
            else:
                # Default
                content = self.auth + test_nonce + json.dumps(test_order_data, sort_keys=True)
            
            # Apply hash function
            hash_method = algorithm['hash_method']
            
            if hash_method == "MD5":
                hash_result = hashlib.md5(content.encode()).hexdigest()
            elif hash_method == "SHA1":
                hash_result = hashlib.sha1(content.encode()).hexdigest()
            elif hash_method == "SHA256":
                hash_result = hashlib.sha256(content.encode()).hexdigest()
            elif hash_method == "SHA512":
                hash_result = hashlib.sha512(content.encode()).hexdigest()
            elif hash_method == "HMAC-MD5-auth":
                hash_result = hmac.new(self.auth.encode(), content.encode(), hashlib.md5).hexdigest()
            elif hash_method == "HMAC-SHA1-auth":
                hash_result = hmac.new(self.auth.encode(), content.encode(), hashlib.sha1).hexdigest()
            elif hash_method == "HMAC-SHA256-auth":
                hash_result = hmac.new(self.auth.encode(), content.encode(), hashlib.sha256).hexdigest()
            elif hash_method == "HMAC-SHA512-auth":
                hash_result = hmac.new(self.auth.encode(), content.encode(), hashlib.sha512).hexdigest()
            elif hash_method == "HMAC-MD5-nonce":
                hash_result = hmac.new(test_nonce.encode(), content.encode(), hashlib.md5).hexdigest()
            elif hash_method == "HMAC-SHA1-nonce":
                hash_result = hmac.new(test_nonce.encode(), content.encode(), hashlib.sha1).hexdigest()
            elif hash_method == "HMAC-SHA256-nonce":
                hash_result = hmac.new(test_nonce.encode(), content.encode(), hashlib.sha256).hexdigest()
            elif hash_method == "HMAC-SHA512-nonce":
                hash_result = hmac.new(test_nonce.encode(), content.encode(), hashlib.sha512).hexdigest()
            elif hash_method == "Double-MD5":
                hash_result = hashlib.md5(hashlib.md5(content.encode()).hexdigest().encode()).hexdigest()
            elif hash_method == "Double-SHA256":
                hash_result = hashlib.sha256(hashlib.sha256(content.encode()).hexdigest().encode()).hexdigest()
            elif hash_method == "Base64-MD5":
                hash_result = hashlib.md5(base64.b64encode(content.encode())).hexdigest()
            elif hash_method == "Base64-SHA256":
                hash_result = hashlib.sha256(base64.b64encode(content.encode())).hexdigest()
            else:
                hash_result = hashlib.sha256(content.encode()).hexdigest()
            
            # Truncate to correct length
            length = algorithm['length']
            signature = hash_result[:length]
            
            print(f"✅ Generated signature: {signature}")
            return signature
            
        except Exception as e:
            print(f"❌ Algorithm test failed: {e}")
            return ""

def main():
    """Main manual analysis"""
    
    analyzer = ManualSignatureAnalyzer()
    
    # Example captured signature data (replace with real data)
    captured_signature = "8eb7338454899debfeaf0f4e444b7ab7"
    captured_nonce = "1754915059118"  # Example nonce
    captured_order_data = {
        "symbol": "BTC_USDT",
        "side": 1,
        "openType": 1,
        "type": "2",
        "vol": 1,
        "leverage": 1,
        "marketCeiling": False,
        "price": "30000",
        "priceProtect": "0",
        "p0": "example_p0",
        "k0": "example_k0"
    }
    
    print("🔍 MANUAL SIGNATURE ANALYSIS")
    print("="*40)
    print("Replace the example data below with real captured data:")
    print(f"Signature: {captured_signature}")
    print(f"Nonce: {captured_nonce}")
    print(f"Order data: {json.dumps(captured_order_data, indent=2)}")
    
    # Analyze the signature
    working_algorithms = analyzer.analyze_signature(
        captured_signature, 
        captured_nonce, 
        captured_order_data
    )
    
    if working_algorithms:
        print(f"\n🎉 SIGNATURE ALGORITHM CRACKED!")
        
        # Save the first working algorithm
        best_algorithm = working_algorithms[0]
        
        with open('cracked_algorithm.json', 'w') as f:
            json.dump(best_algorithm, f, indent=2)
        
        print(f"💾 Algorithm saved to cracked_algorithm.json")
        
        # Test the algorithm with new data
        print(f"\n🧪 Testing algorithm with new data...")
        
        test_order = {
            "symbol": "ETH_USDT",
            "side": 2,
            "openType": 1,
            "type": "2",
            "vol": 2,
            "leverage": 1,
            "marketCeiling": False,
            "price": "2000",
            "priceProtect": "0"
        }
        test_nonce = str(int(time.time() * 1000))
        
        test_signature = analyzer.test_algorithm(best_algorithm, test_order, test_nonce)
        
        if test_signature:
            print(f"✅ Algorithm test successful!")
            print(f"🚀 Ready for production implementation!")
        else:
            print(f"❌ Algorithm test failed")
    else:
        print(f"\n❌ Could not crack the signature algorithm")
        print(f"💡 Try capturing more signatures or check the data")

if __name__ == '__main__':
    main()
