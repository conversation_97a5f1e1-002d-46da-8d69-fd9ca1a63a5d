# Minimal requirements for testing
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
jinja2>=3.1.0
python-multipart>=0.0.6

# Browser automation
playwright>=1.40.0

# HTTP clients
httpx>=0.25.0
aiohttp>=3.9.0
requests>=2.31.0

# Database
sqlalchemy>=2.0.0
aiosqlite>=0.19.0

# Configuration
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Telegram
python-telegram-bot>=20.0

# Security
cryptography>=41.0.0

# Logging
structlog>=23.0.0

# Utilities
python-dateutil>=2.8.0
