#!/bin/bash

# MEXC Trading System Startup Script

set -e

echo "🚀 Starting MEXC Trading System..."

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "❌ .env file not found. Copying from template..."
    cp .env.template .env
    echo "⚠️  Please configure .env file before running the system"
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p logs data browser_data backups static

# Check Python version
python_version=$(python3 --version 2>&1 | cut -d' ' -f2 | cut -d'.' -f1,2)
required_version="3.11"

if [ "$(printf '%s\n' "$required_version" "$python_version" | sort -V | head -n1)" != "$required_version" ]; then
    echo "❌ Python $required_version or higher is required. Found: $python_version"
    exit 1
fi

# Install dependencies if requirements.txt is newer than last install
if [ ! -f ".last_install" ] || [ "requirements.txt" -nt ".last_install" ]; then
    echo "📦 Installing Python dependencies..."
    pip3 install -r requirements.txt
    
    echo "🎭 Installing Playwright browsers..."
    playwright install chromium
    playwright install-deps chromium
    
    touch .last_install
fi

# Check if Telegram bot token is configured
if grep -q "your_telegram_bot_token_here" .env; then
    echo "⚠️  Warning: Telegram bot token not configured"
fi

# Check if encryption key is configured
if grep -q "generate_a_32_byte_key_here" .env; then
    echo "🔐 Generating encryption key..."
    encryption_key=$(python3 -c "from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())")
    sed -i "s/generate_a_32_byte_key_here/$encryption_key/" .env
    echo "✅ Encryption key generated"
fi

# Set permissions
chmod +x main.py

# Start the application
echo "🎯 Starting MEXC Trading System..."

if [ "$1" = "dev" ]; then
    echo "🔧 Starting in development mode..."
    python3 -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
elif [ "$1" = "docker" ]; then
    echo "🐳 Starting in Docker mode..."
    python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 --workers 1
else
    echo "🚀 Starting in production mode..."
    python3 -m uvicorn main:app --host 0.0.0.0 --port 8000 --workers 1
fi
