user  nginx;
worker_processes  auto;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';
    access_log  /var/log/nginx/access.log  main;
    error_log   /var/log/nginx/error.log warn;

    sendfile        on;
    keepalive_timeout  65;

    server {
        listen       80;
        server_name  _;

        # Redirect HTTP to HTTPS if certs are present (optional)
        # return 301 https://$host$request_uri;

        location /tv-webhook {
            proxy_pass         http://127.0.0.1:8000/tv-webhook;
            proxy_http_version 1.1;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }

        location /admin/health {
            auth_basic           "Restricted";
            auth_basic_user_file /etc/nginx/.htpasswd;
            proxy_pass           http://127.0.0.1:8000/admin/health;
        }
    }
}

