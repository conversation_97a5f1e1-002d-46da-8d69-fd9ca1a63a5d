"""
Logging configuration for MEXC Trading System
Provides structured logging with file rotation and multiple output formats
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional

import structlog
from structlog.stdlib import LoggerFactory

from src.config import settings


def setup_logging(log_level: Optional[str] = None) -> logging.Logger:
    """
    Setup structured logging for the application
    
    Args:
        log_level: Override log level from settings
        
    Returns:
        Configured logger instance
    """
    # Use provided log level or fall back to settings
    level = log_level or settings.LOG_LEVEL
    
    # Create logs directory if it doesn't exist
    log_file_path = Path(settings.LOG_FILE)
    log_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure standard library logging
    logging.basicConfig(
        level=getattr(logging, level.upper()),
        format="%(message)s",
        handlers=[]
    )
    
    # Create formatters
    if settings.STRUCTURED_LOGGING:
        # Structured logging with JSON output
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
        ]
        
        # Add JSON processor for file output
        file_processors = processors + [structlog.processors.JSONRenderer()]
        console_processors = processors + [structlog.dev.ConsoleRenderer(colors=True)]
        
    else:
        # Simple text logging
        processors = [
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
        ]
        
        file_processors = processors + [
            structlog.processors.KeyValueRenderer(key_order=['timestamp', 'level', 'logger'])
        ]
        console_processors = processors + [structlog.dev.ConsoleRenderer()]
    
    # Configure structlog
    structlog.configure(
        processors=console_processors,
        context_class=dict,
        logger_factory=LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.handlers.clear()
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, level.upper()))
    
    if settings.STRUCTURED_LOGGING:
        console_formatter = logging.Formatter('%(message)s')
    else:
        console_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=settings.LOG_FILE,
        maxBytes=settings.LOG_MAX_SIZE,
        backupCount=settings.LOG_BACKUP_COUNT,
        encoding='utf-8'
    )
    file_handler.setLevel(getattr(logging, level.upper()))
    
    if settings.STRUCTURED_LOGGING:
        file_formatter = logging.Formatter('%(message)s')
    else:
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
    file_handler.setFormatter(file_formatter)
    root_logger.addHandler(file_handler)
    
    # Create application logger
    logger = structlog.get_logger("mexc_trading_system")
    
    # Log startup information
    logger.info(
        "Logging system initialized",
        log_level=level,
        log_file=settings.LOG_FILE,
        structured_logging=settings.STRUCTURED_LOGGING,
        environment=settings.ENVIRONMENT
    )
    
    return logger


def get_logger(name: str) -> structlog.stdlib.BoundLogger:
    """
    Get a logger instance for a specific module
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured logger instance
    """
    return structlog.get_logger(name)


class LoggerMixin:
    """Mixin class to add logging capabilities to any class"""
    
    @property
    def logger(self) -> structlog.stdlib.BoundLogger:
        """Get logger instance for this class"""
        return get_logger(self.__class__.__name__)


# Trading-specific log filters and handlers
class TradingLogFilter(logging.Filter):
    """Filter for trading-related log messages"""
    
    def filter(self, record):
        # Add trading context to log records
        if hasattr(record, 'symbol'):
            record.msg = f"[{record.symbol}] {record.msg}"
        return True


class SensitiveDataFilter(logging.Filter):
    """Filter to remove sensitive data from logs"""
    
    SENSITIVE_KEYS = [
        'password', 'token', 'key', 'secret', 'authorization',
        'x-mxc-sign', 'x-mxc-nonce', 'mtoken', 'p0', 'k0'
    ]
    
    def filter(self, record):
        # Remove sensitive data from log messages
        if hasattr(record, 'msg') and isinstance(record.msg, str):
            for key in self.SENSITIVE_KEYS:
                if key in record.msg.lower():
                    # Replace sensitive values with [REDACTED]
                    import re
                    pattern = rf'("{key}":\s*")[^"]*(")'
                    record.msg = re.sub(pattern, r'\1[REDACTED]\2', record.msg, flags=re.IGNORECASE)
        
        return True


def setup_trading_logger() -> structlog.stdlib.BoundLogger:
    """Setup specialized logger for trading operations"""
    logger = get_logger("trading")
    
    # Add trading-specific filters
    trading_filter = TradingLogFilter()
    sensitive_filter = SensitiveDataFilter()
    
    # Apply filters to all handlers
    for handler in logging.getLogger().handlers:
        handler.addFilter(trading_filter)
        handler.addFilter(sensitive_filter)
    
    return logger


def log_trade_execution(
    logger: structlog.stdlib.BoundLogger,
    action: str,
    symbol: str,
    side: str,
    quantity: float,
    price: Optional[float] = None,
    order_id: Optional[str] = None,
    status: str = "pending",
    **kwargs
):
    """
    Log trade execution with standardized format
    
    Args:
        logger: Logger instance
        action: Trade action (buy, sell, close)
        symbol: Trading symbol
        side: Trade side (long, short)
        quantity: Trade quantity
        price: Trade price (optional)
        order_id: Order ID (optional)
        status: Trade status
        **kwargs: Additional context
    """
    logger.info(
        f"Trade {action.upper()}",
        symbol=symbol,
        side=side,
        quantity=quantity,
        price=price,
        order_id=order_id,
        status=status,
        **kwargs
    )


def log_session_event(
    logger: structlog.stdlib.BoundLogger,
    event: str,
    session_id: str,
    status: str = "info",
    **kwargs
):
    """
    Log session-related events with standardized format
    
    Args:
        logger: Logger instance
        event: Event description
        session_id: Session identifier
        status: Event status
        **kwargs: Additional context
    """
    logger.info(
        f"Session {event}",
        session_id=session_id,
        status=status,
        **kwargs
    )
