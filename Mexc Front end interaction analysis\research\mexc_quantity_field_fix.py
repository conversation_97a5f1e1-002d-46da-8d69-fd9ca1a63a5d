#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Quantity Field Fix
Specifically target the "Quantity (USDT)" field, not the Price field.

TARGET: The input field below "Quantity (USDT)" label (ignore the dropdown arrow)
GOAL: Fill quantity field → Click Open Long/Short button
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class MEXCQuantityFieldFix:
    """Fix to target the correct Quantity (USDT) field"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"🎯 QUANTITY FIELD FIX: {side} {quantity} {symbol}")
    
    def connect(self):
        """Connect to MEXC"""
        self.logger.info("🔌 Connecting...")
        
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
        
        context = self.browser.contexts[0]
        for page in context.pages:
            if 'mexc.com' in (page.url or '') and 'testnet' not in (page.url or ''):
                self.page = page
                break
        
        if not self.page:
            self.logger.error("❌ MEXC page not found")
            return False
        
        self.logger.info(f"✅ Connected: {self.page.url}")
        return True
    
    def find_and_fill_quantity_field(self):
        """Find the specific Quantity (USDT) field and fill it"""
        self.logger.info("🎯 FINDING QUANTITY (USDT) FIELD...")
        
        quantity_field_script = f"""
        () => {{
            console.log('🎯 Looking for Quantity (USDT) field specifically...');
            
            const testValue = '{self.quantity}';
            const results = {{
                quantity_field_found: false,
                field_filled: false,
                field_info: null,
                search_results: []
            }};
            
            // METHOD 1: Look for "Quantity" text labels and find input fields near them
            console.log('Method 1: Looking for "Quantity" labels...');
            
            const allElements = document.querySelectorAll('*');
            const quantityLabels = [];
            
            for (const element of allElements) {{
                const text = element.textContent || '';
                if (text.includes('Quantity') && text.includes('USDT')) {{
                    quantityLabels.push({{
                        element: element,
                        text: text.trim(),
                        position: {{
                            x: Math.round(element.getBoundingClientRect().x),
                            y: Math.round(element.getBoundingClientRect().y)
                        }}
                    }});
                    console.log(`Found Quantity label: "${{text.trim()}}" at (${{element.getBoundingClientRect().x}}, ${{element.getBoundingClientRect().y}})`);
                }}
            }}
            
            results.search_results.push({{
                method: 'quantity_labels',
                found: quantityLabels.length,
                labels: quantityLabels.map(l => ({{ text: l.text, position: l.position }}))
            }});
            
            // For each quantity label, look for input fields nearby
            for (const labelInfo of quantityLabels) {{
                console.log(`Searching near label: "${{labelInfo.text}}"`);
                
                // Look in the same parent container
                let parent = labelInfo.element.parentElement;
                let depth = 0;
                
                while (parent && depth < 5) {{
                    const inputs = parent.querySelectorAll('input');
                    
                    for (const input of inputs) {{
                        const rect = input.getBoundingClientRect();
                        
                        // Check if input is visible and not disabled
                        if (rect.width > 0 && rect.height > 0 && !input.disabled) {{
                            console.log(`Testing input near Quantity label:`);
                            console.log(`  Type: ${{input.type || 'text'}}`);
                            console.log(`  Placeholder: "${{input.placeholder || ''}}"`);
                            console.log(`  Value: "${{input.value || ''}}"`);
                            console.log(`  Class: "${{input.className || ''}}"`);
                            console.log(`  Position: (${{Math.round(rect.x)}}, ${{Math.round(rect.y)}})`);
                            
                            // Skip if it's clearly a price field
                            const placeholder = (input.placeholder || '').toLowerCase();
                            const className = (input.className || '').toLowerCase();
                            
                            if (placeholder.includes('price') || className.includes('price')) {{
                                console.log(`  ❌ Skipping - appears to be price field`);
                                continue;
                            }}
                            
                            try {{
                                console.log(`  🎯 Attempting to fill this field...`);
                                
                                // Focus the input
                                input.focus();
                                
                                // Clear any existing value
                                input.value = '';
                                
                                // Set the quantity value
                                input.value = testValue;
                                
                                // Trigger events to make it register
                                input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                input.dispatchEvent(new Event('blur', {{ bubbles: true }}));
                                
                                // Verify it worked
                                const finalValue = input.value;
                                console.log(`  Result: "${{finalValue}}" (expected: "${{testValue}}")`);
                                
                                if (finalValue === testValue) {{
                                    results.quantity_field_found = true;
                                    results.field_filled = true;
                                    results.field_info = {{
                                        type: input.type || 'text',
                                        placeholder: input.placeholder || '',
                                        className: input.className || '',
                                        value: finalValue,
                                        position: {{
                                            x: Math.round(rect.x),
                                            y: Math.round(rect.y)
                                        }},
                                        label_text: labelInfo.text,
                                        label_position: labelInfo.position
                                    }};
                                    
                                    console.log(`✅ SUCCESS! Quantity field filled: "${{finalValue}}"`);
                                    console.log(`✅ Field position: (${{Math.round(rect.x)}}, ${{Math.round(rect.y)}})`);
                                    console.log(`✅ Associated label: "${{labelInfo.text}}"`);
                                    
                                    return results; // Exit immediately on success
                                }} else {{
                                    console.log(`  ❌ Value didn't stick: "${{finalValue}}"`);
                                }}
                                
                            }} catch (error) {{
                                console.log(`  ❌ Error filling field: ${{error.message}}`);
                            }}
                        }}
                    }}
                    
                    parent = parent.parentElement;
                    depth++;
                }}
            }}
            
            // METHOD 2: If no quantity labels found, look for empty input fields in trading area
            if (!results.field_filled) {{
                console.log('Method 2: Looking for empty input fields in trading area...');
                
                const tradingContainers = document.querySelectorAll('[class*="trading"], [class*="order"], [class*="trade"]');
                
                for (const container of tradingContainers) {{
                    const inputs = container.querySelectorAll('input.ant-input');
                    
                    for (const input of inputs) {{
                        const rect = input.getBoundingClientRect();
                        
                        if (rect.width > 0 && rect.height > 0 && !input.disabled && input.value === '') {{
                            console.log(`Testing empty input in trading container:`);
                            console.log(`  Placeholder: "${{input.placeholder || ''}}"`);
                            console.log(`  Class: "${{input.className || ''}}"`);
                            console.log(`  Position: (${{Math.round(rect.x)}}, ${{Math.round(rect.y)}})`);
                            
                            // Skip price fields
                            const placeholder = (input.placeholder || '').toLowerCase();
                            if (placeholder.includes('price')) {{
                                console.log(`  ❌ Skipping price field`);
                                continue;
                            }}
                            
                            try {{
                                input.focus();
                                input.value = testValue;
                                input.dispatchEvent(new Event('input', {{ bubbles: true }}));
                                input.dispatchEvent(new Event('change', {{ bubbles: true }}));
                                
                                if (input.value === testValue) {{
                                    results.quantity_field_found = true;
                                    results.field_filled = true;
                                    results.field_info = {{
                                        type: input.type || 'text',
                                        placeholder: input.placeholder || '',
                                        className: input.className || '',
                                        value: input.value,
                                        position: {{
                                            x: Math.round(rect.x),
                                            y: Math.round(rect.y)
                                        }},
                                        method: 'empty_field_in_trading_area'
                                    }};
                                    
                                    console.log(`✅ SUCCESS! Empty field filled: "${{input.value}}"`);
                                    return results;
                                }}
                            }} catch (error) {{
                                console.log(`  ❌ Error: ${{error.message}}`);
                            }}
                        }}
                    }}
                }}
            }}
            
            console.log('❌ No suitable quantity field found');
            return results;
        }}
        """
        
        try:
            result = self.page.evaluate(quantity_field_script)
            
            if result.get('field_filled'):
                field_info = result.get('field_info', {})
                
                self.logger.info("🎯 QUANTITY FIELD FOUND AND FILLED!")
                self.logger.info(f"   Position: {field_info.get('position', {})}")
                self.logger.info(f"   Value: '{field_info.get('value', '')}'")
                self.logger.info(f"   Placeholder: '{field_info.get('placeholder', '')}'")
                self.logger.info(f"   Class: '{field_info.get('className', '')}'")
                
                if 'label_text' in field_info:
                    self.logger.info(f"   Associated label: '{field_info.get('label_text', '')}'")
                    self.logger.info(f"   Label position: {field_info.get('label_position', {})}")
                
                return True
            else:
                self.logger.error("❌ QUANTITY FIELD NOT FOUND OR NOT FILLED")
                
                # Show search results
                search_results = result.get('search_results', [])
                for search in search_results:
                    method = search.get('method', 'unknown')
                    found = search.get('found', 0)
                    self.logger.info(f"   {method}: {found} items found")
                    
                    if method == 'quantity_labels':
                        for label in search.get('labels', []):
                            self.logger.info(f"     Label: '{label.get('text', '')}' at {label.get('position', {})}")
                
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Quantity field search error: {e}")
            return False
    
    def click_trade_button(self):
        """Click the Open Long/Short button"""
        self.logger.info(f"🔘 CLICKING {self.side} BUTTON...")
        
        # Determine button class
        if self.side == "BUY":
            button_class = "component_longBtn__eazYU"
            button_text = "Open Long"
        else:
            button_class = "component_shortBtn__x5P3I"
            button_text = "Open Short"
        
        button_script = f"""
        () => {{
            console.log('🔘 Looking for {button_text} button...');
            
            const results = {{
                button_found: false,
                button_clicked: false,
                dom_changes: 0,
                error_messages: []
            }};
            
            // Find the button
            const button = document.querySelector('button.{button_class}');
            
            if (!button) {{
                console.log('❌ Button not found with class selector, trying text search...');
                
                const allButtons = document.querySelectorAll('button');
                for (const btn of allButtons) {{
                    const text = btn.textContent?.trim() || '';
                    if (text.includes('Open') && text.includes('{"Long" if self.side == "BUY" else "Short"}')) {{
                        button = btn;
                        console.log(`✅ Found button by text: "${{text}}"`);
                        break;
                    }}
                }}
                
                if (!button) {{
                    return {{ success: false, error: 'Button not found' }};
                }}
            }}
            
            results.button_found = true;
            console.log(`Button found: "${{button.textContent}}" at (${{button.getBoundingClientRect().x}}, ${{button.getBoundingClientRect().y}})`);
            
            // Record DOM state before click
            const beforeModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
            const beforeNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
            
            try {{
                // Click the button
                button.focus();
                button.click();
                
                results.button_clicked = true;
                console.log('✅ Button clicked');
                
                // Wait for response
                setTimeout(() => {{
                    const afterModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
                    const afterNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
                    
                    results.dom_changes = (afterModals - beforeModals) + (afterNotifications - beforeNotifications);
                    console.log(`DOM changes: ${{results.dom_changes}}`);
                    
                    // Check for error messages
                    const errorElements = document.querySelectorAll('.ant-message-error, .error, [class*="error"]');
                    for (const errorEl of errorElements) {{
                        const errorText = errorEl.textContent || '';
                        if (errorText.toLowerCase().includes('quantity') || 
                            errorText.toLowerCase().includes('amount') || 
                            errorText.toLowerCase().includes('enter')) {{
                            results.error_messages.push(errorText);
                            console.log(`❌ Error: ${{errorText}}`);
                        }}
                    }}
                    
                    if (results.dom_changes > 0) {{
                        console.log('✅ Button click triggered UI response');
                    }} else if (results.error_messages.length === 0) {{
                        console.log('⚠️ Button clicked but no visible response');
                    }}
                    
                }}, 2000);
                
            }} catch (error) {{
                console.log(`❌ Button click error: ${{error.message}}`);
                results.error_messages.push(error.message);
            }}
            
            return {{
                success: true,
                results: results
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(button_script)
            
            # Wait for results
            time.sleep(3)
            
            if result.get('success'):
                results_data = result.get('results', {})
                
                self.logger.info("🔘 BUTTON CLICK RESULTS:")
                self.logger.info(f"   Button found: {results_data.get('button_found', False)}")
                self.logger.info(f"   Button clicked: {results_data.get('button_clicked', False)}")
                self.logger.info(f"   DOM changes: {results_data.get('dom_changes', 0)}")
                
                error_messages = results_data.get('error_messages', [])
                if error_messages:
                    self.logger.error("   Error messages:")
                    for error in error_messages:
                        self.logger.error(f"     - {error}")
                    return False
                else:
                    self.logger.info("   ✅ No error messages")
                    return results_data.get('button_clicked', False)
            else:
                self.logger.error("❌ Button click failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Button click error: {e}")
            return False
    
    def execute_quantity_fix(self):
        """Execute the quantity field fix"""
        self.logger.info("🎯 EXECUTING QUANTITY FIELD FIX...")
        
        try:
            # Connect
            if not self.connect():
                return False
            
            # Find and fill quantity field
            if not self.find_and_fill_quantity_field():
                return False
            
            # Wait a moment for value to settle
            time.sleep(1)
            
            # Click trade button
            if not self.click_trade_button():
                return False
            
            self.logger.info("🎉 QUANTITY FIELD FIX SUCCESSFUL!")
            return True
            
        except Exception as e:
            self.logger.error(f"Quantity fix error: {e}")
            return False
    
    def cleanup(self):
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="MEXC Quantity Field Fix")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
🎯 MEXC QUANTITY FIELD FIX
==========================
TARGET: Fill the "Quantity (USDT)" field (not Price field)
IGNORE: Dropdown arrow - just fill the input field below it
GOAL: Fill quantity → Click {args.side} button

SEARCH STRATEGY:
1. Look for "Quantity (USDT)" labels
2. Find input fields near those labels
3. Skip any price-related fields
4. Fill the correct quantity field

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    fix = MEXCQuantityFieldFix(args.symbol, args.side, args.quantity)
    
    try:
        success = fix.execute_quantity_fix()
        
        if success:
            print("\n🎉 QUANTITY FIELD FIX SUCCESSFUL!")
            print("Quantity field filled and trade button clicked.")
        else:
            print("\n❌ QUANTITY FIELD FIX FAILED!")
            print("Could not find or fill the Quantity (USDT) field.")
            
    except KeyboardInterrupt:
        print("\n👋 Fix interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        fix.cleanup()

if __name__ == "__main__":
    main()
