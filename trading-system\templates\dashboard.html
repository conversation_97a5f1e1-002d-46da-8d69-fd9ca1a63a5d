{% extends "base.html" %}

{% block title %}Dashboard - MEXC Trading System{% endblock %}
{% block page_title %}System Dashboard{% endblock %}

{% block content %}
<div id="auto-refresh">
    <!-- System Status Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <i class="fas fa-server fa-2x mb-2"></i>
                    <h5 class="card-title">System Status</h5>
                    <h3 class="mb-0">
                        <span class="badge bg-light text-dark">
                            {{ system_status.status.upper() }}
                        </span>
                    </h3>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <i class="fas fa-globe fa-2x mb-2"></i>
                    <h5 class="card-title">Active Sessions</h5>
                    <h3 class="mb-0">{{ session_stats.healthy_sessions or 0 }}/{{ session_stats.total_sessions or 0 }}</h3>
                    <small>Healthy/Total</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <i class="fas fa-exchange-alt fa-2x mb-2"></i>
                    <h5 class="card-title">Active Trades</h5>
                    <h3 class="mb-0">{{ trading_stats.active_trades or 0 }}</h3>
                    <small>Currently executing</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card metric-card">
                <div class="card-body text-center">
                    <i class="fas fa-chart-line fa-2x mb-2"></i>
                    <h5 class="card-title">Success Rate</h5>
                    <h3 class="mb-0">{{ "%.1f"|format((trading_stats.success_rate or 0) * 100) }}%</h3>
                    <small>Trade success rate</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-globe"></i>
                        Session Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="status-healthy">{{ session_stats.authenticated_sessions or 0 }}</h4>
                                <small class="text-muted">Authenticated</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <h4 class="text-primary">{{ "%.2f"|format(session_stats.average_health_score or 0) }}</h4>
                                <small class="text-muted">Avg Health Score</small>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar"
                                 style="width: {{ ((session_stats.healthy_sessions or 0) / ((session_stats.total_sessions or 1) if (session_stats.total_sessions or 1) > 0 else 1) * 100)|round }}%">
                            </div>
                        </div>
                        <small class="text-muted">Session Health</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i>
                        Trading Performance
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="text-primary">{{ trading_stats.total_trades or 0 }}</h4>
                                <small class="text-muted">Total</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="status-healthy">{{ trading_stats.successful_trades or 0 }}</h4>
                                <small class="text-muted">Success</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="status-error">{{ trading_stats.failed_trades or 0 }}</h4>
                                <small class="text-muted">Failed</small>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="progress">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ ((trading_stats.success_rate or 0) * 100)|round }}%">
                            </div>
                        </div>
                        <small class="text-muted">Success Rate</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- MEXC API and Bot Control -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-robot"></i>
                        Bot Control
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Trading Bot Status:</span>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="botToggle" onchange="toggleBot()">
                            <label class="form-check-label" for="botToggle" id="botStatus">
                                Loading...
                            </label>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6">
                            <h6 class="text-muted">Current Symbol</h6>
                            <span class="badge bg-primary" id="currentSymbol">Loading...</span>
                        </div>
                        <div class="col-6">
                            <h6 class="text-muted">Leverage</h6>
                            <span class="badge bg-info" id="currentLeverage">Loading...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-key"></i>
                        MEXC API Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>API Connection:</span>
                        <span class="badge bg-secondary" id="apiStatus">Checking...</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>API Enabled:</span>
                        <span class="badge bg-secondary" id="apiEnabled">Loading...</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Credentials:</span>
                        <span class="badge bg-secondary" id="apiCredentials">Loading...</span>
                    </div>
                    <hr>
                    <button class="btn btn-sm btn-outline-primary" onclick="testMexcConnection()">
                        <i class="fas fa-plug"></i> Test Connection
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="refreshAccountInfo()">
                        <i class="fas fa-sync"></i> Refresh Account
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Information -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-wallet"></i>
                        Spot Account
                    </h5>
                </div>
                <div class="card-body">
                    <div id="spotAccountInfo">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading account information...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-area"></i>
                        Futures Account
                    </h5>
                </div>
                <div class="card-body">
                    <div id="futuresAccountInfo">
                        <div class="text-center text-muted">
                            <i class="fas fa-spinner fa-spin"></i>
                            Loading futures information...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i>
                        System Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Version:</strong></td>
                                    <td>{{ system_status.version }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Environment:</strong></td>
                                    <td>
                                        <span class="badge bg-{% if system_status.environment == 'production' %}danger{% else %}warning{% endif %}">
                                            {{ system_status.environment.upper() }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Max Concurrent Trades:</strong></td>
                                    <td>{{ settings.max_concurrent_trades }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Session Pool Size:</strong></td>
                                    <td>{{ settings.session_pool_size }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Supported Symbols:</strong></td>
                                    <td>{{ settings.supported_symbols|length }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td id="last-updated">{{ system_status.timestamp[:19] if system_status.timestamp else 'N/A' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-coins"></i>
                        Supported Symbols
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex flex-wrap">
                        {% for symbol in settings.supported_symbols %}
                        <span class="badge bg-secondary me-1 mb-1">{{ symbol }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="/dashboard/sessions" class="btn btn-primary btn-lg w-100 mb-2">
                                <i class="fas fa-globe"></i><br>
                                Manage Sessions
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/dashboard/trades" class="btn btn-success btn-lg w-100 mb-2">
                                <i class="fas fa-exchange-alt"></i><br>
                                View Trades
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="/api/status" target="_blank" class="btn btn-info btn-lg w-100 mb-2">
                                <i class="fas fa-heartbeat"></i><br>
                                API Status
                            </a>
                        </div>
                        <div class="col-md-3">
                            <button onclick="testWebhook()" class="btn btn-warning btn-lg w-100 mb-2">
                                <i class="fas fa-vial"></i><br>
                                Test Webhook
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Update last updated timestamp
    document.getElementById('last-updated').textContent = new Date().toLocaleString();
    
    // Test webhook function
    async function testWebhook() {
        const testData = {
            action: "buy",
            symbol: "BTC_USDT",
            side: "long",
            quantity: 0.001,
            price: 45000,
            leverage: 1,
            order_type: "limit"
        };
        
        try {
            const response = await fetch('/webhook/test', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(testData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert('Test webhook successful: ' + result.message);
            } else {
                alert('Test webhook failed: ' + result.message);
            }
        } catch (error) {
            alert('Test webhook error: ' + error.message);
        }
    }

    // Load system configuration and account info
    async function loadSystemConfig() {
        try {
            const response = await fetch('/dashboard/api/system-config');
            const data = await response.json();

            if (data.success) {
                const config = data.config;

                // Update bot status
                const botToggle = document.getElementById('botToggle');
                const botStatus = document.getElementById('botStatus');

                if (config.bot_enabled) {
                    botToggle.checked = true;
                    botStatus.textContent = 'Enabled';
                    botStatus.className = 'form-check-label text-success';
                } else {
                    botToggle.checked = false;
                    botStatus.textContent = 'Disabled';
                    botStatus.className = 'form-check-label text-danger';
                }

                // Update current settings
                document.getElementById('currentSymbol').textContent = config.trading_symbol;
                document.getElementById('currentLeverage').textContent = config.leverage + 'x';

                // Update API status
                const apiStatus = document.getElementById('apiStatus');
                const apiEnabled = document.getElementById('apiEnabled');
                const apiCredentials = document.getElementById('apiCredentials');

                if (config.mexc_api_enabled) {
                    apiEnabled.textContent = 'Yes';
                    apiEnabled.className = 'badge bg-success';
                } else {
                    apiEnabled.textContent = 'No';
                    apiEnabled.className = 'badge bg-secondary';
                }

                if (config.mexc_api_key_configured && config.mexc_api_secret_configured) {
                    apiCredentials.textContent = 'Configured';
                    apiCredentials.className = 'badge bg-success';
                } else {
                    apiCredentials.textContent = 'Not Set';
                    apiCredentials.className = 'badge bg-warning';
                }
            }
        } catch (error) {
            console.error('Failed to load system config:', error);
        }
    }

    // Toggle bot status
    async function toggleBot() {
        const botToggle = document.getElementById('botToggle');
        const isEnabled = botToggle.checked;

        try {
            const response = await fetch('/config/bot-control', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    bot_enabled: isEnabled
                })
            });

            const data = await response.json();

            if (data.success) {
                const botStatus = document.getElementById('botStatus');
                if (isEnabled) {
                    botStatus.textContent = 'Enabled';
                    botStatus.className = 'form-check-label text-success';
                } else {
                    botStatus.textContent = 'Disabled';
                    botStatus.className = 'form-check-label text-danger';
                }

                // Show success message
                showAlert(data.message, 'success');
            } else {
                // Revert toggle on failure
                botToggle.checked = !isEnabled;
                showAlert('Failed to update bot status', 'danger');
            }
        } catch (error) {
            // Revert toggle on error
            botToggle.checked = !isEnabled;
            showAlert('Error updating bot status', 'danger');
        }
    }

    // Test MEXC connection
    async function testMexcConnection() {
        try {
            const response = await fetch('/dashboard/api/test-mexc-connection');
            const data = await response.json();

            const apiStatus = document.getElementById('apiStatus');

            if (data.success && data.connection_test.status === 'connected') {
                apiStatus.textContent = 'Connected';
                apiStatus.className = 'badge bg-success';
                showAlert('MEXC API connection successful!', 'success');
            } else {
                apiStatus.textContent = 'Failed';
                apiStatus.className = 'badge bg-danger';
                showAlert('MEXC API connection failed: ' + (data.connection_test?.error || 'Unknown error'), 'danger');
            }
        } catch (error) {
            const apiStatus = document.getElementById('apiStatus');
            apiStatus.textContent = 'Error';
            apiStatus.className = 'badge bg-danger';
            showAlert('Error testing MEXC connection', 'danger');
        }
    }

    // Refresh account information
    async function refreshAccountInfo() {
        try {
            const response = await fetch('/dashboard/api/account-info');
            const data = await response.json();

            const spotDiv = document.getElementById('spotAccountInfo');
            const futuresDiv = document.getElementById('futuresAccountInfo');

            if (data.success) {
                // Update spot account
                if (data.spot_account && data.spot_account.balances && data.spot_account.balances.length > 0) {
                    let spotHtml = '<div class="table-responsive"><table class="table table-sm">';
                    data.spot_account.balances.forEach(balance => {
                        if (balance.total > 0) {
                            spotHtml += `
                                <tr>
                                    <td><strong>${balance.asset}</strong></td>
                                    <td class="text-end">${balance.total.toFixed(8)}</td>
                                </tr>
                            `;
                        }
                    });
                    spotHtml += '</table></div>';
                    spotDiv.innerHTML = spotHtml;
                } else {
                    spotDiv.innerHTML = '<div class="text-center text-muted">No balances or API not configured</div>';
                }

                // Update futures account
                if (data.futures_account && data.futures_account.total_wallet_balance !== undefined) {
                    let futuresHtml = `
                        <div class="row text-center">
                            <div class="col-6">
                                <h6 class="text-muted">Wallet Balance</h6>
                                <h5>${data.futures_account.total_wallet_balance.toFixed(2)} USDT</h5>
                            </div>
                            <div class="col-6">
                                <h6 class="text-muted">Available</h6>
                                <h5>${data.futures_account.available_balance.toFixed(2)} USDT</h5>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h6 class="text-muted">Unrealized PnL</h6>
                            <h5 class="${data.futures_account.total_unrealized_pnl >= 0 ? 'text-success' : 'text-danger'}">
                                ${data.futures_account.total_unrealized_pnl.toFixed(2)} USDT
                            </h5>
                        </div>
                    `;
                    futuresDiv.innerHTML = futuresHtml;
                } else {
                    futuresDiv.innerHTML = '<div class="text-center text-muted">Limited API support for futures</div>';
                }

                showAlert('Account information refreshed', 'success');
            } else {
                spotDiv.innerHTML = '<div class="alert alert-warning">Failed to load account info: ' + data.error + '</div>';
                futuresDiv.innerHTML = '<div class="alert alert-warning">Failed to load futures info</div>';
            }
        } catch (error) {
            document.getElementById('spotAccountInfo').innerHTML = '<div class="alert alert-danger">Error loading account info</div>';
            document.getElementById('futuresAccountInfo').innerHTML = '<div class="alert alert-danger">Error loading futures info</div>';
        }
    }

    // Show alert message
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alertDiv);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);
    }

    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        loadSystemConfig();
        refreshAccountInfo();

        // Auto-refresh every 30 seconds
        setInterval(() => {
            loadSystemConfig();
            refreshAccountInfo();
        }, 30000);
    });
</script>
{% endblock %}
