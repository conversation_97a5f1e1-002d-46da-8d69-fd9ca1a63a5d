#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Synchronized Trade Execution System
PROBLEM: Button click happens but MEXC validation occurs after value is cleared
SOLUTION: Synchronized execution that ensures value is present during validation
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class SynchronizedTradeExecution:
    """Synchronized system that coordinates value persistence with button clicks"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler(f'synchronized_trade_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"SYNCHRONIZED TRADE EXECUTION: {side} {quantity} {symbol}")
    
    def connect_to_mexc(self):
        """Connect to MEXC browser tab"""
        self.logger.info("Connecting to MEXC...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                url = page.url or ''
                if 'mexc.com' in url and 'testnet' not in url:
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("MEXC page not found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"Connected to MEXC: {self.page.url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Connection failed: {e}")
            return False
    
    def setup_synchronized_system(self):
        """Setup synchronized execution system"""
        self.logger.info("Setting up synchronized execution system...")
        
        sync_script = f"""
        () => {{
            console.log('Setting up synchronized trade execution...');
            
            const TARGET_VALUE = '{self.quantity}';
            const SIDE = '{self.side}';
            
            // Global synchronized execution state
            window.mexcSyncExecution = {{
                quantityField: null,
                tradeButton: null,
                monitoring: false,
                executing: false,
                restorations: 0,
                validationAttempts: 0,
                lastValidation: null,
                monitorInterval: null
            }};
            
            const sync = window.mexcSyncExecution;
            
            // Step 1: Find quantity field
            function findQuantityField() {{
                const inputs = document.querySelectorAll('input.ant-input');
                
                for (const input of inputs) {{
                    const rect = input.getBoundingClientRect();
                    if (Math.abs(rect.x - 668) < 10 && Math.abs(rect.y - 603) < 50) {{
                        return input;
                    }}
                }}
                
                // Fallback search
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {{
                    const text = element.textContent || '';
                    if (text.includes('Quantity') && text.includes('USDT')) {{
                        let parent = element.parentElement;
                        let depth = 0;
                        
                        while (parent && depth < 5) {{
                            const inputs = parent.querySelectorAll('input.ant-input');
                            for (const input of inputs) {{
                                const rect = input.getBoundingClientRect();
                                if (rect.width > 0 && rect.height > 0) {{
                                    return input;
                                }}
                            }}
                            parent = parent.parentElement;
                            depth++;
                        }}
                    }}
                }}
                
                return null;
            }}
            
            // Step 2: Find trade button
            function findTradeButton() {{
                const buttonClass = SIDE === 'BUY' ? 'component_longBtn__eazYU' : 'component_shortBtn__x5P3I';
                return document.querySelector(`button.${{buttonClass}}`);
            }}
            
            // Step 3: Advanced value restoration
            function restoreValueAdvanced(input) {{
                try {{
                    // Method 1: Focus and direct assignment
                    input.focus();
                    input.select();
                    input.value = '';
                    input.value = TARGET_VALUE;
                    
                    // Method 2: React/Vue override
                    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    nativeInputValueSetter.call(input, TARGET_VALUE);
                    
                    // Method 3: Attribute setting
                    input.setAttribute('value', TARGET_VALUE);
                    
                    // Method 4: Character-by-character typing simulation
                    input.value = '';
                    for (let i = 0; i < TARGET_VALUE.length; i++) {{
                        const char = TARGET_VALUE[i];
                        input.value += char;
                        input.dispatchEvent(new KeyboardEvent('keydown', {{ key: char, bubbles: true }}));
                        input.dispatchEvent(new KeyboardEvent('keypress', {{ key: char, bubbles: true }}));
                        input.dispatchEvent(new KeyboardEvent('keyup', {{ key: char, bubbles: true }}));
                    }}
                    
                    // Method 5: Comprehensive event triggering
                    const events = ['input', 'change', 'blur', 'focus'];
                    events.forEach(eventType => {{
                        input.dispatchEvent(new Event(eventType, {{ bubbles: true }}));
                    }});
                    
                    // Method 6: Custom events for React/Vue
                    input.dispatchEvent(new CustomEvent('input', {{ bubbles: true, detail: {{ value: TARGET_VALUE }} }}));
                    input.dispatchEvent(new CustomEvent('change', {{ bubbles: true, detail: {{ value: TARGET_VALUE }} }}));
                    
                    sync.restorations++;
                    console.log(`Advanced restoration #${{sync.restorations}}: "${{input.value}}"`);
                    
                    return input.value === TARGET_VALUE;
                    
                }} catch (error) {{
                    console.log(`Advanced restoration error: ${{error.message}}`);
                    return false;
                }}
            }}
            
            // Step 4: Pre-click validation and restoration
            function preClickValidation() {{
                if (!sync.quantityField) return false;
                
                const currentValue = sync.quantityField.value;
                sync.validationAttempts++;
                sync.lastValidation = Date.now();
                
                console.log(`Pre-click validation #${{sync.validationAttempts}}: "${{currentValue}}"`);
                
                if (currentValue !== TARGET_VALUE) {{
                    console.log('Value missing before click - performing emergency restoration...');
                    
                    // Emergency restoration with multiple attempts
                    for (let attempt = 1; attempt <= 3; attempt++) {{
                        const success = restoreValueAdvanced(sync.quantityField);
                        console.log(`Emergency restoration attempt ${{attempt}}: ${{success ? 'SUCCESS' : 'FAILED'}}`);
                        
                        if (success) break;
                        
                        // Brief pause between attempts
                        const start = Date.now();
                        while (Date.now() - start < 10) {{}} // 10ms busy wait
                    }}
                }}
                
                return sync.quantityField.value === TARGET_VALUE;
            }}
            
            // Step 5: Synchronized click execution
            function executeSynchronizedClick() {{
                if (sync.executing) {{
                    console.log('Already executing, skipping...');
                    return {{ success: false, error: 'Already executing' }};
                }}
                
                sync.executing = true;
                
                try {{
                    // Pre-click validation
                    const validationSuccess = preClickValidation();
                    if (!validationSuccess) {{
                        return {{ 
                            success: false, 
                            error: `Pre-click validation failed: "${{sync.quantityField ? sync.quantityField.value : 'null'}}"` 
                        }};
                    }}
                    
                    // Verify button exists
                    if (!sync.tradeButton) {{
                        return {{ success: false, error: 'Trade button not found' }};
                    }}
                    
                    // Record state before click
                    const quantityBeforeClick = sync.quantityField.value;
                    const beforeModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
                    const beforeNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
                    
                    console.log(`Executing synchronized click with quantity: "${{quantityBeforeClick}}"`);
                    
                    // Execute click with immediate post-click restoration
                    sync.tradeButton.focus();
                    sync.tradeButton.click();
                    
                    // Immediate post-click restoration (within same event loop)
                    restoreValueAdvanced(sync.quantityField);
                    
                    // Schedule additional restorations
                    setTimeout(() => restoreValueAdvanced(sync.quantityField), 1);
                    setTimeout(() => restoreValueAdvanced(sync.quantityField), 5);
                    setTimeout(() => restoreValueAdvanced(sync.quantityField), 10);
                    setTimeout(() => restoreValueAdvanced(sync.quantityField), 25);
                    setTimeout(() => restoreValueAdvanced(sync.quantityField), 50);
                    
                    console.log('Synchronized click executed');
                    
                    // Check for responses after brief delay
                    setTimeout(() => {{
                        const afterModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
                        const afterNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
                        const quantityAfterClick = sync.quantityField.value;
                        
                        const modalChange = afterModals - beforeModals;
                        const notificationChange = afterNotifications - beforeNotifications;
                        
                        console.log(`Post-click state: quantity="${{quantityAfterClick}}", modals=${{modalChange}}, notifications=${{notificationChange}}`);
                        
                        // Check for error messages
                        const errorElements = document.querySelectorAll('.ant-message-error, .error, [class*="error"], .ant-notification-notice-error');
                        const errors = [];
                        for (const errorEl of errorElements) {{
                            const errorText = errorEl.textContent || '';
                            if (errorText.toLowerCase().includes('quantity') || 
                                errorText.toLowerCase().includes('amount') || 
                                errorText.toLowerCase().includes('enter')) {{
                                errors.push(errorText.trim());
                            }}
                        }}
                        
                        if (errors.length > 0) {{
                            console.log(`ERRORS DETECTED: ${{errors.join(', ')}}`);
                        }} else {{
                            console.log('No quantity-related errors detected');
                        }}
                        
                        // Check for success indicators
                        const successElements = document.querySelectorAll('.ant-message-success, .success, [class*="success"], .ant-notification-notice-success');
                        const successes = [];
                        for (const successEl of successElements) {{
                            const successText = successEl.textContent || '';
                            if (successText.trim()) {{
                                successes.push(successText.trim());
                            }}
                        }}
                        
                        if (successes.length > 0) {{
                            console.log(`SUCCESS INDICATORS: ${{successes.join(', ')}}`);
                        }}
                        
                    }}, 500);
                    
                    return {{
                        success: true,
                        quantity_before_click: quantityBeforeClick,
                        button_text: sync.tradeButton.textContent,
                        validation_attempts: sync.validationAttempts,
                        total_restorations: sync.restorations
                    }};
                    
                }} catch (error) {{
                    return {{ success: false, error: error.message }};
                }} finally {{
                    sync.executing = false;
                }}
            }}
            
            // Step 6: Continuous monitoring during execution
            function continuousMonitoring() {{
                if (!sync.monitoring || !sync.quantityField) return;
                
                const currentValue = sync.quantityField.value;
                if (currentValue !== TARGET_VALUE && !sync.executing) {{
                    restoreValueAdvanced(sync.quantityField);
                }}
            }}
            
            // Initialize system
            sync.quantityField = findQuantityField();
            sync.tradeButton = findTradeButton();
            
            if (!sync.quantityField) {{
                return {{ success: false, error: 'Quantity field not found' }};
            }}
            
            if (!sync.tradeButton) {{
                return {{ success: false, error: 'Trade button not found' }};
            }}
            
            // Initial value setting
            restoreValueAdvanced(sync.quantityField);
            
            // Start monitoring
            sync.monitoring = true;
            sync.monitorInterval = setInterval(continuousMonitoring, 25); // Every 25ms
            
            // Expose synchronized click function
            window.executeSynchronizedTrade = executeSynchronizedClick;
            
            console.log('Synchronized execution system ready');
            
            return {{
                success: true,
                field_position: {{
                    x: Math.round(sync.quantityField.getBoundingClientRect().x),
                    y: Math.round(sync.quantityField.getBoundingClientRect().y)
                }},
                button_text: sync.tradeButton.textContent,
                initial_value: sync.quantityField.value,
                monitor_interval: 25
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(sync_script)
            
            if result.get('success'):
                field_position = result.get('field_position', {})
                button_text = result.get('button_text', '')
                initial_value = result.get('initial_value', '')
                monitor_interval = result.get('monitor_interval', 0)
                
                self.logger.info("SYNCHRONIZED SYSTEM SETUP SUCCESS:")
                self.logger.info(f"   Field position: {field_position}")
                self.logger.info(f"   Button text: '{button_text}'")
                self.logger.info(f"   Initial value: '{initial_value}'")
                self.logger.info(f"   Monitor interval: {monitor_interval}ms")
                
                return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Synchronized system setup failed: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Synchronized system setup error: {e}")
            return False
    
    def execute_synchronized_trade_with_retry(self, max_retries=3):
        """Execute synchronized trade with retry mechanism"""
        self.logger.info(f"Executing synchronized trade with up to {max_retries} retries...")
        
        for attempt in range(1, max_retries + 1):
            self.logger.info(f"ATTEMPT {attempt}/{max_retries}")
            
            # Wait a moment between attempts
            if attempt > 1:
                time.sleep(2)
            
            execute_script = """
            () => {
                if (typeof window.executeSynchronizedTrade === 'function') {
                    return window.executeSynchronizedTrade();
                } else {
                    return { success: false, error: 'Synchronized trade function not available' };
                }
            }
            """
            
            try:
                result = self.page.evaluate(execute_script)
                
                if result.get('success'):
                    quantity_before = result.get('quantity_before_click', '')
                    button_text = result.get('button_text', '')
                    validation_attempts = result.get('validation_attempts', 0)
                    total_restorations = result.get('total_restorations', 0)
                    
                    self.logger.info(f"SYNCHRONIZED TRADE ATTEMPT {attempt} SUCCESS:")
                    self.logger.info(f"   Quantity before click: '{quantity_before}'")
                    self.logger.info(f"   Button clicked: '{button_text}'")
                    self.logger.info(f"   Validation attempts: {validation_attempts}")
                    self.logger.info(f"   Total restorations: {total_restorations}")
                    
                    # Wait for response and check for errors
                    time.sleep(3)
                    
                    error_check = self.check_for_errors()
                    if not error_check.get('has_quantity_error', False):
                        self.logger.info(f"SUCCESS: No quantity errors detected on attempt {attempt}")
                        return True
                    else:
                        errors = error_check.get('errors', [])
                        self.logger.warning(f"Attempt {attempt} failed with errors: {errors}")
                        if attempt < max_retries:
                            self.logger.info("Retrying...")
                            continue
                else:
                    error = result.get('error', 'Unknown error')
                    self.logger.error(f"Attempt {attempt} failed: {error}")
                    if attempt < max_retries:
                        self.logger.info("Retrying...")
                        continue
                        
            except Exception as e:
                self.logger.error(f"Attempt {attempt} error: {e}")
                if attempt < max_retries:
                    self.logger.info("Retrying...")
                    continue
        
        self.logger.error(f"All {max_retries} attempts failed")
        return False
    
    def check_for_errors(self):
        """Check for error messages after trade execution"""
        error_check_script = """
        () => {
            const errorSelectors = [
                '.ant-message-error',
                '.ant-notification-notice-error',
                '.error',
                '[class*="error"]',
                '.ant-form-item-explain-error'
            ];
            
            const errors = [];
            let hasQuantityError = false;
            
            for (const selector of errorSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    const text = element.textContent || '';
                    if (text.trim()) {
                        errors.push(text.trim());
                        
                        if (text.toLowerCase().includes('quantity') || 
                            text.toLowerCase().includes('amount') || 
                            text.toLowerCase().includes('enter')) {
                            hasQuantityError = true;
                        }
                    }
                }
            }
            
            // Also check for success messages
            const successSelectors = [
                '.ant-message-success',
                '.ant-notification-notice-success',
                '.success',
                '[class*="success"]'
            ];
            
            const successes = [];
            for (const selector of successSelectors) {
                const elements = document.querySelectorAll(selector);
                for (const element of elements) {
                    const text = element.textContent || '';
                    if (text.trim()) {
                        successes.push(text.trim());
                    }
                }
            }
            
            return {
                errors: errors,
                successes: successes,
                has_quantity_error: hasQuantityError,
                total_errors: errors.length,
                total_successes: successes.length
            };
        }
        """
        
        try:
            return self.page.evaluate(error_check_script)
        except Exception as e:
            self.logger.error(f"Error check failed: {e}")
            return {'has_quantity_error': True, 'errors': [str(e)]}
    
    def stop_monitoring(self):
        """Stop the monitoring system"""
        stop_script = """
        () => {
            if (window.mexcSyncExecution && window.mexcSyncExecution.monitorInterval) {
                clearInterval(window.mexcSyncExecution.monitorInterval);
                window.mexcSyncExecution.monitoring = false;
                
                return {
                    success: true,
                    total_restorations: window.mexcSyncExecution.restorations,
                    validation_attempts: window.mexcSyncExecution.validationAttempts
                };
            }
            
            return { success: false, error: 'Monitor not found' };
        }
        """
        
        try:
            result = self.page.evaluate(stop_script)
            if result.get('success'):
                self.logger.info("MONITORING STOPPED:")
                self.logger.info(f"   Total restorations: {result.get('total_restorations', 0)}")
                self.logger.info(f"   Validation attempts: {result.get('validation_attempts', 0)}")
        except Exception as e:
            self.logger.error(f"Stop monitoring error: {e}")
    
    def execute_complete_synchronized_trade(self):
        """Execute complete synchronized trade with all safeguards"""
        self.logger.info("EXECUTING COMPLETE SYNCHRONIZED TRADE")
        self.logger.info("="*60)
        
        try:
            # Step 1: Connect
            if not self.connect_to_mexc():
                return False
            
            # Step 2: Setup synchronized system
            if not self.setup_synchronized_system():
                return False
            
            # Step 3: Wait for system to stabilize
            self.logger.info("Allowing system to stabilize...")
            time.sleep(3)
            
            # Step 4: Execute synchronized trade with retry
            success = self.execute_synchronized_trade_with_retry(max_retries=5)
            
            # Step 5: Final error check
            if success:
                time.sleep(2)
                final_check = self.check_for_errors()
                
                self.logger.info("FINAL RESULT CHECK:")
                self.logger.info(f"   Errors: {final_check.get('errors', [])}")
                self.logger.info(f"   Successes: {final_check.get('successes', [])}")
                self.logger.info(f"   Has quantity error: {final_check.get('has_quantity_error', False)}")
                
                if final_check.get('has_quantity_error', False):
                    self.logger.error("FINAL CHECK: Still has quantity errors")
                    success = False
                else:
                    self.logger.info("FINAL CHECK: No quantity errors detected")
            
            # Step 6: Stop monitoring
            self.stop_monitoring()
            
            self.logger.info("="*60)
            if success:
                self.logger.info("SYNCHRONIZED TRADE EXECUTION: SUCCESS")
            else:
                self.logger.error("SYNCHRONIZED TRADE EXECUTION: FAILED")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Complete synchronized trade error: {e}")
            return False
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Synchronized Trade Execution")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
SYNCHRONIZED TRADE EXECUTION
============================
PROBLEM: Button click validation occurs after value clearing
SOLUTION: Synchronized execution with pre/post-click restoration

FEATURES:
1. Pre-click validation and emergency restoration
2. Immediate post-click restoration (same event loop)
3. Multiple restoration methods (6 different approaches)
4. Retry mechanism (up to 5 attempts)
5. Comprehensive error detection
6. 25ms continuous monitoring

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    executor = SynchronizedTradeExecution(args.symbol, args.side, args.quantity)
    
    try:
        success = executor.execute_complete_synchronized_trade()
        
        print("\n" + "="*60)
        if success:
            print("SUCCESS: Synchronized trade execution completed")
            print("Check MEXC interface for trade confirmation")
        else:
            print("FAILED: All synchronization attempts failed")
            print("Check logs for detailed error analysis")
        print("="*60)
        
    except KeyboardInterrupt:
        print("\nExecution interrupted")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
    finally:
        executor.cleanup()

if __name__ == "__main__":
    main()
