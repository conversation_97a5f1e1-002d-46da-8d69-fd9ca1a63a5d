"""
Configuration Management API Routes
==================================

API routes for managing system configuration including:
- MEXC API credentials
- Money management settings
- Bot control
- Trading parameters
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, validator
from typing import Dict, Any, Optional, List
from datetime import datetime
import os
import json

from src.config import settings
from src.utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter()


class MEXCAPIConfig(BaseModel):
    """MEXC API configuration model"""
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    api_enabled: bool = False


class MoneyManagementConfig(BaseModel):
    """Money management configuration model"""
    trading_symbol: str = "TRU_USDT"
    leverage: int = 1
    position_size_type: str = "percentage"  # "percentage" or "fixed"
    position_size_percentage: float = 50.0
    position_size_fixed: float = 100.0
    max_position_amount: float = 100.0
    use_max_position_limit: bool = True
    
    @validator("leverage")
    def validate_leverage(cls, v):
        if v < 1 or v > 100:
            raise ValueError("Leverage must be between 1 and 100")
        return v
    
    @validator("position_size_type")
    def validate_position_size_type(cls, v):
        if v not in ["percentage", "fixed"]:
            raise ValueError("Position size type must be 'percentage' or 'fixed'")
        return v
    
    @validator("position_size_percentage")
    def validate_position_size_percentage(cls, v):
        if v < 0 or v > 100:
            raise ValueError("Position size percentage must be between 0 and 100")
        return v
    
    @validator("position_size_fixed")
    def validate_position_size_fixed(cls, v):
        if v < 0:
            raise ValueError("Position size fixed amount must be positive")
        return v


class BotControlConfig(BaseModel):
    """Bot control configuration model"""
    bot_enabled: bool = True


class TradingConfig(BaseModel):
    """Trading configuration model"""
    max_concurrent_trades: int = 5
    risk_management_enabled: bool = True
    max_position_size: float = 1000.0
    supported_symbols: List[str] = ["TRU_USDT", "ETH_USDT", "BNB_USDT"]
    stop_loss_enabled: bool = True
    take_profit_enabled: bool = True
    
    @validator("max_concurrent_trades")
    def validate_max_concurrent_trades(cls, v):
        if v < 1 or v > 20:
            raise ValueError("Max concurrent trades must be between 1 and 20")
        return v


class NotificationConfig(BaseModel):
    """Notification configuration model"""
    telegram_enabled: bool = False
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None


class SystemConfigUpdate(BaseModel):
    """Complete system configuration update model"""
    mexc_api: Optional[MEXCAPIConfig] = None
    money_management: Optional[MoneyManagementConfig] = None
    bot_control: Optional[BotControlConfig] = None
    trading: Optional[TradingConfig] = None
    notifications: Optional[NotificationConfig] = None


@router.get("/current")
async def get_current_config():
    """Get current system configuration"""
    try:
        return {
            "success": True,
            "config": {
                "mexc_api": {
                    "api_key": "***" if settings.MEXC_API_KEY else None,
                    "api_secret": "***" if settings.MEXC_API_SECRET else None,
                    "api_enabled": settings.MEXC_API_ENABLED
                },
                "money_management": {
                    "trading_symbol": settings.TRADING_SYMBOL,
                    "leverage": settings.LEVERAGE,
                    "position_size_type": settings.POSITION_SIZE_TYPE,
                    "position_size_percentage": settings.POSITION_SIZE_PERCENTAGE,
                    "position_size_fixed": settings.POSITION_SIZE_FIXED,
                    "max_position_amount": settings.MAX_POSITION_AMOUNT,
                    "use_max_position_limit": settings.USE_MAX_POSITION_LIMIT
                },
                "bot_control": {
                    "bot_enabled": settings.BOT_ENABLED
                },
                "trading": {
                    "max_concurrent_trades": settings.MAX_CONCURRENT_TRADES,
                    "risk_management_enabled": settings.RISK_MANAGEMENT_ENABLED,
                    "max_position_size": settings.MAX_POSITION_SIZE,
                    "supported_symbols": settings.SUPPORTED_SYMBOLS,
                    "stop_loss_enabled": settings.STOP_LOSS_ENABLED,
                    "take_profit_enabled": settings.TAKE_PROFIT_ENABLED
                },
                "notifications": {
                    "telegram_enabled": settings.TELEGRAM_ENABLED,
                    "telegram_bot_token": "***" if settings.TELEGRAM_BOT_TOKEN else None,
                    "telegram_chat_id": settings.TELEGRAM_CHAT_ID
                }
            },
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get current config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/update")
async def update_config(config_update: SystemConfigUpdate):
    """Update system configuration"""
    try:
        updated_fields = []
        
        # Update MEXC API configuration
        if config_update.mexc_api:
            if config_update.mexc_api.api_key is not None:
                settings.MEXC_API_KEY = config_update.mexc_api.api_key
                updated_fields.append("mexc_api_key")
            
            if config_update.mexc_api.api_secret is not None:
                settings.MEXC_API_SECRET = config_update.mexc_api.api_secret
                updated_fields.append("mexc_api_secret")
            
            settings.MEXC_API_ENABLED = config_update.mexc_api.api_enabled
            updated_fields.append("mexc_api_enabled")
        
        # Update money management configuration
        if config_update.money_management:
            settings.TRADING_SYMBOL = config_update.money_management.trading_symbol
            settings.LEVERAGE = config_update.money_management.leverage
            settings.POSITION_SIZE_TYPE = config_update.money_management.position_size_type
            settings.POSITION_SIZE_PERCENTAGE = config_update.money_management.position_size_percentage
            settings.POSITION_SIZE_FIXED = config_update.money_management.position_size_fixed
            settings.MAX_POSITION_AMOUNT = config_update.money_management.max_position_amount
            settings.USE_MAX_POSITION_LIMIT = config_update.money_management.use_max_position_limit
            updated_fields.extend([
                "trading_symbol", "leverage", "position_size_type", 
                "position_size_percentage", "position_size_fixed", 
                "max_position_amount", "use_max_position_limit"
            ])
        
        # Update bot control
        if config_update.bot_control:
            settings.BOT_ENABLED = config_update.bot_control.bot_enabled
            updated_fields.append("bot_enabled")
        
        # Update trading configuration
        if config_update.trading:
            settings.MAX_CONCURRENT_TRADES = config_update.trading.max_concurrent_trades
            settings.RISK_MANAGEMENT_ENABLED = config_update.trading.risk_management_enabled
            settings.MAX_POSITION_SIZE = config_update.trading.max_position_size
            settings.SUPPORTED_SYMBOLS = config_update.trading.supported_symbols
            settings.STOP_LOSS_ENABLED = config_update.trading.stop_loss_enabled
            settings.TAKE_PROFIT_ENABLED = config_update.trading.take_profit_enabled
            updated_fields.extend([
                "max_concurrent_trades", "risk_management_enabled", 
                "max_position_size", "supported_symbols", 
                "stop_loss_enabled", "take_profit_enabled"
            ])
        
        # Update notification configuration
        if config_update.notifications:
            settings.TELEGRAM_ENABLED = config_update.notifications.telegram_enabled
            if config_update.notifications.telegram_bot_token is not None:
                settings.TELEGRAM_BOT_TOKEN = config_update.notifications.telegram_bot_token
                updated_fields.append("telegram_bot_token")
            if config_update.notifications.telegram_chat_id is not None:
                settings.TELEGRAM_CHAT_ID = config_update.notifications.telegram_chat_id
                updated_fields.append("telegram_chat_id")
            updated_fields.append("telegram_enabled")
        
        # Save configuration to environment file
        await save_config_to_env()
        
        logger.info(f"Configuration updated: {', '.join(updated_fields)}")
        
        return {
            "success": True,
            "message": "Configuration updated successfully",
            "updated_fields": updated_fields,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to update config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/mexc-api")
async def update_mexc_api_config(mexc_config: MEXCAPIConfig):
    """Update MEXC API configuration"""
    try:
        if mexc_config.api_key is not None:
            settings.MEXC_API_KEY = mexc_config.api_key
        
        if mexc_config.api_secret is not None:
            settings.MEXC_API_SECRET = mexc_config.api_secret
        
        settings.MEXC_API_ENABLED = mexc_config.api_enabled
        
        # Save to environment file
        await save_config_to_env()
        
        # Test connection if credentials provided
        connection_test = None
        if mexc_config.api_enabled and settings.MEXC_API_KEY and settings.MEXC_API_SECRET:
            try:
                from src.integrations.mexc_api import test_mexc_api
                connection_test = await test_mexc_api()
            except Exception as e:
                logger.warning(f"MEXC API connection test failed: {e}")
                connection_test = {"status": "failed", "error": str(e)}
        
        return {
            "success": True,
            "message": "MEXC API configuration updated",
            "connection_test": connection_test,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to update MEXC API config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/money-management")
async def update_money_management_config(mm_config: MoneyManagementConfig):
    """Update money management configuration"""
    try:
        settings.TRADING_SYMBOL = mm_config.trading_symbol
        settings.LEVERAGE = mm_config.leverage
        settings.POSITION_SIZE_TYPE = mm_config.position_size_type
        settings.POSITION_SIZE_PERCENTAGE = mm_config.position_size_percentage
        settings.POSITION_SIZE_FIXED = mm_config.position_size_fixed
        settings.MAX_POSITION_AMOUNT = mm_config.max_position_amount
        settings.USE_MAX_POSITION_LIMIT = mm_config.use_max_position_limit
        
        # Save to environment file
        await save_config_to_env()
        
        return {
            "success": True,
            "message": "Money management configuration updated",
            "config": mm_config.dict(),
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to update money management config: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bot-control")
async def update_bot_control(bot_config: BotControlConfig):
    """Update bot control configuration"""
    try:
        settings.BOT_ENABLED = bot_config.bot_enabled
        
        # Save to environment file
        await save_config_to_env()
        
        status_message = "Bot enabled" if bot_config.bot_enabled else "Bot disabled"
        
        return {
            "success": True,
            "message": status_message,
            "bot_enabled": bot_config.bot_enabled,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to update bot control: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def save_config_to_env():
    """Save current configuration to .env file"""
    try:
        env_file_path = ".env"
        
        # Read current .env file
        env_vars = {}
        if os.path.exists(env_file_path):
            with open(env_file_path, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value
        
        # Update with current settings
        env_vars.update({
            "MEXC_API_KEY": settings.MEXC_API_KEY or "",
            "MEXC_API_SECRET": settings.MEXC_API_SECRET or "",
            "MEXC_API_ENABLED": str(settings.MEXC_API_ENABLED).lower(),
            "BOT_ENABLED": str(settings.BOT_ENABLED).lower(),
            "TRADING_SYMBOL": settings.TRADING_SYMBOL,
            "LEVERAGE": str(settings.LEVERAGE),
            "POSITION_SIZE_TYPE": settings.POSITION_SIZE_TYPE,
            "POSITION_SIZE_PERCENTAGE": str(settings.POSITION_SIZE_PERCENTAGE),
            "POSITION_SIZE_FIXED": str(settings.POSITION_SIZE_FIXED),
            "MAX_POSITION_AMOUNT": str(settings.MAX_POSITION_AMOUNT),
            "USE_MAX_POSITION_LIMIT": str(settings.USE_MAX_POSITION_LIMIT).lower(),
            "TELEGRAM_ENABLED": str(settings.TELEGRAM_ENABLED).lower(),
            "TELEGRAM_BOT_TOKEN": settings.TELEGRAM_BOT_TOKEN or "",
            "TELEGRAM_CHAT_ID": settings.TELEGRAM_CHAT_ID or ""
        })
        
        # Write back to .env file
        with open(env_file_path, 'w') as f:
            for key, value in env_vars.items():
                f.write(f"{key}={value}\n")
        
        logger.info("Configuration saved to .env file")
        
    except Exception as e:
        logger.error(f"Failed to save config to .env file: {e}")
        raise
