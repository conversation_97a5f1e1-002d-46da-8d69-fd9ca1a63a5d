#!/usr/bin/env python3
"""Quick diagnostic to check current page elements"""

from playwright.sync_api import sync_playwright
import time

def main():
    playwright = sync_playwright().start()
    browser = playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
    context = browser.contexts[0]

    for page in context.pages:
        if 'mexc.com' in (page.url or ''):
            print(f'Found MEXC page: {page.url}')
            
            # Check for quantity input fields
            selectors = [
                'input[placeholder*="quantity"]',
                'input[placeholder*="amount"]',
                'input[type="number"]',
                '.ant-input-number-input',
                'input'
            ]
            
            # Get all input elements and analyze them
            try:
                all_inputs = page.locator('input').all()
                print(f'\nFound {len(all_inputs)} input elements:')

                for i, el in enumerate(all_inputs):
                    if el.is_visible():
                        placeholder = el.get_attribute('placeholder') or ''
                        value = el.input_value() or ''
                        classes = el.get_attribute('class') or ''
                        input_type = el.get_attribute('type') or ''
                        parent_text = ''

                        try:
                            # Get parent element text for context
                            parent = el.locator('xpath=..')
                            parent_text = parent.text_content()[:50] if parent.text_content() else ''
                        except:
                            pass

                        print(f'  Input {i+1}:')
                        print(f'    Type: {input_type}')
                        print(f'    Placeholder: "{placeholder}"')
                        print(f'    Value: "{value}"')
                        print(f'    Classes: "{classes}"')
                        print(f'    Parent context: "{parent_text}"')
                        print()

            except Exception as e:
                print(f'Error analyzing inputs: {e}')

            # Also check for buttons
            try:
                buy_buttons = page.locator('button:has-text("Buy"), button:has-text("Long"), button:has-text("Open")').all()
                print(f'\nFound {len(buy_buttons)} potential buy buttons:')

                for i, btn in enumerate(buy_buttons[:5]):
                    if btn.is_visible():
                        text = btn.text_content() or ''
                        classes = btn.get_attribute('class') or ''
                        print(f'  Button {i+1}: "{text}" - classes: "{classes[:50]}"')

            except Exception as e:
                print(f'Error analyzing buttons: {e}')

            break

    playwright.stop()

if __name__ == "__main__":
    main()
