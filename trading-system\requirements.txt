# Web Framework
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
jinja2>=3.1.0
python-multipart>=0.0.6

# Browser Automation
playwright>=1.40.0

# HTTP Clients
httpx>=0.25.0
aiohttp>=3.9.0
requests>=2.31.0

# Database & Storage
sqlalchemy>=2.0.0
aiosqlite>=0.19.0

# Async & Concurrency
aiofiles>=23.0.0

# Configuration & Environment
python-dotenv>=1.0.0
pydantic>=2.5.0
pydantic-settings>=2.1.0

# Telegram Bot
python-telegram-bot>=20.0

# Encryption & Security
cryptography>=41.0.0

# Logging & Monitoring
structlog>=23.0.0

# Utilities
python-dateutil>=2.8.0
pytz>=2023.0

# Performance
orjson>=3.9.0
