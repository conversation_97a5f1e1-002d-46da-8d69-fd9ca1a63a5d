"""
Telegram Bot integration for MEXC Trading System
Provides notifications and alerts for trading events and system status
"""

import asyncio
import json
from datetime import datetime
from typing import Optional, Dict, Any, List
from enum import Enum

import httpx
from telegram import Bot, Update
from telegram.ext import Application, CommandHandler, ContextTypes

from src.config import settings
from src.utils.logger import get_logger


class NotificationLevel(Enum):
    """Notification priority levels"""
    INFO = "ℹ️"
    SUCCESS = "✅"
    WARNING = "⚠️"
    ERROR = "❌"
    CRITICAL = "🚨"
    TRADE = "💰"
    SESSION = "🔐"


class TelegramBot:
    """
    Telegram bot for system notifications and monitoring
    """
    
    def __init__(self, token: str, chat_id: str):
        """
        Initialize Telegram bot
        
        Args:
            token: Telegram bot token
            chat_id: Target chat ID for notifications
        """
        self.token = token
        self.chat_id = chat_id
        self.bot: Optional[Bot] = None
        self.application: Optional[Application] = None
        self.logger = get_logger(__name__)
        self._connected = False
        self._message_queue: List[Dict[str, Any]] = []
        self._rate_limit_delay = 1.0  # Seconds between messages
        self._last_message_time = 0.0
        
    async def initialize(self) -> bool:
        """
        Initialize the Telegram bot
        
        Returns:
            True if initialization successful
        """
        try:
            self.bot = Bot(token=self.token)
            
            # Test connection
            bot_info = await self.bot.get_me()
            self.logger.info(
                "Telegram bot initialized",
                bot_name=bot_info.username,
                bot_id=bot_info.id
            )
            
            # Setup application for commands
            self.application = Application.builder().token(self.token).build()
            
            # Add command handlers
            self.application.add_handler(CommandHandler("status", self._handle_status_command))
            self.application.add_handler(CommandHandler("trades", self._handle_trades_command))
            self.application.add_handler(CommandHandler("sessions", self._handle_sessions_command))
            self.application.add_handler(CommandHandler("help", self._handle_help_command))
            
            self._connected = True
            
            # Send initialization message
            await self.send_message(
                "🤖 MEXC Trading Bot initialized successfully!\n"
                f"Bot: @{bot_info.username}\n"
                f"Chat ID: {self.chat_id}\n"
                "Type /help for available commands.",
                level=NotificationLevel.SUCCESS
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Telegram bot: {e}")
            self._connected = False
            return False
    
    async def send_message(
        self,
        message: str,
        level: NotificationLevel = NotificationLevel.INFO,
        parse_mode: str = "HTML",
        disable_notification: bool = False,
        **kwargs
    ) -> bool:
        """
        Send a message to the configured chat
        
        Args:
            message: Message text
            level: Notification level
            parse_mode: Message parse mode
            disable_notification: Disable notification sound
            **kwargs: Additional context for formatting
            
        Returns:
            True if message sent successfully
        """
        if not self._connected or not self.bot:
            self.logger.warning("Telegram bot not connected, queuing message")
            self._message_queue.append({
                "message": message,
                "level": level,
                "parse_mode": parse_mode,
                "disable_notification": disable_notification,
                "kwargs": kwargs
            })
            return False
        
        try:
            # Rate limiting
            current_time = asyncio.get_event_loop().time()
            if current_time - self._last_message_time < self._rate_limit_delay:
                await asyncio.sleep(self._rate_limit_delay)
            
            # Format message with level emoji and timestamp
            formatted_message = self._format_message(message, level, **kwargs)
            
            # Send message
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=formatted_message,
                parse_mode=parse_mode,
                disable_notification=disable_notification
            )
            
            self._last_message_time = asyncio.get_event_loop().time()
            
            self.logger.debug(
                "Telegram message sent",
                level=level.name,
                message_length=len(message)
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send Telegram message: {e}")
            return False
    
    async def send_trade_notification(
        self,
        action: str,
        symbol: str,
        side: str,
        quantity: float,
        price: Optional[float] = None,
        order_id: Optional[str] = None,
        status: str = "executed",
        pnl: Optional[float] = None
    ):
        """
        Send trade execution notification
        
        Args:
            action: Trade action (buy, sell, close)
            symbol: Trading symbol
            side: Trade side (long, short)
            quantity: Trade quantity
            price: Execution price
            order_id: Order ID
            status: Trade status
            pnl: Profit/Loss (for close orders)
        """
        emoji_map = {
            "buy": "📈",
            "sell": "📉",
            "close": "🔄"
        }
        
        message = f"{emoji_map.get(action, '💰')} <b>Trade {action.upper()}</b>\n"
        message += f"Symbol: <code>{symbol}</code>\n"
        message += f"Side: <b>{side.upper()}</b>\n"
        message += f"Quantity: <code>{quantity}</code>\n"
        
        if price:
            message += f"Price: <code>${price:.4f}</code>\n"
        
        if order_id:
            message += f"Order ID: <code>{order_id}</code>\n"
        
        message += f"Status: <b>{status.upper()}</b>\n"
        
        if pnl is not None:
            pnl_emoji = "💚" if pnl >= 0 else "❤️"
            message += f"PnL: {pnl_emoji} <code>${pnl:.2f}</code>\n"
        
        message += f"Time: <code>{datetime.now().strftime('%H:%M:%S')}</code>"
        
        await self.send_message(message, level=NotificationLevel.TRADE)
    
    async def send_session_alert(
        self,
        event: str,
        session_id: str,
        status: str = "info",
        expires_in: Optional[int] = None,
        **kwargs
    ):
        """
        Send session-related alert
        
        Args:
            event: Event description
            session_id: Session identifier
            status: Alert status
            expires_in: Time until expiry (seconds)
            **kwargs: Additional context
        """
        level_map = {
            "info": NotificationLevel.SESSION,
            "warning": NotificationLevel.WARNING,
            "error": NotificationLevel.ERROR,
            "critical": NotificationLevel.CRITICAL
        }
        
        message = f"🔐 <b>Session {event}</b>\n"
        message += f"Session: <code>{session_id[:8]}...</code>\n"
        
        if expires_in:
            hours = expires_in // 3600
            minutes = (expires_in % 3600) // 60
            message += f"Expires in: <b>{hours}h {minutes}m</b>\n"
        
        for key, value in kwargs.items():
            if key not in ['level', 'parse_mode']:
                message += f"{key.replace('_', ' ').title()}: <code>{value}</code>\n"
        
        message += f"Time: <code>{datetime.now().strftime('%H:%M:%S')}</code>"
        
        await self.send_message(
            message,
            level=level_map.get(status, NotificationLevel.INFO)
        )
    
    async def send_system_alert(
        self,
        title: str,
        description: str,
        level: NotificationLevel = NotificationLevel.INFO,
        **kwargs
    ):
        """
        Send system status alert
        
        Args:
            title: Alert title
            description: Alert description
            level: Notification level
            **kwargs: Additional context
        """
        message = f"<b>{title}</b>\n"
        message += f"{description}\n"
        
        for key, value in kwargs.items():
            if key not in ['level', 'parse_mode']:
                message += f"{key.replace('_', ' ').title()}: <code>{value}</code>\n"
        
        message += f"Time: <code>{datetime.now().strftime('%H:%M:%S')}</code>"
        
        await self.send_message(message, level=level)
    
    def _format_message(
        self,
        message: str,
        level: NotificationLevel,
        **kwargs
    ) -> str:
        """
        Format message with level emoji and timestamp
        
        Args:
            message: Original message
            level: Notification level
            **kwargs: Additional formatting context
            
        Returns:
            Formatted message
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted = f"{level.value} {message}"
        
        # Add timestamp if not already present
        if "Time:" not in formatted:
            formatted += f"\n⏰ {timestamp}"
        
        return formatted
    
    async def _handle_status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command"""
        try:
            # This would be implemented to get actual system status
            status_message = (
                "🤖 <b>MEXC Trading System Status</b>\n"
                "Status: <b>Running</b>\n"
                "Sessions: <code>3/3 Active</code>\n"
                "Active Trades: <code>2</code>\n"
                "Uptime: <code>2h 15m</code>"
            )
            
            await update.message.reply_text(status_message, parse_mode="HTML")
            
        except Exception as e:
            self.logger.error(f"Error handling status command: {e}")
    
    async def _handle_trades_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /trades command"""
        try:
            trades_message = (
                "💰 <b>Recent Trades</b>\n"
                "1. BTC_USDT LONG +0.1 @ $45,000\n"
                "2. ETH_USDT SHORT -0.5 @ $2,800\n"
                "Total PnL: <b>+$125.50</b>"
            )
            
            await update.message.reply_text(trades_message, parse_mode="HTML")
            
        except Exception as e:
            self.logger.error(f"Error handling trades command: {e}")
    
    async def _handle_sessions_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /sessions command"""
        try:
            sessions_message = (
                "🔐 <b>Session Status</b>\n"
                "Session 1: ✅ Healthy (expires in 4d 12h)\n"
                "Session 2: ✅ Healthy (expires in 4d 8h)\n"
                "Session 3: ⚠️ Warning (expires in 18h)"
            )
            
            await update.message.reply_text(sessions_message, parse_mode="HTML")
            
        except Exception as e:
            self.logger.error(f"Error handling sessions command: {e}")
    
    async def _handle_help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        try:
            help_message = (
                "🤖 <b>MEXC Trading Bot Commands</b>\n\n"
                "/status - System status\n"
                "/trades - Recent trades\n"
                "/sessions - Session status\n"
                "/help - This help message\n\n"
                "The bot automatically sends notifications for:\n"
                "• Trade executions\n"
                "• Session expiry warnings\n"
                "• System alerts\n"
                "• Error notifications"
            )
            
            await update.message.reply_text(help_message, parse_mode="HTML")
            
        except Exception as e:
            self.logger.error(f"Error handling help command: {e}")
    
    async def process_queued_messages(self):
        """Process any queued messages"""
        if not self._connected or not self._message_queue:
            return
        
        messages_to_send = self._message_queue.copy()
        self._message_queue.clear()
        
        for msg_data in messages_to_send:
            await self.send_message(**msg_data)
    
    def is_connected(self) -> bool:
        """Check if bot is connected"""
        return self._connected
    
    async def shutdown(self):
        """Shutdown the Telegram bot"""
        try:
            if self.application:
                await self.application.shutdown()
            
            self._connected = False
            self.logger.info("Telegram bot shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during Telegram bot shutdown: {e}")
