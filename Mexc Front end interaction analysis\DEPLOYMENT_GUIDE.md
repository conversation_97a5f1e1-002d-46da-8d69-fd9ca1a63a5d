# MEXC Futures Automation - Deployment Guide
## 🚀 **Production-Ready Deployment**

This guide covers deploying the working MEXC futures automation solution in production environments, including webhook integration, session management, and high-frequency trading setup.

## 📋 **QUICK START**

### **1. Basic Setup**
```bash
# 1. Install dependencies
pip install playwright

# 2. Start Chrome with remote debugging
chrome.exe --remote-debugging-port=9222 --user-data-dir=temp-profile

# 3. Open MEXC futures page
# Navigate to: https://www.mexc.com/futures/TRU_USDT

# 4. Run automation
python mexc_futures_automation.py --side BUY --quantity 2.5 --symbol TRU_USDT
```

### **2. Verify Success**
✅ **Check MEXC Interface:**
- New position opened
- Order execution confirmation  
- Balance changes
- No "please enter quantity" errors

## 🔧 **PRODUCTION DEPLOYMENT**

### **1. Docker Container Setup**
```dockerfile
# Dockerfile
FROM python:3.11-slim

# Install Chrome
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    unzip \
    && wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | apt-key add - \
    && echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" >> /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable

# Install Python dependencies
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy automation scripts
COPY mexc_futures_automation.py .
COPY webhook_server.py .

# Start Chrome with remote debugging
CMD ["sh", "-c", "google-chrome --remote-debugging-port=9222 --no-sandbox --disable-dev-shm-usage --headless & python webhook_server.py"]
```

### **2. Webhook Integration Server**
```python
# webhook_server.py
from flask import Flask, request, jsonify
import subprocess
import logging
from datetime import datetime

app = Flask(__name__)
logging.basicConfig(level=logging.INFO)

@app.route('/webhook/mexc-trade', methods=['POST'])
def handle_trade_webhook():
    """Handle incoming trade signals"""
    try:
        data = request.json
        
        # Validate required fields
        required_fields = ['symbol', 'side', 'quantity']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Extract trade parameters
        symbol = data['symbol']
        side = data['side'].upper()
        quantity = str(data['quantity'])
        
        # Optional parameters
        price = data.get('price')
        order_type = data.get('orderType', 'MARKET')
        
        logging.info(f"Received trade signal: {side} {quantity} {symbol}")
        
        # Execute automation
        cmd = [
            'python', 'mexc_futures_automation.py',
            '--symbol', symbol,
            '--side', side,
            '--quantity', quantity
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            logging.info(f"Trade executed successfully: {side} {quantity} {symbol}")
            return jsonify({
                'success': True,
                'message': f'Trade executed: {side} {quantity} {symbol}',
                'timestamp': datetime.now().isoformat()
            })
        else:
            logging.error(f"Trade execution failed: {result.stderr}")
            return jsonify({
                'success': False,
                'error': result.stderr,
                'timestamp': datetime.now().isoformat()
            }), 500
            
    except Exception as e:
        logging.error(f"Webhook error: {e}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

### **3. Session Management System**
```python
# session_manager.py
import time
import logging
from datetime import datetime, timedelta
from playwright.sync_api import sync_playwright

class MexcSessionManager:
    """Manages MEXC login sessions and handles 5-day expiry"""
    
    def __init__(self, telegram_bot_token=None, telegram_chat_id=None):
        self.telegram_bot_token = telegram_bot_token
        self.telegram_chat_id = telegram_chat_id
        self.last_session_check = None
        self.session_expires_at = None
        
    def check_session_status(self):
        """Check if MEXC session is still valid"""
        try:
            playwright = sync_playwright().start()
            browser = playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not browser.contexts:
                return False
                
            context = browser.contexts[0]
            
            for page in context.pages:
                if 'mexc.com' in page.url:
                    # Check for login indicators
                    login_required = page.evaluate("""
                        () => {
                            // Check for login button or login form
                            const loginButton = document.querySelector('.login-btn, [href*="login"]');
                            const loginForm = document.querySelector('.login-form, .ant-form');
                            const userInfo = document.querySelector('.user-info, .account-info');
                            
                            return {
                                loginRequired: !!(loginButton || loginForm),
                                userLoggedIn: !!userInfo,
                                currentUrl: window.location.href
                            };
                        }
                    """)
                    
                    if login_required['loginRequired'] and not login_required['userLoggedIn']:
                        self.send_telegram_notification("🚨 MEXC Session Expired - Login Required")
                        return False
                    
                    return True
                    
            return False
            
        except Exception as e:
            logging.error(f"Session check error: {e}")
            return False
        finally:
            try:
                playwright.stop()
            except:
                pass
    
    def send_telegram_notification(self, message):
        """Send Telegram notification about session status"""
        if not self.telegram_bot_token or not self.telegram_chat_id:
            return
            
        try:
            import requests
            
            url = f"https://api.telegram.org/bot{self.telegram_bot_token}/sendMessage"
            data = {
                'chat_id': self.telegram_chat_id,
                'text': f"{message}\n\nTime: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                'parse_mode': 'HTML'
            }
            
            response = requests.post(url, data=data, timeout=10)
            if response.status_code == 200:
                logging.info("Telegram notification sent successfully")
            else:
                logging.error(f"Telegram notification failed: {response.text}")
                
        except Exception as e:
            logging.error(f"Telegram notification error: {e}")
    
    def monitor_session(self, check_interval_hours=6):
        """Monitor session status continuously"""
        while True:
            try:
                session_valid = self.check_session_status()
                
                if session_valid:
                    logging.info("MEXC session is valid")
                    self.last_session_check = datetime.now()
                else:
                    logging.warning("MEXC session is invalid - manual login required")
                    self.send_telegram_notification("⚠️ MEXC Session Invalid - Please Login")
                
                # Sleep for specified interval
                time.sleep(check_interval_hours * 3600)
                
            except Exception as e:
                logging.error(f"Session monitoring error: {e}")
                time.sleep(300)  # Wait 5 minutes before retry
```

### **4. High-Frequency Trading Setup**
```python
# high_frequency_trader.py
import asyncio
import logging
from datetime import datetime
from mexc_futures_automation import MexcFuturesAutomation

class HighFrequencyTrader:
    """High-frequency trading system for MEXC (30 signals/day)"""
    
    def __init__(self):
        self.trade_queue = asyncio.Queue()
        self.active_trades = {}
        self.daily_trade_count = 0
        self.last_reset_date = datetime.now().date()
        
    async def add_trade_signal(self, signal):
        """Add trade signal to queue"""
        await self.trade_queue.put(signal)
        logging.info(f"Trade signal queued: {signal}")
    
    async def process_trade_queue(self):
        """Process trade signals from queue"""
        while True:
            try:
                # Reset daily counter if new day
                current_date = datetime.now().date()
                if current_date > self.last_reset_date:
                    self.daily_trade_count = 0
                    self.last_reset_date = current_date
                    logging.info("Daily trade counter reset")
                
                # Check daily limit
                if self.daily_trade_count >= 30:
                    logging.warning("Daily trade limit reached (30/day)")
                    await asyncio.sleep(3600)  # Wait 1 hour
                    continue
                
                # Get next trade signal
                signal = await self.trade_queue.get()
                
                # Execute trade with retry mechanism
                success = await self.execute_trade_with_retry(signal)
                
                if success:
                    self.daily_trade_count += 1
                    logging.info(f"Trade executed successfully. Daily count: {self.daily_trade_count}/30")
                else:
                    logging.error(f"Trade execution failed: {signal}")
                
                # Rate limiting - minimum 2 seconds between trades
                await asyncio.sleep(2)
                
            except Exception as e:
                logging.error(f"Trade processing error: {e}")
                await asyncio.sleep(5)
    
    async def execute_trade_with_retry(self, signal, max_retries=3):
        """Execute trade with retry mechanism"""
        for attempt in range(1, max_retries + 1):
            try:
                automation = MexcFuturesAutomation(
                    symbol=signal['symbol'],
                    side=signal['side'],
                    quantity=signal['quantity']
                )
                
                success = automation.run_automation()
                automation.cleanup()
                
                if success:
                    return True
                else:
                    logging.warning(f"Trade attempt {attempt} failed, retrying...")
                    await asyncio.sleep(attempt * 2)  # Exponential backoff
                    
            except Exception as e:
                logging.error(f"Trade execution error (attempt {attempt}): {e}")
                await asyncio.sleep(attempt * 2)
        
        return False

# Usage
async def main():
    trader = HighFrequencyTrader()
    
    # Start trade processing
    asyncio.create_task(trader.process_trade_queue())
    
    # Example: Add trade signals
    await trader.add_trade_signal({
        'symbol': 'TRU_USDT',
        'side': 'BUY',
        'quantity': 2.5
    })
    
    # Keep running
    while True:
        await asyncio.sleep(1)

if __name__ == "__main__":
    asyncio.run(main())
```

## 🔒 **SECURITY CONSIDERATIONS**

### **1. API Security**
```python
# Secure webhook endpoint
from functools import wraps
import hmac
import hashlib

def verify_webhook_signature(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        signature = request.headers.get('X-Signature')
        if not signature:
            return jsonify({'error': 'Missing signature'}), 401
        
        payload = request.get_data()
        expected_signature = hmac.new(
            WEBHOOK_SECRET.encode(),
            payload,
            hashlib.sha256
        ).hexdigest()
        
        if not hmac.compare_digest(signature, expected_signature):
            return jsonify({'error': 'Invalid signature'}), 401
        
        return f(*args, **kwargs)
    return decorated_function

@app.route('/webhook/mexc-trade', methods=['POST'])
@verify_webhook_signature
def handle_secure_webhook():
    # Secure webhook handler
    pass
```

### **2. Environment Configuration**
```bash
# .env file
MEXC_WEBHOOK_SECRET=your_webhook_secret_here
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
CHROME_DEBUGGING_PORT=9222
MAX_DAILY_TRADES=30
TRADE_RETRY_ATTEMPTS=3
SESSION_CHECK_INTERVAL_HOURS=6
```

## 📊 **MONITORING AND LOGGING**

### **1. Comprehensive Logging**
```python
import logging
from logging.handlers import RotatingFileHandler

# Setup rotating log files
handler = RotatingFileHandler(
    'mexc_automation.log',
    maxBytes=10*1024*1024,  # 10MB
    backupCount=5
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        handler,
        logging.StreamHandler()
    ]
)
```

### **2. Performance Metrics**
```python
import time
from collections import defaultdict

class PerformanceMonitor:
    def __init__(self):
        self.metrics = defaultdict(list)
    
    def record_trade_execution_time(self, duration):
        self.metrics['execution_time'].append(duration)
    
    def record_success_rate(self, success):
        self.metrics['success_rate'].append(1 if success else 0)
    
    def get_daily_stats(self):
        return {
            'avg_execution_time': sum(self.metrics['execution_time']) / len(self.metrics['execution_time']),
            'success_rate': sum(self.metrics['success_rate']) / len(self.metrics['success_rate']) * 100,
            'total_trades': len(self.metrics['success_rate'])
        }
```

## 🚀 **DEPLOYMENT CHECKLIST**

### **Pre-Deployment**
- [ ] Chrome browser installed with remote debugging enabled
- [ ] MEXC account logged in and session active
- [ ] Python dependencies installed (`pip install playwright`)
- [ ] Automation script tested manually
- [ ] Webhook server configured and tested
- [ ] Session management system setup
- [ ] Telegram notifications configured
- [ ] Security measures implemented
- [ ] Logging and monitoring setup

### **Production Deployment**
- [ ] Docker container built and tested
- [ ] Environment variables configured
- [ ] SSL certificates installed for webhook endpoints
- [ ] Rate limiting implemented
- [ ] Error handling and retry mechanisms tested
- [ ] Daily trade limits configured
- [ ] Session monitoring active
- [ ] Performance monitoring enabled
- [ ] Backup and recovery procedures documented

### **Post-Deployment**
- [ ] Monitor logs for errors
- [ ] Verify trade executions in MEXC interface
- [ ] Check Telegram notifications working
- [ ] Monitor performance metrics
- [ ] Test session expiry handling
- [ ] Verify webhook security
- [ ] Document any issues and resolutions

## 🎯 **SUCCESS METRICS**

### **Target Performance**
- **Trade Execution Success Rate**: >95%
- **Average Execution Time**: <10 seconds
- **Daily Trade Capacity**: 30 trades/day
- **Session Uptime**: >99%
- **Error Recovery Time**: <5 minutes

This deployment guide provides everything needed to run the MEXC futures automation solution in production with high reliability and security.
