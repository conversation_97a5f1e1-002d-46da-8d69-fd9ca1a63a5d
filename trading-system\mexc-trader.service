[Unit]
Description=MEXC High-Speed Futures Trading System
After=network.target
Wants=network-online.target

[Service]
Type=simple
User=mexc
Group=mexc
WorkingDirectory=/opt/mexc-trading-system
Environment=PATH=/opt/mexc-trading-system/venv/bin
ExecStart=/opt/mexc-trading-system/venv/bin/python -m uvicorn main:app --host 0.0.0.0 --port 8000
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mexc-trader

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/mexc-trading-system/logs /opt/mexc-trading-system/data /opt/mexc-trading-system/browser_data /opt/mexc-trading-system/backups

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

# Environment variables
Environment=PYTHONPATH=/opt/mexc-trading-system
Environment=ENVIRONMENT=production
Environment=LOG_LEVEL=INFO

[Install]
WantedBy=multi-user.target
