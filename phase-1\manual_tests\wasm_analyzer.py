#!/usr/bin/env python3
"""
WEBASSEMBLY ANALYZER
Find and decompile WASM files from MEXC to crack the signature algorithm
"""

import json
import time
import requests
import base64
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class WasmAnalyzer:
    """Analyze WebAssembly files from MEXC"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🔬 WEBASSEMBLY ANALYZER")
        print("="*30)
        print("🎯 HUNTING FOR WASM FILES AND MODULES")
    
    def setup_browser_connection(self):
        """Setup browser connection"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def scan_for_wasm_files(self):
        """Scan for WebAssembly files and modules"""
        
        print("\n🔍 SCANNING FOR WASM FILES")
        print("="*35)
        
        # Inject comprehensive WASM scanner
        wasm_scan_result = self.page.evaluate("""
            () => {
                const results = {
                    wasmFiles: [],
                    wasmModules: [],
                    wasmInstances: [],
                    wasmMemory: [],
                    networkRequests: []
                };
                
                console.log('🔍 Starting comprehensive WASM scan...');
                
                // 1. Scan network requests for WASM files
                if (window.performance && window.performance.getEntriesByType) {
                    const resources = window.performance.getEntriesByType('resource');
                    for (const resource of resources) {
                        if (resource.name.includes('.wasm') || 
                            resource.name.includes('wasm') ||
                            resource.name.includes('binary') ||
                            (resource.initiatorType === 'fetch' && resource.transferSize > 10000)) {
                            
                            results.wasmFiles.push({
                                url: resource.name,
                                size: resource.transferSize,
                                type: resource.initiatorType,
                                startTime: resource.startTime,
                                duration: resource.duration
                            });
                        }
                    }
                }
                
                // 2. Search for WebAssembly objects in global scope
                function deepSearch(obj, path = '', depth = 0, visited = new Set()) {
                    if (depth > 4 || !obj || visited.has(obj)) return;
                    visited.add(obj);
                    
                    try {
                        for (const key in obj) {
                            try {
                                const value = obj[key];
                                const currentPath = path ? `${path}.${key}` : key;
                                
                                if (value && typeof value === 'object') {
                                    // Check for WebAssembly objects
                                    if (value.constructor) {
                                        const constructorName = value.constructor.name;
                                        
                                        if (constructorName === 'WebAssembly.Module') {
                                            results.wasmModules.push({
                                                path: currentPath,
                                                type: 'Module',
                                                constructor: constructorName
                                            });
                                        } else if (constructorName === 'WebAssembly.Instance') {
                                            results.wasmInstances.push({
                                                path: currentPath,
                                                type: 'Instance',
                                                exports: Object.keys(value.exports || {}),
                                                constructor: constructorName
                                            });
                                        } else if (constructorName === 'WebAssembly.Memory') {
                                            results.wasmMemory.push({
                                                path: currentPath,
                                                type: 'Memory',
                                                bufferSize: value.buffer ? value.buffer.byteLength : 0,
                                                constructor: constructorName
                                            });
                                        }
                                    }
                                    
                                    // Recursive search
                                    if (depth < 3) {
                                        deepSearch(value, currentPath, depth + 1, visited);
                                    }
                                }
                            } catch (e) {
                                // Ignore access errors
                            }
                        }
                    } catch (e) {
                        // Ignore access errors
                    }
                }
                
                // Search in window and common locations
                deepSearch(window, 'window');
                
                // 3. Check for WASM in specific locations
                const wasmLocations = [
                    'window.WebAssembly',
                    'window.wasm',
                    'window.Module',
                    'window.wasmModule',
                    'window.crypto.wasm',
                    'window.mexc',
                    'window.app',
                    'window.main'
                ];
                
                for (const location of wasmLocations) {
                    try {
                        const obj = eval(location);
                        if (obj) {
                            deepSearch(obj, location);
                        }
                    } catch (e) {
                        // Location doesn't exist
                    }
                }
                
                // 4. Scan for potential WASM binary data in variables
                function scanForBinaryData(obj, path = '', depth = 0) {
                    if (depth > 2) return;
                    
                    try {
                        for (const key in obj) {
                            try {
                                const value = obj[key];
                                
                                if (value instanceof ArrayBuffer && value.byteLength > 1000) {
                                    const view = new Uint8Array(value);
                                    // Check for WASM magic number (0x00 0x61 0x73 0x6D)
                                    if (view[0] === 0x00 && view[1] === 0x61 && view[2] === 0x73 && view[3] === 0x6D) {
                                        results.wasmFiles.push({
                                            path: `${path}.${key}`,
                                            type: 'ArrayBuffer',
                                            size: value.byteLength,
                                            isWasm: true
                                        });
                                    }
                                } else if (typeof value === 'string' && value.length > 1000) {
                                    // Check for base64 encoded WASM
                                    if (value.startsWith('AGFzbQ') || value.includes('wasm')) {
                                        results.wasmFiles.push({
                                            path: `${path}.${key}`,
                                            type: 'base64String',
                                            size: value.length,
                                            isPotentialWasm: true
                                        });
                                    }
                                } else if (typeof value === 'object' && value !== null && depth < 1) {
                                    scanForBinaryData(value, `${path}.${key}`, depth + 1);
                                }
                            } catch (e) {
                                // Ignore access errors
                            }
                        }
                    } catch (e) {
                        // Ignore access errors
                    }
                }
                
                scanForBinaryData(window, 'window');
                
                console.log('✅ WASM scan completed');
                return results;
            }
        """)
        
        print(f"✅ WASM scan completed!")
        print(f"   📁 WASM files: {len(wasm_scan_result['wasmFiles'])}")
        print(f"   📦 WASM modules: {len(wasm_scan_result['wasmModules'])}")
        print(f"   ⚙️ WASM instances: {len(wasm_scan_result['wasmInstances'])}")
        print(f"   🧠 WASM memory: {len(wasm_scan_result['wasmMemory'])}")
        
        return wasm_scan_result
    
    def analyze_wasm_files(self, wasm_scan_result):
        """Analyze found WASM files"""
        
        print(f"\n🔍 ANALYZING WASM FILES")
        print("="*30)
        
        wasm_files = wasm_scan_result['wasmFiles']
        
        if not wasm_files:
            print("❌ No WASM files found")
            return False
        
        for i, wasm_file in enumerate(wasm_files):
            print(f"\n📁 WASM File #{i+1}:")
            print(f"   URL/Path: {wasm_file.get('url', wasm_file.get('path', 'Unknown'))}")
            print(f"   Size: {wasm_file.get('size', 0)} bytes")
            print(f"   Type: {wasm_file.get('type', 'Unknown')}")
            
            # Try to download and analyze the WASM file
            if 'url' in wasm_file and wasm_file['url'].startswith('http'):
                self.download_and_analyze_wasm(wasm_file['url'])
            elif 'path' in wasm_file:
                self.extract_wasm_from_browser(wasm_file['path'])
        
        return True
    
    def download_and_analyze_wasm(self, url):
        """Download and analyze a WASM file"""
        
        print(f"   📥 Downloading WASM from: {url}")
        
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                wasm_data = response.content
                print(f"   ✅ Downloaded {len(wasm_data)} bytes")
                
                # Save WASM file
                filename = f"mexc_wasm_{int(time.time())}.wasm"
                with open(filename, 'wb') as f:
                    f.write(wasm_data)
                
                print(f"   💾 Saved as: {filename}")
                
                # Analyze WASM structure
                self.analyze_wasm_binary(wasm_data, filename)
                
                return True
            else:
                print(f"   ❌ Download failed: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Download error: {e}")
        
        return False
    
    def extract_wasm_from_browser(self, path):
        """Extract WASM data from browser memory"""
        
        print(f"   🧠 Extracting WASM from: {path}")
        
        try:
            wasm_data = self.page.evaluate(f"""
                () => {{
                    try {{
                        const obj = {path};
                        if (obj instanceof ArrayBuffer) {{
                            return Array.from(new Uint8Array(obj));
                        }} else if (typeof obj === 'string') {{
                            // Try to decode base64
                            try {{
                                const decoded = atob(obj);
                                return Array.from(new Uint8Array(decoded.split('').map(c => c.charCodeAt(0))));
                            }} catch (e) {{
                                return null;
                            }}
                        }}
                        return null;
                    }} catch (e) {{
                        return null;
                    }}
                }}
            """)
            
            if wasm_data:
                wasm_bytes = bytes(wasm_data)
                print(f"   ✅ Extracted {len(wasm_bytes)} bytes")
                
                # Save WASM file
                filename = f"mexc_extracted_{int(time.time())}.wasm"
                with open(filename, 'wb') as f:
                    f.write(wasm_bytes)
                
                print(f"   💾 Saved as: {filename}")
                
                # Analyze WASM structure
                self.analyze_wasm_binary(wasm_bytes, filename)
                
                return True
            else:
                print(f"   ❌ Could not extract WASM data")
        except Exception as e:
            print(f"   ❌ Extraction error: {e}")
        
        return False
    
    def analyze_wasm_binary(self, wasm_data, filename):
        """Analyze WASM binary structure"""
        
        print(f"   🔍 Analyzing WASM binary structure...")
        
        if len(wasm_data) < 8:
            print(f"   ❌ File too small to be valid WASM")
            return False
        
        # Check WASM magic number
        magic = wasm_data[:4]
        if magic != b'\x00asm':
            print(f"   ❌ Invalid WASM magic number: {magic.hex()}")
            return False
        
        # Check version
        version = int.from_bytes(wasm_data[4:8], 'little')
        print(f"   ✅ Valid WASM file, version: {version}")
        
        # Look for function exports that might be crypto-related
        try:
            # Simple analysis - look for common crypto function names in the binary
            wasm_str = wasm_data.decode('latin-1', errors='ignore')
            
            crypto_keywords = ['md5', 'sha', 'hash', 'sign', 'crypto', 'hmac', 'digest']
            found_keywords = []
            
            for keyword in crypto_keywords:
                if keyword in wasm_str.lower():
                    found_keywords.append(keyword)
            
            if found_keywords:
                print(f"   🎯 Found crypto keywords: {found_keywords}")
                return True
            else:
                print(f"   ❌ No obvious crypto keywords found")
        except:
            print(f"   ⚠️ Could not analyze WASM strings")
        
        return False
    
    def analyze_wasm_instances(self, wasm_scan_result):
        """Analyze WASM instances and their exports"""
        
        print(f"\n⚙️ ANALYZING WASM INSTANCES")
        print("="*35)
        
        wasm_instances = wasm_scan_result['wasmInstances']
        
        if not wasm_instances:
            print("❌ No WASM instances found")
            return False
        
        for i, instance in enumerate(wasm_instances):
            print(f"\n⚙️ WASM Instance #{i+1}:")
            print(f"   Path: {instance['path']}")
            print(f"   Exports: {instance['exports']}")
            
            # Try to call exported functions
            self.test_wasm_exports(instance['path'], instance['exports'])
        
        return True
    
    def test_wasm_exports(self, instance_path, exports):
        """Test WASM exported functions"""
        
        print(f"   🧪 Testing WASM exports...")
        
        for export_name in exports:
            try:
                # Test if this export might be a crypto function
                result = self.page.evaluate(f"""
                    () => {{
                        try {{
                            const instance = {instance_path};
                            const func = instance.exports['{export_name}'];
                            
                            if (typeof func === 'function') {{
                                // Try calling with test data
                                try {{
                                    // Test with simple inputs
                                    const testInputs = [
                                        [],
                                        [0],
                                        [1, 2, 3],
                                        [0x12, 0x34, 0x56, 0x78]
                                    ];
                                    
                                    for (const input of testInputs) {{
                                        try {{
                                            const result = func(...input);
                                            if (result !== undefined) {{
                                                return {{
                                                    name: '{export_name}',
                                                    input: input,
                                                    result: result,
                                                    type: typeof result
                                                }};
                                            }}
                                        }} catch (e) {{
                                            // Function call failed, try next input
                                        }}
                                    }}
                                }} catch (e) {{
                                    return {{
                                        name: '{export_name}',
                                        error: e.message
                                    }};
                                }}
                            }}
                            
                            return null;
                        }} catch (e) {{
                            return null;
                        }}
                    }}
                """)
                
                if result:
                    print(f"     🎯 {export_name}: {result}")
                    
                    # Check if this could be a signature function
                    if 'result' in result and isinstance(result['result'], (int, float)):
                        if len(str(result['result'])) == 32:  # Potential signature
                            print(f"       🔥 POTENTIAL SIGNATURE FUNCTION!")
            
            except Exception as e:
                print(f"     ❌ Error testing {export_name}: {e}")
    
    def run_wasm_analysis(self):
        """Run complete WASM analysis"""
        
        print("="*60)
        print("🔬 WEBASSEMBLY ANALYSIS")
        print("="*60)
        
        # Setup browser
        if not self.setup_browser_connection():
            return False
        
        try:
            # Scan for WASM files
            wasm_scan_result = self.scan_for_wasm_files()
            
            # Analyze found WASM files
            found_files = self.analyze_wasm_files(wasm_scan_result)
            
            # Analyze WASM instances
            found_instances = self.analyze_wasm_instances(wasm_scan_result)
            
            if found_files or found_instances:
                print(f"\n🎉 WASM ANALYSIS COMPLETE!")
                print(f"   Found WASM files: {found_files}")
                print(f"   Found WASM instances: {found_instances}")
                return True
            else:
                print(f"\n❌ No WASM files or instances found")
                return False
            
        finally:
            # Cleanup
            try:
                if hasattr(self, 'browser'):
                    self.browser.close()
                if hasattr(self, 'playwright'):
                    self.playwright.stop()
            except:
                pass

def main():
    """Main function"""
    
    analyzer = WasmAnalyzer()
    analyzer.run_wasm_analysis()

if __name__ == '__main__':
    main()
