#!/usr/bin/env python3
"""
MEMORY DEBUGGER
Use advanced browser debugging tools to analyze memory and crack signature
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class MemoryDebugger:
    """Advanced memory debugging to crack signature algorithm"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🧠 MEMORY DEBUGGER")
        print("="*25)
        print("🔥 ADVANCED BROWSER DEBUGGING")
    
    def setup_advanced_debugging(self):
        """Setup advanced debugging with CDP"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Enable advanced debugging
            self.cdp = self.page.context.new_cdp_session(self.page)
            
            # Enable runtime, debugger, and profiler
            self.cdp.send('Runtime.enable')
            self.cdp.send('Debugger.enable')
            self.cdp.send('Profiler.enable')
            self.cdp.send('HeapProfiler.enable')
            
            print("✅ Advanced debugging enabled")
            return True
            
        except Exception as e:
            print(f"❌ Debugging setup failed: {e}")
            return False
    
    def inject_memory_hooks(self):
        """Inject comprehensive memory and execution hooks"""
        
        print("\n🧠 INJECTING MEMORY HOOKS")
        print("="*30)
        
        # Inject advanced memory monitoring
        self.page.evaluate("""
            window.memoryAnalysis = {
                signatures: [],
                memoryOperations: [],
                functionCalls: [],
                stringOperations: [],
                cryptoOperations: []
            };
            
            console.log('🧠 Installing advanced memory hooks...');
            
            // 1. Hook ALL function calls with stack traces
            const originalCall = Function.prototype.call;
            Function.prototype.call = function(thisArg, ...args) {
                const funcStr = this.toString();
                
                // Check if this might be crypto-related
                if (funcStr.includes('md5') || funcStr.includes('sha') || funcStr.includes('sign') || 
                    funcStr.includes('hash') || funcStr.includes('crypto') || funcStr.includes('hmac')) {
                    
                    console.log('🔥 Crypto function call detected:', funcStr.substring(0, 100));
                    
                    const result = originalCall.apply(this, arguments);
                    
                    window.memoryAnalysis.functionCalls.push({
                        type: 'crypto_function_call',
                        function: funcStr.substring(0, 200),
                        args: args.map(arg => typeof arg === 'string' ? arg.substring(0, 50) : typeof arg),
                        result: typeof result === 'string' ? result.substring(0, 50) : typeof result,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    });
                    
                    return result;
                }
                
                return originalCall.apply(this, arguments);
            };
            
            // 2. Hook memory allocation
            const originalArrayBuffer = window.ArrayBuffer;
            window.ArrayBuffer = function(length) {
                const buffer = new originalArrayBuffer(length);
                
                if (length >= 16) {
                    console.log('🧠 Memory allocation:', length, 'bytes');
                    
                    window.memoryAnalysis.memoryOperations.push({
                        type: 'memory_allocation',
                        size: length,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    });
                }
                
                return buffer;
            };
            
            // 3. Hook Uint8Array operations (common in crypto)
            const originalUint8Array = window.Uint8Array;
            window.Uint8Array = function(...args) {
                const array = new originalUint8Array(...args);
                
                // Hook set method
                const originalSet = array.set;
                array.set = function(source, offset) {
                    if (source && source.length > 8) {
                        console.log('🧠 Uint8Array.set:', source.length, 'bytes');
                        
                        const sourceArray = Array.from(source.slice(0, 32));
                        const sourceHex = sourceArray.map(b => b.toString(16).padStart(2, '0')).join('');
                        
                        window.memoryAnalysis.memoryOperations.push({
                            type: 'uint8array_set',
                            length: source.length,
                            offset: offset,
                            data: sourceArray,
                            hex: sourceHex,
                            timestamp: Date.now(),
                            stack: new Error().stack
                        });
                    }
                    
                    return originalSet.apply(this, arguments);
                };
                
                return array;
            };
            
            // 4. Hook string creation and manipulation
            const originalString = window.String;
            window.String = function(value) {
                const str = originalString(value);
                
                // Check if this looks like a signature
                if (typeof str === 'string' && str.length === 32 && /^[a-f0-9]+$/i.test(str)) {
                    console.log('🔥 Potential signature string created:', str);
                    
                    window.memoryAnalysis.stringOperations.push({
                        type: 'signature_string_creation',
                        value: str,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    });
                }
                
                return str;
            };
            
            // 5. Hook object property access
            const originalGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;
            Object.getOwnPropertyDescriptor = function(obj, prop) {
                const descriptor = originalGetOwnPropertyDescriptor.apply(this, arguments);
                
                if (typeof prop === 'string' && 
                    (prop.includes('sign') || prop.includes('hash') || prop.includes('crypto'))) {
                    
                    console.log('🔥 Crypto property access:', prop, descriptor);
                    
                    window.memoryAnalysis.functionCalls.push({
                        type: 'crypto_property_access',
                        property: prop,
                        descriptor: descriptor,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    });
                }
                
                return descriptor;
            };
            
            // 6. Hook signature header setting with memory context
            const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
            XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                if (name.toLowerCase() === 'x-mxc-sign') {
                    console.log('🎉🎉🎉 SIGNATURE WITH MEMORY CONTEXT! 🎉🎉🎉');
                    console.log('Signature:', value);
                    
                    // Capture recent memory operations
                    const recentMemory = window.memoryAnalysis.memoryOperations.filter(
                        op => Date.now() - op.timestamp < 30000
                    );
                    
                    const recentFunctions = window.memoryAnalysis.functionCalls.filter(
                        call => Date.now() - call.timestamp < 30000
                    );
                    
                    const recentStrings = window.memoryAnalysis.stringOperations.filter(
                        str => Date.now() - str.timestamp < 30000
                    );
                    
                    window.memoryAnalysis.signatures.push({
                        type: 'signature_with_memory_context',
                        signature: value,
                        timestamp: Date.now(),
                        recentMemory: recentMemory,
                        recentFunctions: recentFunctions,
                        recentStrings: recentStrings,
                        stack: new Error().stack
                    });
                    
                    console.log('🧠 Memory context captured:');
                    console.log('  Recent memory ops:', recentMemory.length);
                    console.log('  Recent function calls:', recentFunctions.length);
                    console.log('  Recent string ops:', recentStrings.length);
                }
                
                return originalSetRequestHeader.apply(this, arguments);
            };
            
            console.log('✅ Advanced memory hooks installed!');
        """)
        
        print("✅ Memory hooks injected")
    
    def monitor_memory_operations(self):
        """Monitor memory operations during signature generation"""
        
        print("\n🔍 MONITORING MEMORY OPERATIONS")
        print("="*40)
        print()
        print("🎯 PLACE AN ORDER NOW!")
        print("   - All memory operations are being tracked")
        print("   - Function calls are being monitored")
        print("   - String operations are being captured")
        print()
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Get memory analysis data
                memory_data = self.page.evaluate("() => window.memoryAnalysis || {}")
                
                signatures = memory_data.get('signatures', [])
                
                if signatures:
                    print(f"\n🎉 SIGNATURE WITH MEMORY CONTEXT CAPTURED!")
                    
                    for i, sig_data in enumerate(signatures):
                        print(f"\n🔐 Signature #{i+1}: {sig_data['signature']}")
                        
                        # Analyze memory context
                        if self.analyze_memory_context(sig_data):
                            return True
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:
                    total_ops = sum(len(memory_data.get(key, [])) for key in memory_data.keys())
                    print(f"⏱️  Monitoring... ({elapsed}s, {total_ops} operations)")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(2)
        
        print(f"\n⏰ Monitoring timeout")
        return False
    
    def analyze_memory_context(self, sig_data):
        """Analyze memory context around signature generation"""
        
        print(f"🧠 ANALYZING MEMORY CONTEXT")
        print("-" * 35)
        
        signature = sig_data['signature']
        recent_memory = sig_data.get('recentMemory', [])
        recent_functions = sig_data.get('recentFunctions', [])
        recent_strings = sig_data.get('recentStrings', [])
        
        print(f"🔐 Signature: {signature}")
        print(f"🧠 Recent memory ops: {len(recent_memory)}")
        print(f"🔧 Recent function calls: {len(recent_functions)}")
        print(f"🔤 Recent string ops: {len(recent_strings)}")
        
        # 1. Check if signature appears in recent strings
        for string_op in recent_strings:
            if string_op.get('value') == signature:
                print(f"🎯 SIGNATURE FOUND IN STRING OPERATIONS!")
                print(f"   Operation: {string_op['type']}")
                print(f"   Stack: {string_op['stack'][:200]}...")
                return True
        
        # 2. Analyze memory operations for crypto patterns
        for mem_op in recent_memory:
            if mem_op['type'] == 'uint8array_set':
                mem_hex = mem_op.get('hex', '')
                
                # Test if this memory data is related to signature
                if self.test_memory_signature_correlation(mem_hex, signature):
                    return True
        
        # 3. Analyze function calls for crypto operations
        for func_call in recent_functions:
            if func_call['type'] == 'crypto_function_call':
                result = func_call.get('result', '')
                
                # Check if function result matches signature
                if isinstance(result, str) and result == signature:
                    print(f"🎉🎉🎉 SIGNATURE FUNCTION FOUND! 🎉🎉🎉")
                    print(f"   Function: {func_call['function']}")
                    print(f"   Args: {func_call['args']}")
                    print(f"   Result: {result}")
                    return True
        
        return False
    
    def test_memory_signature_correlation(self, mem_hex, signature):
        """Test correlation between memory data and signature"""
        
        if not mem_hex or len(mem_hex) < 16:
            return False
        
        print(f"   🧪 Testing memory correlation: {mem_hex[:32]}...")
        
        # Test various combinations
        test_patterns = [
            mem_hex,
            mem_hex + self.auth,
            self.auth + mem_hex,
            mem_hex + str(int(time.time() * 1000)),
            str(int(time.time() * 1000)) + mem_hex,
        ]
        
        for pattern in test_patterns:
            # Test MD5
            test_sig = hashlib.md5(pattern.encode()).hexdigest()
            if test_sig == signature:
                print(f"🎉🎉🎉 MEMORY SIGNATURE CRACKED! 🎉🎉🎉")
                print(f"   Algorithm: MD5({pattern})")
                print(f"   Memory data: {mem_hex}")
                return True
            
            # Test SHA256 (first 32)
            test_sig = hashlib.sha256(pattern.encode()).hexdigest()[:32]
            if test_sig == signature:
                print(f"🎉🎉🎉 MEMORY SIGNATURE CRACKED! 🎉🎉🎉")
                print(f"   Algorithm: SHA256({pattern})[:32]")
                print(f"   Memory data: {mem_hex}")
                return True
        
        return False
    
    def use_heap_profiler(self):
        """Use heap profiler to analyze memory during signature generation"""
        
        print(f"\n🔍 USING HEAP PROFILER")
        print("="*30)
        
        try:
            # Start heap profiling
            self.cdp.send('HeapProfiler.startSampling', {'samplingInterval': 32768})
            
            print("✅ Heap profiling started")
            print("🎯 Place an order now to capture heap data...")
            
            # Wait for user to place order
            time.sleep(60)  # Wait 1 minute for order placement
            
            # Stop profiling and get data
            profile = self.cdp.send('HeapProfiler.stopSampling')
            
            print(f"✅ Heap profile captured")
            
            # Analyze heap profile
            if profile and 'profile' in profile:
                self.analyze_heap_profile(profile['profile'])
                return True
            
        except Exception as e:
            print(f"❌ Heap profiling error: {e}")
        
        return False
    
    def analyze_heap_profile(self, profile):
        """Analyze heap profile for signature generation patterns"""
        
        print(f"🔍 ANALYZING HEAP PROFILE")
        print("-" * 30)
        
        # Look for function calls in the profile
        samples = profile.get('samples', [])
        
        print(f"📊 Heap samples: {len(samples)}")
        
        # Analyze samples for crypto-related functions
        crypto_samples = []
        
        for sample in samples:
            # Look for nodes that might be crypto-related
            node_id = sample.get('node', 0)
            
            # This is a simplified analysis - in a real implementation,
            # we'd need to traverse the call tree more thoroughly
            if node_id:
                crypto_samples.append(sample)
        
        if crypto_samples:
            print(f"🎯 Found {len(crypto_samples)} potential crypto samples")
            return True
        
        return False
    
    def run_memory_debugging(self):
        """Run complete memory debugging"""
        
        print("="*60)
        print("🧠 ADVANCED MEMORY DEBUGGING")
        print("="*60)
        
        # Setup debugging
        if not self.setup_advanced_debugging():
            return False
        
        try:
            # Inject memory hooks
            self.inject_memory_hooks()
            
            # Monitor memory operations
            if self.monitor_memory_operations():
                return True
            
            # Use heap profiler
            if self.use_heap_profiler():
                return True
            
            print(f"\n📋 MEMORY DEBUGGING COMPLETE")
            print("- Advanced hooks installed")
            print("- Memory operations monitored")
            print("- Function calls tracked")
            print("- Heap profiling attempted")
            
            return False
            
        finally:
            # Cleanup
            try:
                if hasattr(self, 'cdp'):
                    self.cdp.detach()
                if hasattr(self, 'browser'):
                    self.browser.close()
                if hasattr(self, 'playwright'):
                    self.playwright.stop()
            except:
                pass

def main():
    """Main function"""
    
    debugger = MemoryDebugger()
    if debugger.run_memory_debugging():
        print("\n🎉 SIGNATURE CRACKED WITH MEMORY DEBUGGING!")
    else:
        print("\n🔍 Memory debugging completed - continue with next approach")

if __name__ == '__main__':
    main()
