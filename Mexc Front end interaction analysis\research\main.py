"""
MEXC High-Speed Futures Trading System
Main application entry point with FastAPI server and browser automation
"""

import asyncio
import logging
import sys
import platform
from contextlib import asynccontextmanager
from datetime import datetime
from pathlib import Path

# Fix Windows Playwright compatibility BEFORE any other imports
if platform.system().lower() == "windows":
    if hasattr(asyncio, 'WindowsSelectorEventLoopPolicy'):
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        print("✅ Set WindowsSelectorEventLoopPolicy for Playwright compatibility")

import uvicorn
from fastapi import FastAPI, HTTPException, Request, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, FileResponse

from src.config import settings
from src.core.session_manager import SessionManager
from src.core.trading_engine import TradingEngine
from src.api.routes import webhook, management, dashboard
from src.utils.logger import setup_logging
from src.utils.telegram_bot import TelegramBot
from src.models.database import init_database


# Setup logging
logger = setup_logging()

# Global instances
session_manager: SessionManager = None
trading_engine: TradingEngine = None
telegram_bot: TelegramBot = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown"""
    global session_manager, trading_engine, telegram_bot
    
    logger.info("🚀 Starting MEXC Trading System...")
    
    try:
        # Initialize database
        await init_database()
        logger.info("✅ Database initialized")
        
        # Initialize Telegram bot
        if settings.TELEGRAM_ENABLED:
            telegram_bot = TelegramBot(
                token=settings.TELEGRAM_BOT_TOKEN,
                chat_id=settings.TELEGRAM_CHAT_ID
            )
            await telegram_bot.initialize()
            logger.info("✅ Telegram bot initialized")
        
        # Initialize session manager
        session_manager = SessionManager(
            pool_size=settings.SESSION_POOL_SIZE,
            telegram_bot=telegram_bot
        )
        await session_manager.initialize()
        logger.info("✅ Session manager initialized")
        
        # Initialize trading engine
        trading_engine = TradingEngine(
            session_manager=session_manager,
            telegram_bot=telegram_bot
        )
        await trading_engine.initialize()
        logger.info("✅ Trading engine initialized")
        
        # Inject dependencies into API routes
        webhook.set_trading_engine(trading_engine)
        management.set_managers(session_manager, trading_engine)
        dashboard.set_managers(session_manager, trading_engine)

        # Start background tasks
        asyncio.create_task(session_manager.start_health_monitoring())
        asyncio.create_task(trading_engine.start_monitoring())

        logger.info("🎯 MEXC Trading System started successfully!")

        # Send startup notification
        if telegram_bot:
            await telegram_bot.send_message(
                "🚀 MEXC Trading System started successfully!\n"
                f"📊 Session pool size: {settings.SESSION_POOL_SIZE}\n"
                f"🔧 Environment: {settings.ENVIRONMENT}\n"
                f"⚡ Ready to process trading signals"
            )
        
        yield
        
    except Exception as e:
        logger.error(f"❌ Failed to start application: {e}")
        if telegram_bot:
            await telegram_bot.send_message(f"❌ System startup failed: {e}")
        raise
    
    finally:
        # Cleanup on shutdown
        logger.info("🛑 Shutting down MEXC Trading System...")
        
        if trading_engine:
            await trading_engine.shutdown()
            logger.info("✅ Trading engine shutdown")
        
        if session_manager:
            await session_manager.shutdown()
            logger.info("✅ Session manager shutdown")
        
        if telegram_bot:
            await telegram_bot.send_message("🛑 MEXC Trading System shutdown")
            await telegram_bot.shutdown()
            logger.info("✅ Telegram bot shutdown")
        
        logger.info("👋 MEXC Trading System shutdown complete")


# Create FastAPI application
app = FastAPI(
    title="MEXC High-Speed Futures Trading System",
    description="Production-ready trading system with browser automation and session management",
    version=settings.VERSION,
    lifespan=lifespan
)

# Add CORS middleware
if settings.CORS_ENABLED:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Add custom functions to Jinja2 environment
templates.env.globals.update({
    'max': max,
    'min': min,
    'abs': abs,
    'round': round
})

# Include API routes
app.include_router(webhook.router, prefix="/webhook", tags=["webhooks"])
app.include_router(management.router, prefix="/api", tags=["management"])
app.include_router(dashboard.router, prefix="/dashboard", tags=["dashboard"])


@app.get("/", response_class=HTMLResponse)
async def dashboard_home(request: Request):
    """Main dashboard home page"""
    try:
        # Get system status
        session_stats = session_manager.get_session_stats() if session_manager else {
            "total_sessions": 0,
            "healthy_sessions": 0,
            "authenticated_sessions": 0,
            "average_health_score": 0.0
        }

        trading_stats = trading_engine.get_performance_stats() if trading_engine else {
            "total_trades": 0,
            "successful_trades": 0,
            "failed_trades": 0,
            "success_rate": 0.0,
            "active_trades": 0,
            "queued_trades": 0
        }

        system_status = {
            "status": "running",
            "sessions": len(session_manager.sessions) if session_manager else 0,
            "active_trades": trading_engine.active_trades_count if trading_engine else 0,
            "uptime": "N/A",  # TODO: Calculate uptime
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "timestamp": datetime.now().isoformat()
        }
        
        return templates.TemplateResponse(
            "dashboard.html",
            {
                "request": request,
                "system_status": system_status,
                "session_stats": session_stats,
                "trading_stats": trading_stats,
                "settings": settings
            }
        )
    except Exception as e:
        logger.error(f"Dashboard error: {e}")
        raise HTTPException(status_code=500, detail="Dashboard unavailable")


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring"""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "version": settings.VERSION,
            "environment": settings.ENVIRONMENT,
            "components": {
                "session_manager": session_manager.is_healthy() if session_manager else False,
                "trading_engine": trading_engine.is_healthy() if trading_engine else False,
                "telegram_bot": telegram_bot.is_connected() if telegram_bot else False,
            }
        }
        
        # Check if all components are healthy
        all_healthy = all(health_status["components"].values())
        if not all_healthy:
            health_status["status"] = "degraded"
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": asyncio.get_event_loop().time()
        }


@app.get("/favicon.ico", include_in_schema=False)
async def favicon():
    """Serve favicon"""
    favicon_path = Path("static/favicon.svg")
    if favicon_path.exists():
        return FileResponse(
            path=favicon_path,
            media_type="image/svg+xml",
            filename="favicon.ico"
        )
    else:
        # Return 204 No Content if no favicon available
        from fastapi.responses import Response
        return Response(status_code=204)


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    try:
        metrics_data = {
            "sessions_active": len(session_manager.sessions) if session_manager else 0,
            "sessions_healthy": sum(1 for s in session_manager.sessions if s.is_healthy()) if session_manager else 0,
            "trades_active": trading_engine.active_trades_count if trading_engine else 0,
            "trades_total": trading_engine.total_trades_count if trading_engine else 0,
            "trades_successful": trading_engine.successful_trades_count if trading_engine else 0,
            "trades_failed": trading_engine.failed_trades_count if trading_engine else 0,
        }
        
        return metrics_data
        
    except Exception as e:
        logger.error(f"Metrics collection failed: {e}")
        return {"error": str(e)}


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    if telegram_bot:
        await telegram_bot.send_message(
            f"🚨 System Error:\n"
            f"Path: {request.url.path}\n"
            f"Error: {str(exc)[:200]}..."
        )
    
    return {
        "error": "Internal server error",
        "detail": str(exc) if settings.DEBUG_MODE else "An error occurred"
    }


def main():
    """Main entry point"""
    try:
        # Create necessary directories
        Path("logs").mkdir(exist_ok=True)
        Path("data").mkdir(exist_ok=True)
        Path("browser_data").mkdir(exist_ok=True)
        Path("backups").mkdir(exist_ok=True)
        
        # Run the application
        uvicorn.run(
            "main:app",
            host=settings.API_HOST,
            port=settings.API_PORT,
            workers=settings.API_WORKERS,
            reload=settings.RELOAD_ON_CHANGE,
            log_level=settings.LOG_LEVEL.lower(),
            access_log=True
        )
        
    except KeyboardInterrupt:
        logger.info("Application stopped by user")
    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
