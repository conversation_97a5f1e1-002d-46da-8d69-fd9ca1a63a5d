<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}MEXC Trading System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            margin: 0.25rem 0;
            border-radius: 0.5rem;
            transition: all 0.3s;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background-color: rgba(255,255,255,0.1);
        }
        .main-content {
            background-color: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 1rem;
            box-shadow: 0 0.125rem 0.25rem rgba(0,0,0,0.075);
        }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .trade-success { background-color: #d4edda; }
        .trade-failed { background-color: #f8d7da; }
        .trade-pending { background-color: #fff3cd; }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-chart-line"></i>
                            MEXC Trader
                        </h4>
                        <small class="text-white-50">v{{ settings.VERSION if settings else '1.0.0' }}</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                                <i class="fas fa-tachometer-alt"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'sessions' in request.url.path %}active{% endif %}" href="/dashboard/sessions">
                                <i class="fas fa-globe"></i>
                                Sessions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'trades' in request.url.path %}active{% endif %}" href="/dashboard/trades">
                                <i class="fas fa-exchange-alt"></i>
                                Trades
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if 'config' in request.url.path %}active{% endif %}" href="/dashboard/config">
                                <i class="fas fa-cog"></i>
                                Configuration
                            </a>
                        </li>
                        <li class="nav-item mt-3">
                            <a class="nav-link" href="/api/status" target="_blank">
                                <i class="fas fa-heartbeat"></i>
                                API Status
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/docs" target="_blank">
                                <i class="fas fa-book"></i>
                                API Docs
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4 main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}Dashboard{% endblock %}</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i>
                                Refresh
                            </button>
                        </div>
                        <div class="btn-group">
                            <button type="button" class="btn btn-sm btn-danger" onclick="emergencyStop()">
                                <i class="fas fa-stop"></i>
                                Emergency Stop
                            </button>
                        </div>
                    </div>
                </div>

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto-refresh every 30 seconds
        setInterval(function() {
            if (document.getElementById('auto-refresh')) {
                location.reload();
            }
        }, 30000);
        
        // Emergency stop function
        async function emergencyStop() {
            if (confirm('Are you sure you want to activate emergency stop? This will halt all trading operations.')) {
                try {
                    const response = await fetch('/api/emergency-stop', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        }
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        alert('Emergency stop activated successfully');
                        location.reload();
                    } else {
                        alert('Emergency stop failed: ' + result.message);
                    }
                } catch (error) {
                    alert('Emergency stop failed: ' + error.message);
                }
            }
        }
        
        // Utility functions
        function formatTimestamp(timestamp) {
            return new Date(timestamp).toLocaleString();
        }
        
        function formatCurrency(amount) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD'
            }).format(amount);
        }
        
        function getStatusClass(status) {
            switch(status.toLowerCase()) {
                case 'healthy':
                case 'active':
                case 'executed':
                case 'success':
                    return 'status-healthy';
                case 'warning':
                case 'pending':
                    return 'status-warning';
                case 'error':
                case 'failed':
                case 'unhealthy':
                    return 'status-error';
                default:
                    return '';
            }
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
