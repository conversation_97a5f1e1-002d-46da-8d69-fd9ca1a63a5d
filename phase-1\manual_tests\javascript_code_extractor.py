#!/usr/bin/env python3
"""
JavaScript Code Extractor
Extract and analyze all JavaScript code to find the signature function
"""

import json
import time
import re
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class JavaScriptCodeExtractor:
    """Extract and analyze JavaScript code to find signature generation"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🔍 JavaScript Code Extractor")
        print("="*35)
        print("🎯 FINDING THE SIGNATURE FUNCTION")
    
    def setup_browser(self):
        """Setup browser for code extraction"""
        
        print("\n🌐 Setting up browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            print("✅ Browser setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def extract_all_javascript(self):
        """Extract all JavaScript code from the page"""
        
        print("\n📜 EXTRACTING ALL JAVASCRIPT CODE")
        print("="*40)
        
        # Extract all script sources and content
        script_data = self.page.evaluate("""
            () => {
                const scripts = [];
                
                // Get all script elements
                const scriptElements = document.querySelectorAll('script');
                
                scriptElements.forEach((script, index) => {
                    if (script.src) {
                        // External script
                        scripts.push({
                            type: 'external',
                            src: script.src,
                            index: index,
                            content: null
                        });
                    } else if (script.textContent) {
                        // Inline script
                        const content = script.textContent;
                        scripts.push({
                            type: 'inline',
                            src: null,
                            index: index,
                            content: content,
                            length: content.length
                        });
                    }
                });
                
                return scripts;
            }
        """)
        
        print(f"✅ Found {len(script_data)} scripts")
        
        # Analyze each script
        signature_related_scripts = []
        
        for script in script_data:
            if script['type'] == 'inline' and script['content']:
                content = script['content']
                
                # Look for signature-related keywords
                signature_keywords = [
                    'sign', 'signature', 'hash', 'md5', 'sha', 'hmac',
                    'crypto', 'encrypt', 'auth', 'token', 'nonce',
                    'x-mxc-sign', 'createSignature', 'generateSign'
                ]
                
                keyword_matches = []
                for keyword in signature_keywords:
                    if keyword.lower() in content.lower():
                        # Count occurrences
                        count = content.lower().count(keyword.lower())
                        keyword_matches.append((keyword, count))
                
                if keyword_matches:
                    signature_related_scripts.append({
                        'script': script,
                        'keywords': keyword_matches,
                        'relevance_score': sum(count for _, count in keyword_matches)
                    })
            
            elif script['type'] == 'external':
                # Check if external script URL looks relevant
                src = script['src']
                if any(keyword in src.lower() for keyword in ['crypto', 'sign', 'auth', 'hash']):
                    signature_related_scripts.append({
                        'script': script,
                        'keywords': [('url_match', 1)],
                        'relevance_score': 1
                    })
        
        # Sort by relevance
        signature_related_scripts.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        print(f"📊 Found {len(signature_related_scripts)} signature-related scripts")
        
        return signature_related_scripts
    
    def analyze_signature_scripts(self, signature_scripts):
        """Analyze the most relevant scripts for signature functions"""
        
        print(f"\n🔍 ANALYZING TOP SIGNATURE SCRIPTS")
        print("="*40)
        
        for i, script_info in enumerate(signature_scripts[:5]):  # Top 5 scripts
            script = script_info['script']
            keywords = script_info['keywords']
            score = script_info['relevance_score']
            
            print(f"\n📋 SCRIPT #{i+1} (Score: {score})")
            print(f"   Type: {script['type']}")
            
            if script['type'] == 'external':
                print(f"   Source: {script['src']}")
                print(f"   Keywords: {keywords}")
            else:
                print(f"   Length: {script['length']} chars")
                print(f"   Keywords: {keywords}")
                
                # Extract signature-related functions
                content = script['content']
                self.extract_signature_functions(content, i+1)
    
    def extract_signature_functions(self, content, script_num):
        """Extract potential signature functions from script content"""
        
        print(f"   🔧 Extracting functions from script #{script_num}...")
        
        # Look for function definitions that might be signature-related
        function_patterns = [
            r'function\s+(\w*[Ss]ign\w*)\s*\([^)]*\)\s*{[^}]*}',
            r'function\s+(\w*[Hh]ash\w*)\s*\([^)]*\)\s*{[^}]*}',
            r'function\s+(\w*[Cc]rypto\w*)\s*\([^)]*\)\s*{[^}]*}',
            r'(\w*[Ss]ign\w*)\s*[:=]\s*function\s*\([^)]*\)\s*{[^}]*}',
            r'(\w*[Hh]ash\w*)\s*[:=]\s*function\s*\([^)]*\)\s*{[^}]*}',
            r'(\w*[Cc]rypto\w*)\s*[:=]\s*function\s*\([^)]*\)\s*{[^}]*}',
        ]
        
        found_functions = []
        
        for pattern in function_patterns:
            matches = re.finditer(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                func_name = match.group(1)
                func_code = match.group(0)
                
                # Limit function code length for display
                if len(func_code) > 500:
                    func_code = func_code[:500] + "..."
                
                found_functions.append({
                    'name': func_name,
                    'code': func_code
                })
        
        if found_functions:
            print(f"      Found {len(found_functions)} potential signature functions:")
            for func in found_functions[:3]:  # Show top 3
                print(f"         🎯 {func['name']}")
                print(f"            {func['code'][:100]}...")
        else:
            print(f"      No obvious signature functions found")
        
        # Look for specific signature-related strings
        signature_strings = [
            r'["\']x-mxc-sign["\']',
            r'["\']x-mxc-nonce["\']',
            r'["\']signature["\']',
            r'md5\s*\(',
            r'sha\d+\s*\(',
            r'hmac\s*\(',
            r'CryptoJS\.',
        ]
        
        found_strings = []
        for pattern in signature_strings:
            matches = re.finditer(pattern, content, re.IGNORECASE)
            for match in matches:
                context_start = max(0, match.start() - 50)
                context_end = min(len(content), match.end() + 50)
                context = content[context_start:context_end]
                
                found_strings.append({
                    'pattern': pattern,
                    'match': match.group(0),
                    'context': context
                })
        
        if found_strings:
            print(f"      Found {len(found_strings)} signature-related strings:")
            for string_info in found_strings[:3]:  # Show top 3
                print(f"         🔍 {string_info['match']}")
                print(f"            Context: ...{string_info['context']}...")
    
    def search_for_signature_generation(self):
        """Search for the actual signature generation code"""
        
        print(f"\n🎯 SEARCHING FOR SIGNATURE GENERATION")
        print("="*45)
        
        # Search for signature generation in global scope
        search_results = self.page.evaluate("""
            () => {
                const results = {
                    globalFunctions: [],
                    cryptoLibraries: [],
                    signatureReferences: []
                };
                
                // Search for global functions
                for (const key in window) {
                    if (typeof window[key] === 'function') {
                        const funcName = key.toLowerCase();
                        if (funcName.includes('sign') || funcName.includes('hash') || 
                            funcName.includes('crypto') || funcName.includes('auth')) {
                            
                            const funcStr = window[key].toString();
                            results.globalFunctions.push({
                                name: key,
                                source: funcStr.substring(0, 200)
                            });
                        }
                    }
                }
                
                // Check for crypto libraries
                const cryptoLibs = ['CryptoJS', 'forge', 'sjcl', 'crypto', 'md5', 'sha1', 'sha256'];
                for (const lib of cryptoLibs) {
                    if (window[lib]) {
                        results.cryptoLibraries.push({
                            name: lib,
                            type: typeof window[lib],
                            methods: Object.keys(window[lib] || {}).slice(0, 10)
                        });
                    }
                }
                
                // Search for signature references in all objects
                function searchObject(obj, path, depth = 0) {
                    if (depth > 2) return;
                    
                    try {
                        for (const key in obj) {
                            if (typeof key === 'string' && key.toLowerCase().includes('sign')) {
                                results.signatureReferences.push({
                                    path: `${path}.${key}`,
                                    type: typeof obj[key],
                                    value: typeof obj[key] === 'function' ? 
                                           obj[key].toString().substring(0, 100) : 
                                           String(obj[key]).substring(0, 100)
                                });
                            }
                            
                            if (typeof obj[key] === 'object' && obj[key] !== null && depth < 2) {
                                searchObject(obj[key], `${path}.${key}`, depth + 1);
                            }
                        }
                    } catch (e) {
                        // Ignore access errors
                    }
                }
                
                // Search common namespaces
                const namespaces = ['window', 'document', 'navigator'];
                for (const ns of namespaces) {
                    if (window[ns]) {
                        searchObject(window[ns], ns);
                    }
                }
                
                return results;
            }
        """)
        
        print(f"✅ Search completed!")
        print(f"   Global functions: {len(search_results['globalFunctions'])}")
        print(f"   Crypto libraries: {len(search_results['cryptoLibraries'])}")
        print(f"   Signature references: {len(search_results['signatureReferences'])}")
        
        # Display results
        if search_results['globalFunctions']:
            print(f"\n🔧 GLOBAL SIGNATURE FUNCTIONS:")
            for func in search_results['globalFunctions'][:5]:
                print(f"   📍 {func['name']}")
                print(f"      {func['source']}...")
        
        if search_results['cryptoLibraries']:
            print(f"\n🔐 CRYPTO LIBRARIES:")
            for lib in search_results['cryptoLibraries']:
                print(f"   📚 {lib['name']} ({lib['type']})")
                print(f"      Methods: {lib['methods']}")
        
        if search_results['signatureReferences']:
            print(f"\n🔍 SIGNATURE REFERENCES:")
            for ref in search_results['signatureReferences'][:10]:
                print(f"   📍 {ref['path']} ({ref['type']})")
                print(f"      {ref['value']}...")
        
        return search_results
    
    def run_extraction(self):
        """Run the complete JavaScript extraction"""
        
        print("="*60)
        print("🔍 JAVASCRIPT SIGNATURE FUNCTION EXTRACTION")
        print("="*60)
        
        # Setup browser
        if not self.setup_browser():
            return False
        
        try:
            # Extract all JavaScript
            signature_scripts = self.extract_all_javascript()
            
            # Analyze signature-related scripts
            self.analyze_signature_scripts(signature_scripts)
            
            # Search for signature generation
            search_results = self.search_for_signature_generation()
            
            if (signature_scripts or 
                search_results['globalFunctions'] or 
                search_results['cryptoLibraries'] or 
                search_results['signatureReferences']):
                print("\n🎉 JAVASCRIPT ANALYSIS COMPLETE!")
                print("Found potential signature-related code!")
                return True
            else:
                print("\n❌ No signature-related JavaScript found")
                print("The signature generation might be:")
                print("- Heavily obfuscated")
                print("- In WebAssembly")
                print("- Generated server-side")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    extractor = JavaScriptCodeExtractor()
    success = extractor.run_extraction()
    
    if success:
        print("\n🚀 JAVASCRIPT EXTRACTION SUCCESSFUL!")
        print("Potential signature functions identified!")
    else:
        print("\n🔧 JavaScript extraction complete")
        print("May need alternative reverse engineering approaches")

if __name__ == '__main__':
    main()
