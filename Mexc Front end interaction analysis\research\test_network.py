#!/usr/bin/env python3
"""
Network connectivity test for MEXC Trading System
"""

import asyncio
import aiohttp
import time
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

async def test_mexc_connectivity():
    """Test connectivity to MEXC"""
    print("🌐 Testing MEXC Connectivity...")
    print("=" * 50)
    
    urls_to_test = [
        "https://futures.mexc.com/",
        "https://www.mexc.com/",
        "https://api.mexc.com/",
    ]
    
    results = {}
    
    for url in urls_to_test:
        print(f"Testing {url}...")
        try:
            start_time = time.time()
            
            async with aiohttp.ClientSession() as session:
                async with session.get(url, timeout=30) as response:
                    end_time = time.time()
                    response_time = (end_time - start_time) * 1000
                    
                    results[url] = {
                        'status': response.status,
                        'response_time': response_time,
                        'success': True
                    }
                    
                    print(f"  ✅ Status: {response.status}")
                    print(f"  ⏱️  Response time: {response_time:.0f}ms")
                    
        except asyncio.TimeoutError:
            results[url] = {
                'status': 'TIMEOUT',
                'response_time': 30000,
                'success': False
            }
            print(f"  ❌ TIMEOUT (30s)")
            
        except Exception as e:
            results[url] = {
                'status': f'ERROR: {e}',
                'response_time': 0,
                'success': False
            }
            print(f"  ❌ ERROR: {e}")
        
        print()
    
    # Summary
    print("📊 SUMMARY")
    print("=" * 50)
    
    successful = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    print(f"Successful connections: {successful}/{total}")
    
    if successful == 0:
        print("❌ NO CONNECTIONS SUCCESSFUL")
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Check your internet connection")
        print("2. Check if MEXC is blocked by firewall/antivirus")
        print("3. Try using a VPN if MEXC is geo-blocked")
        print("4. Check Windows Defender/Firewall settings")
        return False
    elif successful < total:
        print("⚠️  PARTIAL SUCCESS")
        print("Some URLs are accessible, system may work with reduced functionality")
        return True
    else:
        print("✅ ALL CONNECTIONS SUCCESSFUL")
        print("Network connectivity is good for MEXC trading")
        return True

async def test_browser_launch():
    """Test if browser can be launched"""
    print("\n🌐 Testing Browser Launch...")
    print("=" * 50)
    
    try:
        from src.core.session_manager import SessionManager
        from src.utils.telegram_bot import TelegramBot
        
        # Create minimal session manager
        telegram_bot = TelegramBot()
        session_manager = SessionManager(pool_size=1, telegram_bot=telegram_bot)
        
        # Test browser detection
        browser_exe = session_manager._find_browser_executable()
        print(f"✅ Browser found: {browser_exe}")
        
        # Test persistent browser launch
        print("Testing persistent browser launch...")
        success = await session_manager._launch_persistent_browser()
        
        if success:
            print("✅ Persistent browser launched successfully")
            print(f"   PID: {session_manager.chrome_process.pid}")
            print(f"   Debug port: http://localhost:9222")
            
            # Clean up
            if session_manager.chrome_process:
                session_manager.chrome_process.terminate()
                session_manager.chrome_process.wait()
                print("✅ Browser terminated cleanly")
            
            return True
        else:
            print("❌ Failed to launch persistent browser")
            return False
            
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        return False

async def main():
    """Run all tests"""
    print("🧪 MEXC Trading System - Network & Browser Test")
    print("=" * 60)
    
    # Test network connectivity
    network_ok = await test_mexc_connectivity()
    
    # Test browser launch
    browser_ok = await test_browser_launch()
    
    print("\n" + "=" * 60)
    print("🎯 FINAL RESULTS")
    print("=" * 60)
    
    print(f"Network connectivity: {'✅ PASS' if network_ok else '❌ FAIL'}")
    print(f"Browser launch: {'✅ PASS' if browser_ok else '❌ FAIL'}")
    
    if network_ok and browser_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("The system should work correctly.")
        print("\n📋 NEXT STEPS:")
        print("1. Start the system: python test_startup.py")
        print("2. Open debug viewer: open debug_viewer.html")
        print("3. Check sessions: http://localhost:8000/dashboard/sessions")
    else:
        print("\n❌ SOME TESTS FAILED")
        print("Please fix the issues before starting the trading system.")
        
        if not network_ok:
            print("\n🔧 NETWORK ISSUES:")
            print("- Check internet connection")
            print("- Check firewall settings")
            print("- Try using VPN if needed")
            
        if not browser_ok:
            print("\n🔧 BROWSER ISSUES:")
            print("- Check Chrome installation")
            print("- Check Windows permissions")
            print("- Try running as administrator")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)
