#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Comprehensive Test Suite
Complete testing automation for all MEXC trading interface elements and interactions.

TEST CATEGORIES:
1. Tab Navigation Testing (Open/Close tabs)
2. Trading Configuration Testing (Trigger Type, TP/SL)
3. Popup Management System (Error/Confirmation/Unexpected)
4. Comprehensive Field Testing (All input fields)
5. Integration Testing (Complete workflow)

This suite validates each interaction type and reports which elements work correctly.
"""

import os
import sys
import json
import time
import logging
import argparse
from datetime import datetime
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from playwright.sync_api import sync_playwright

@dataclass
class TestResult:
    test_name: str
    success: bool
    duration: float
    details: Dict[str, Any] = field(default_factory=dict)
    errors: List[str] = field(default_factory=list)
    screenshots: List[str] = field(default_factory=list)

@dataclass
class TradeConfig:
    symbol: str = "TRU_USDT"
    side: str = "BUY"
    quantity: float = 10.0
    price: Optional[float] = None
    leverage: int = 20
    trigger_type: str = "DEFAULT"  # DEFAULT, POST_ONLY
    take_profit: Optional[float] = None
    stop_loss: Optional[float] = None
    margin_mode: str = "CROSS"  # CROSS, ISOLATED
    order_type: str = "MARKET"  # MARKET, LIMIT

class MEXCComprehensiveTestSuite:
    """Comprehensive test suite for all MEXC trading interface elements"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        # Browser components
        self.playwright = None
        self.browser = None
        self.page = None
        
        # Test tracking
        self.screenshot_counter = 0
        self.test_results: List[TestResult] = []
        
        self.logger.info(f"🧪 Comprehensive test suite initialized: {config}")
    
    def take_screenshot(self, name: str, description: str = "") -> str:
        """Take a screenshot for test verification"""
        self.screenshot_counter += 1
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_{self.screenshot_counter:03d}_{name}_{timestamp}.png"
        
        try:
            self.page.screenshot(path=filename, full_page=True)
            self.logger.info(f"📸 {filename} - {description}")
            return filename
        except Exception as e:
            self.logger.error(f"Screenshot failed: {e}")
            return ""
    
    def connect_to_browser(self) -> bool:
        """Connect to browser"""
        self.logger.info("🔌 Connecting to browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("No MEXC page found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"✅ Connected to MEXC page: {self.page.url}")
            
            # Take initial screenshot
            self.take_screenshot("initial_state", "Initial browser state")
            return True
            
        except Exception as e:
            self.logger.error(f"Browser connection failed: {e}")
            return False
    
    def test_tab_navigation(self) -> TestResult:
        """Test 1: Tab Navigation Testing (Open/Close tabs)"""
        self.logger.info("🧪 TEST 1: Tab Navigation Testing")
        
        start_time = time.time()
        result = TestResult(
            test_name="tab_navigation",
            success=False,
            duration=0
        )
        
        # Take before screenshot
        before_screenshot = self.take_screenshot("before_tab_test", "Before tab navigation test")
        result.screenshots.append(before_screenshot)
        
        tab_test_script = """
        () => {
            console.log('🧪 Testing tab navigation...');
            
            const results = {
                tabs_found: [],
                current_tab: null,
                navigation_tests: []
            };
            
            // Find tab elements
            const tabSelectors = [
                '.ant-tabs-tab',
                '[role="tab"]',
                '.tab',
                'button[data-testid*="tab"]',
                'div[class*="tab"]'
            ];
            
            let tabs = [];
            for (const selector of tabSelectors) {
                const elements = document.querySelectorAll(selector);
                if (elements.length > 0) {
                    tabs = Array.from(elements);
                    break;
                }
            }
            
            if (tabs.length === 0) {
                return { success: false, error: 'No tabs found', results: results };
            }
            
            // Analyze found tabs
            tabs.forEach((tab, index) => {
                const text = tab.textContent?.trim() || '';
                const isActive = tab.classList.contains('ant-tabs-tab-active') || 
                                tab.classList.contains('active') ||
                                tab.getAttribute('aria-selected') === 'true';
                
                results.tabs_found.push({
                    index: index,
                    text: text,
                    isActive: isActive,
                    className: tab.className,
                    position: {
                        x: Math.round(tab.getBoundingClientRect().x),
                        y: Math.round(tab.getBoundingClientRect().y)
                    }
                });
                
                if (isActive) {
                    results.current_tab = { index: index, text: text };
                }
            });
            
            // Test tab navigation
            for (let i = 0; i < Math.min(tabs.length, 3); i++) {
                const tab = tabs[i];
                const originalText = tab.textContent?.trim() || '';
                
                try {
                    // Click the tab
                    tab.focus();
                    tab.click();
                    
                    // Wait for potential changes
                    setTimeout(() => {}, 500);
                    
                    // Check if tab became active
                    const isNowActive = tab.classList.contains('ant-tabs-tab-active') || 
                                       tab.classList.contains('active') ||
                                       tab.getAttribute('aria-selected') === 'true';
                    
                    results.navigation_tests.push({
                        tab_index: i,
                        tab_text: originalText,
                        click_attempted: true,
                        became_active: isNowActive,
                        success: true
                    });
                    
                } catch (error) {
                    results.navigation_tests.push({
                        tab_index: i,
                        tab_text: originalText,
                        click_attempted: true,
                        became_active: false,
                        success: false,
                        error: error.message
                    });
                }
            }
            
            return {
                success: true,
                results: results,
                total_tabs: tabs.length,
                successful_navigations: results.navigation_tests.filter(t => t.success).length
            };
        }
        """
        
        try:
            tab_result = self.page.evaluate(tab_test_script)
            
            # Take after screenshot
            after_screenshot = self.take_screenshot("after_tab_test", "After tab navigation test")
            result.screenshots.append(after_screenshot)
            
            if tab_result.get('success'):
                results_data = tab_result.get('results', {})
                total_tabs = tab_result.get('total_tabs', 0)
                successful_navigations = tab_result.get('successful_navigations', 0)
                
                result.success = total_tabs > 0
                result.details = {
                    'total_tabs': total_tabs,
                    'successful_navigations': successful_navigations,
                    'tabs_found': results_data.get('tabs_found', []),
                    'current_tab': results_data.get('current_tab'),
                    'navigation_tests': results_data.get('navigation_tests', [])
                }
                
                self.logger.info(f"✅ Tab navigation test completed:")
                self.logger.info(f"   Total tabs found: {total_tabs}")
                self.logger.info(f"   Successful navigations: {successful_navigations}")
                
                for tab in results_data.get('tabs_found', []):
                    status = "🟢 ACTIVE" if tab.get('isActive') else "⚪ INACTIVE"
                    self.logger.info(f"   Tab {tab.get('index', 0)}: '{tab.get('text', '')}' {status}")
                
            else:
                error = tab_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Tab navigation test failed: {error}")
                
        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Tab navigation test exception: {e}")
        
        result.duration = time.time() - start_time
        return result

    def test_trading_configuration(self) -> TestResult:
        """Test 2: Trading Configuration Testing (Trigger Type, TP/SL)"""
        self.logger.info("🧪 TEST 2: Trading Configuration Testing")

        start_time = time.time()
        result = TestResult(
            test_name="trading_configuration",
            success=False,
            duration=0
        )

        # Take before screenshot
        before_screenshot = self.take_screenshot("before_config_test", "Before trading configuration test")
        result.screenshots.append(before_screenshot)

        config_test_script = """
        () => {
            console.log('🧪 Testing trading configuration...');

            const results = {
                trigger_type: { found: false, tested: false, success: false },
                take_profit: { found: false, tested: false, success: false },
                stop_loss: { found: false, tested: false, success: false },
                leverage: { found: false, tested: false, success: false },
                margin_mode: { found: false, tested: false, success: false }
            };

            // Test Trigger Type (Post Only mode)
            console.log('Testing Trigger Type...');
            const triggerSelectors = [
                'select[name*="trigger"]',
                'select[name*="type"]',
                '.ant-select:has-text("Post Only")',
                'button:has-text("Post Only")',
                '[data-testid*="trigger"]',
                '[class*="trigger"]'
            ];

            for (const selector of triggerSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.trigger_type.found = true;
                        results.trigger_type.selector = selector;
                        results.trigger_type.count = elements.length;

                        // Try to interact with first element
                        const element = elements[0];
                        element.focus();
                        element.click();
                        results.trigger_type.tested = true;
                        results.trigger_type.success = true;
                        break;
                    }
                } catch (error) {
                    results.trigger_type.error = error.message;
                }
            }

            // Test Take Profit
            console.log('Testing Take Profit...');
            const tpSelectors = [
                'input[name*="profit"]',
                'input[placeholder*="profit"]',
                'input[placeholder*="TP"]',
                '.take-profit input',
                '[data-testid*="profit"] input'
            ];

            for (const selector of tpSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.take_profit.found = true;
                        results.take_profit.selector = selector;
                        results.take_profit.count = elements.length;

                        // Try to fill with test value
                        const element = elements[0];
                        element.focus();
                        element.value = '0.05';
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        results.take_profit.tested = true;
                        results.take_profit.success = element.value === '0.05';
                        break;
                    }
                } catch (error) {
                    results.take_profit.error = error.message;
                }
            }

            // Test Stop Loss
            console.log('Testing Stop Loss...');
            const slSelectors = [
                'input[name*="loss"]',
                'input[placeholder*="loss"]',
                'input[placeholder*="SL"]',
                '.stop-loss input',
                '[data-testid*="loss"] input'
            ];

            for (const selector of slSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.stop_loss.found = true;
                        results.stop_loss.selector = selector;
                        results.stop_loss.count = elements.length;

                        // Try to fill with test value
                        const element = elements[0];
                        element.focus();
                        element.value = '0.02';
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        results.stop_loss.tested = true;
                        results.stop_loss.success = element.value === '0.02';
                        break;
                    }
                } catch (error) {
                    results.stop_loss.error = error.message;
                }
            }

            // Test Leverage
            console.log('Testing Leverage...');
            const leverageSelectors = [
                'input[name*="leverage"]',
                'input[placeholder*="leverage"]',
                '.leverage input',
                '[data-testid*="leverage"] input',
                'button:has-text("20x")',
                'button:has-text("10x")'
            ];

            for (const selector of leverageSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.leverage.found = true;
                        results.leverage.selector = selector;
                        results.leverage.count = elements.length;

                        const element = elements[0];
                        element.focus();
                        element.click();
                        results.leverage.tested = true;
                        results.leverage.success = true;
                        break;
                    }
                } catch (error) {
                    results.leverage.error = error.message;
                }
            }

            // Test Margin Mode
            console.log('Testing Margin Mode...');
            const marginSelectors = [
                'button:has-text("Cross")',
                'button:has-text("Isolated")',
                '.margin-mode button',
                '[data-testid*="margin"] button'
            ];

            for (const selector of marginSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.margin_mode.found = true;
                        results.margin_mode.selector = selector;
                        results.margin_mode.count = elements.length;

                        const element = elements[0];
                        element.focus();
                        element.click();
                        results.margin_mode.tested = true;
                        results.margin_mode.success = true;
                        break;
                    }
                } catch (error) {
                    results.margin_mode.error = error.message;
                }
            }

            return {
                success: true,
                results: results,
                summary: {
                    total_configs: 5,
                    found: Object.values(results).filter(r => r.found).length,
                    tested: Object.values(results).filter(r => r.tested).length,
                    successful: Object.values(results).filter(r => r.success).length
                }
            };
        }
        """

        try:
            config_result = self.page.evaluate(config_test_script)

            # Take after screenshot
            after_screenshot = self.take_screenshot("after_config_test", "After trading configuration test")
            result.screenshots.append(after_screenshot)

            if config_result.get('success'):
                results_data = config_result.get('results', {})
                summary = config_result.get('summary', {})

                result.success = summary.get('found', 0) > 0
                result.details = {
                    'results': results_data,
                    'summary': summary
                }

                self.logger.info(f"✅ Trading configuration test completed:")
                self.logger.info(f"   Configurations found: {summary.get('found', 0)}/5")
                self.logger.info(f"   Configurations tested: {summary.get('tested', 0)}/5")
                self.logger.info(f"   Successful interactions: {summary.get('successful', 0)}/5")

                for config_name, config_data in results_data.items():
                    status = "✅" if config_data.get('success') else "❌" if config_data.get('tested') else "🔍" if config_data.get('found') else "❓"
                    self.logger.info(f"   {config_name}: {status}")

            else:
                error = config_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Trading configuration test failed: {error}")

        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Trading configuration test exception: {e}")

        result.duration = time.time() - start_time
        return result

    def test_popup_management(self) -> TestResult:
        """Test 3: Popup Management System (Error/Confirmation/Unexpected)"""
        self.logger.info("🧪 TEST 3: Popup Management System")

        start_time = time.time()
        result = TestResult(
            test_name="popup_management",
            success=False,
            duration=0
        )

        # Take before screenshot
        before_screenshot = self.take_screenshot("before_popup_test", "Before popup management test")
        result.screenshots.append(before_screenshot)

        popup_test_script = """
        () => {
            console.log('🧪 Testing popup management...');

            const results = {
                popups_detected: [],
                error_popups: [],
                confirmation_popups: [],
                unexpected_popups: [],
                close_attempts: [],
                confirm_attempts: []
            };

            // Comprehensive popup detection
            const popupSelectors = [
                '.ant-modal:not([style*="display: none"])',
                '.modal:not([style*="display: none"])',
                '[role="dialog"]:not([style*="display: none"])',
                '.ant-notification',
                '.ant-message',
                '.popup',
                '.overlay',
                '[class*="popup"]',
                '[class*="modal"]'
            ];

            let allPopups = [];
            popupSelectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    const rect = element.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {
                        allPopups.push({
                            element: element,
                            selector: selector,
                            text: element.textContent?.substring(0, 200) || '',
                            position: {
                                x: Math.round(rect.x),
                                y: Math.round(rect.y),
                                width: Math.round(rect.width),
                                height: Math.round(rect.height)
                            }
                        });
                    }
                });
            });

            results.popups_detected = allPopups.map(p => ({
                selector: p.selector,
                text: p.text,
                position: p.position
            }));

            // Classify popups
            allPopups.forEach((popup, index) => {
                const text = popup.text.toLowerCase();

                // Error popup detection
                if (text.includes('insufficient') ||
                    text.includes('error') ||
                    text.includes('failed') ||
                    text.includes('invalid') ||
                    text.includes('margin')) {

                    results.error_popups.push({
                        index: index,
                        type: 'error',
                        text: popup.text,
                        selector: popup.selector
                    });

                    // Try to close error popup
                    const closeButtons = popup.element.querySelectorAll(
                        '.ant-modal-close, .close, [aria-label="close"], .ant-modal-close-x, button[aria-label="Close"], .ant-modal-close-icon'
                    );

                    closeButtons.forEach((closeBtn, btnIndex) => {
                        try {
                            closeBtn.click();
                            results.close_attempts.push({
                                popup_index: index,
                                button_index: btnIndex,
                                success: true,
                                method: 'close_button'
                            });
                        } catch (error) {
                            results.close_attempts.push({
                                popup_index: index,
                                button_index: btnIndex,
                                success: false,
                                error: error.message,
                                method: 'close_button'
                            });
                        }
                    });
                }

                // Confirmation popup detection
                else if (text.includes('confirm') ||
                         text.includes('proceed') ||
                         text.includes('continue') ||
                         text.includes('leverage') ||
                         text.includes('margin mode')) {

                    results.confirmation_popups.push({
                        index: index,
                        type: 'confirmation',
                        text: popup.text,
                        selector: popup.selector
                    });

                    // Try to confirm
                    const confirmButtons = popup.element.querySelectorAll(
                        'button[class*="primary"], .ant-btn-primary, button'
                    ).filter(btn => {
                        const text = btn.textContent?.toLowerCase() || '';
                        return text.includes('confirm') || text.includes('ok') || text.includes('continue');
                    });

                    confirmButtons.forEach((confirmBtn, btnIndex) => {
                        try {
                            confirmBtn.click();
                            results.confirm_attempts.push({
                                popup_index: index,
                                button_index: btnIndex,
                                success: true,
                                method: 'confirm_button'
                            });
                        } catch (error) {
                            results.confirm_attempts.push({
                                popup_index: index,
                                button_index: btnIndex,
                                success: false,
                                error: error.message,
                                method: 'confirm_button'
                            });
                        }
                    });
                }

                // Unexpected popup
                else {
                    results.unexpected_popups.push({
                        index: index,
                        type: 'unexpected',
                        text: popup.text,
                        selector: popup.selector
                    });

                    // Try to close unexpected popup
                    const closeButtons = popup.element.querySelectorAll(
                        '.ant-modal-close, .close, [aria-label="close"], button[aria-label="Close"], .ant-modal-close-icon, button'
                    ).filter(btn => {
                        const text = btn.textContent?.toLowerCase() || '';
                        return text.includes('×') || text.includes('cancel') || text.includes('close');
                    });

                    closeButtons.forEach((closeBtn, btnIndex) => {
                        try {
                            closeBtn.click();
                            results.close_attempts.push({
                                popup_index: index,
                                button_index: btnIndex,
                                success: true,
                                method: 'unexpected_close'
                            });
                        } catch (error) {
                            results.close_attempts.push({
                                popup_index: index,
                                button_index: btnIndex,
                                success: false,
                                error: error.message,
                                method: 'unexpected_close'
                            });
                        }
                    });
                }
            });

            return {
                success: true,
                results: results,
                summary: {
                    total_popups: allPopups.length,
                    error_popups: results.error_popups.length,
                    confirmation_popups: results.confirmation_popups.length,
                    unexpected_popups: results.unexpected_popups.length,
                    close_attempts: results.close_attempts.length,
                    confirm_attempts: results.confirm_attempts.length,
                    successful_closes: results.close_attempts.filter(a => a.success).length,
                    successful_confirms: results.confirm_attempts.filter(a => a.success).length
                }
            };
        }
        """

        try:
            popup_result = self.page.evaluate(popup_test_script)

            # Wait for popup actions to complete
            time.sleep(2)

            # Take after screenshot
            after_screenshot = self.take_screenshot("after_popup_test", "After popup management test")
            result.screenshots.append(after_screenshot)

            if popup_result.get('success'):
                results_data = popup_result.get('results', {})
                summary = popup_result.get('summary', {})

                result.success = True  # Always successful if we can detect popups
                result.details = {
                    'results': results_data,
                    'summary': summary
                }

                self.logger.info(f"✅ Popup management test completed:")
                self.logger.info(f"   Total popups detected: {summary.get('total_popups', 0)}")
                self.logger.info(f"   Error popups: {summary.get('error_popups', 0)}")
                self.logger.info(f"   Confirmation popups: {summary.get('confirmation_popups', 0)}")
                self.logger.info(f"   Unexpected popups: {summary.get('unexpected_popups', 0)}")
                self.logger.info(f"   Successful closes: {summary.get('successful_closes', 0)}")
                self.logger.info(f"   Successful confirms: {summary.get('successful_confirms', 0)}")

                # Log popup details
                for popup in results_data.get('error_popups', []):
                    self.logger.info(f"   🔴 Error popup: {popup.get('text', '')[:50]}...")

                for popup in results_data.get('confirmation_popups', []):
                    self.logger.info(f"   🟡 Confirmation popup: {popup.get('text', '')[:50]}...")

            else:
                error = popup_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Popup management test failed: {error}")

        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Popup management test exception: {e}")

        result.duration = time.time() - start_time
        return result

    def test_comprehensive_fields(self) -> TestResult:
        """Test 4: Comprehensive Field Testing (All input fields)"""
        self.logger.info("🧪 TEST 4: Comprehensive Field Testing")

        start_time = time.time()
        result = TestResult(
            test_name="comprehensive_fields",
            success=False,
            duration=0
        )

        # Take before screenshot
        before_screenshot = self.take_screenshot("before_field_test", "Before comprehensive field test")
        result.screenshots.append(before_screenshot)

        field_test_script = """
        () => {
            console.log('🧪 Testing all input fields...');

            const results = {
                quantity_fields: [],
                price_fields: [],
                leverage_fields: [],
                other_fields: [],
                field_tests: []
            };

            // Find all input fields
            const allInputs = document.querySelectorAll('input');
            console.log(`Found ${allInputs.length} total input fields`);

            allInputs.forEach((input, index) => {
                const rect = input.getBoundingClientRect();
                if (rect.width === 0 || rect.height === 0) return; // Skip hidden inputs

                const fieldInfo = {
                    index: index,
                    type: input.type || 'text',
                    name: input.name || '',
                    placeholder: input.placeholder || '',
                    value: input.value || '',
                    className: input.className || '',
                    position: {
                        x: Math.round(rect.x),
                        y: Math.round(rect.y),
                        width: Math.round(rect.width),
                        height: Math.round(rect.height)
                    },
                    parent_text: ''
                };

                // Get parent context
                try {
                    let parent = input.parentElement;
                    let depth = 0;
                    while (parent && depth < 3) {
                        const parentText = parent.textContent || '';
                        if (parentText.length > fieldInfo.parent_text.length && parentText.length < 200) {
                            fieldInfo.parent_text = parentText.substring(0, 100);
                        }
                        parent = parent.parentElement;
                        depth++;
                    }
                } catch (e) {}

                // Classify field by content and context
                const lowerText = (fieldInfo.placeholder + fieldInfo.name + fieldInfo.parent_text).toLowerCase();

                if (lowerText.includes('quantity') || lowerText.includes('amount') || lowerText.includes('size')) {
                    results.quantity_fields.push(fieldInfo);
                } else if (lowerText.includes('price') || lowerText.includes('rate')) {
                    results.price_fields.push(fieldInfo);
                } else if (lowerText.includes('leverage') || lowerText.includes('multiplier')) {
                    results.leverage_fields.push(fieldInfo);
                } else if (input.type === 'number' || input.type === 'text') {
                    results.other_fields.push(fieldInfo);
                }
            });

            // Test each field category
            const testCategories = [
                { name: 'quantity', fields: results.quantity_fields, testValue: '5.0' },
                { name: 'price', fields: results.price_fields, testValue: '0.035' },
                { name: 'leverage', fields: results.leverage_fields, testValue: '10' },
                { name: 'other', fields: results.other_fields.slice(0, 5), testValue: '1.0' } // Limit other fields
            ];

            testCategories.forEach(category => {
                category.fields.forEach((fieldInfo, fieldIndex) => {
                    const input = allInputs[fieldInfo.index];
                    if (!input) return;

                    const testResult = {
                        category: category.name,
                        field_index: fieldInfo.index,
                        field_info: fieldInfo,
                        test_value: category.testValue,
                        original_value: input.value,
                        fill_attempted: false,
                        fill_successful: false,
                        value_persisted: false,
                        errors: []
                    };

                    try {
                        // Test filling the field
                        console.log(`Testing ${category.name} field ${fieldIndex}: ${fieldInfo.placeholder || fieldInfo.name || 'unnamed'}`);

                        // Focus and clear
                        input.focus();
                        input.value = '';

                        // Fill with test value
                        input.value = category.testValue;

                        // Trigger events
                        input.dispatchEvent(new Event('input', { bubbles: true }));
                        input.dispatchEvent(new Event('change', { bubbles: true }));
                        input.dispatchEvent(new Event('blur', { bubbles: true }));

                        testResult.fill_attempted = true;
                        testResult.fill_successful = input.value === category.testValue;

                        // Wait and check persistence
                        setTimeout(() => {
                            testResult.value_persisted = input.value === category.testValue;
                        }, 500);

                    } catch (error) {
                        testResult.errors.push(error.message);
                    }

                    results.field_tests.push(testResult);
                });
            });

            return {
                success: true,
                results: results,
                summary: {
                    total_inputs: allInputs.length,
                    quantity_fields: results.quantity_fields.length,
                    price_fields: results.price_fields.length,
                    leverage_fields: results.leverage_fields.length,
                    other_fields: results.other_fields.length,
                    tests_attempted: results.field_tests.length,
                    successful_fills: results.field_tests.filter(t => t.fill_successful).length,
                    persistent_values: results.field_tests.filter(t => t.value_persisted).length
                }
            };
        }
        """

        try:
            field_result = self.page.evaluate(field_test_script)

            # Wait for field persistence tests
            time.sleep(3)

            # Take after screenshot
            after_screenshot = self.take_screenshot("after_field_test", "After comprehensive field test")
            result.screenshots.append(after_screenshot)

            if field_result.get('success'):
                results_data = field_result.get('results', {})
                summary = field_result.get('summary', {})

                result.success = summary.get('successful_fills', 0) > 0
                result.details = {
                    'results': results_data,
                    'summary': summary
                }

                self.logger.info(f"✅ Comprehensive field test completed:")
                self.logger.info(f"   Total input fields: {summary.get('total_inputs', 0)}")
                self.logger.info(f"   Quantity fields: {summary.get('quantity_fields', 0)}")
                self.logger.info(f"   Price fields: {summary.get('price_fields', 0)}")
                self.logger.info(f"   Leverage fields: {summary.get('leverage_fields', 0)}")
                self.logger.info(f"   Other fields: {summary.get('other_fields', 0)}")
                self.logger.info(f"   Tests attempted: {summary.get('tests_attempted', 0)}")
                self.logger.info(f"   Successful fills: {summary.get('successful_fills', 0)}")
                self.logger.info(f"   Persistent values: {summary.get('persistent_values', 0)}")

                # Log field test details
                for test in results_data.get('field_tests', [])[:10]:  # Show first 10
                    field_info = test.get('field_info', {})
                    status = "✅" if test.get('value_persisted') else "⚠️" if test.get('fill_successful') else "❌"
                    field_desc = field_info.get('placeholder') or field_info.get('name') or f"Field {test.get('field_index')}"
                    self.logger.info(f"   {status} {test.get('category')} - {field_desc}")

            else:
                error = field_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Comprehensive field test failed: {error}")

        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Comprehensive field test exception: {e}")

        result.duration = time.time() - start_time
        return result

    def test_integration_workflow(self) -> TestResult:
        """Test 5: Integration Testing (Complete workflow)"""
        self.logger.info("🧪 TEST 5: Integration Testing - Complete Workflow")

        start_time = time.time()
        result = TestResult(
            test_name="integration_workflow",
            success=False,
            duration=0
        )

        # Take before screenshot
        before_screenshot = self.take_screenshot("before_integration_test", "Before integration workflow test")
        result.screenshots.append(before_screenshot)

        workflow_steps = []

        try:
            # Step 1: Tab navigation
            self.logger.info("🔄 Integration Step 1: Tab navigation")
            tab_result = self.test_tab_navigation()
            workflow_steps.append({"step": "tab_navigation", "success": tab_result.success})

            # Step 2: Handle any popups
            self.logger.info("🔄 Integration Step 2: Popup management")
            popup_result = self.test_popup_management()
            workflow_steps.append({"step": "popup_management", "success": popup_result.success})

            # Step 3: Configure trading settings
            self.logger.info("🔄 Integration Step 3: Trading configuration")
            config_result = self.test_trading_configuration()
            workflow_steps.append({"step": "trading_configuration", "success": config_result.success})

            # Step 4: Fill fields
            self.logger.info("🔄 Integration Step 4: Field filling")
            field_result = self.test_comprehensive_fields()
            workflow_steps.append({"step": "field_filling", "success": field_result.success})

            # Step 5: Final popup check
            self.logger.info("🔄 Integration Step 5: Final popup check")
            final_popup_result = self.test_popup_management()
            workflow_steps.append({"step": "final_popup_check", "success": final_popup_result.success})

            # Calculate overall success
            successful_steps = sum(1 for step in workflow_steps if step["success"])
            total_steps = len(workflow_steps)

            result.success = successful_steps >= 3  # At least 3 out of 5 steps successful
            result.details = {
                'workflow_steps': workflow_steps,
                'successful_steps': successful_steps,
                'total_steps': total_steps,
                'success_rate': successful_steps / total_steps if total_steps > 0 else 0
            }

            self.logger.info(f"✅ Integration workflow test completed:")
            self.logger.info(f"   Successful steps: {successful_steps}/{total_steps}")
            self.logger.info(f"   Success rate: {(successful_steps/total_steps)*100:.1f}%")

            for step in workflow_steps:
                status = "✅" if step["success"] else "❌"
                self.logger.info(f"   {status} {step['step']}")

        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Integration workflow test exception: {e}")

        # Take after screenshot
        after_screenshot = self.take_screenshot("after_integration_test", "After integration workflow test")
        result.screenshots.append(after_screenshot)

        result.duration = time.time() - start_time
        return result

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests in the comprehensive test suite"""
        self.logger.info("🧪 Starting MEXC Comprehensive Test Suite")

        if not self.connect_to_browser():
            return {"error": "Browser connection failed"}

        # Run all tests
        tests = [
            self.test_tab_navigation,
            self.test_trading_configuration,
            self.test_popup_management,
            self.test_comprehensive_fields,
            self.test_integration_workflow
        ]

        for test_func in tests:
            try:
                test_result = test_func()
                self.test_results.append(test_result)
            except Exception as e:
                self.logger.error(f"Test {test_func.__name__} failed with exception: {e}")
                failed_result = TestResult(
                    test_name=test_func.__name__,
                    success=False,
                    duration=0,
                    errors=[str(e)]
                )
                self.test_results.append(failed_result)

        # Generate comprehensive report
        return self.generate_comprehensive_report()

    def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result.success)
        total_duration = sum(result.duration for result in self.test_results)

        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": total_tests - successful_tests,
                "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
                "total_duration": total_duration,
                "total_screenshots": self.screenshot_counter
            },
            "test_results": [
                {
                    "test_name": result.test_name,
                    "success": result.success,
                    "duration": result.duration,
                    "errors": result.errors,
                    "screenshots": result.screenshots,
                    "details": result.details
                }
                for result in self.test_results
            ],
            "recommendations": self.generate_recommendations()
        }

        # Save report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"mexc_comprehensive_test_report_{timestamp}.json"

        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)

            self.logger.info(f"📊 Comprehensive test report saved: {report_file}")
        except Exception as e:
            self.logger.error(f"Failed to save test report: {e}")

        return report

    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []

        for result in self.test_results:
            if not result.success:
                if result.test_name == "tab_navigation":
                    recommendations.append("Tab navigation failed - check if tabs are present and clickable")
                elif result.test_name == "trading_configuration":
                    recommendations.append("Trading configuration failed - verify trigger type, TP/SL controls exist")
                elif result.test_name == "popup_management":
                    recommendations.append("Popup management issues - review popup detection and handling logic")
                elif result.test_name == "comprehensive_fields":
                    recommendations.append("Field testing failed - implement persistent field filling for problematic fields")
                elif result.test_name == "integration_workflow":
                    recommendations.append("Integration workflow failed - review individual test failures")

        # Add general recommendations
        successful_tests = sum(1 for result in self.test_results if result.success)
        if successful_tests < len(self.test_results):
            recommendations.append("Consider implementing retry mechanisms for failed interactions")
            recommendations.append("Add more robust element detection with multiple selector strategies")

        return recommendations

    def cleanup(self):
        """Clean up resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except Exception as e:
            self.logger.error(f"Cleanup error: {e}")

def main():
    """Main entry point for comprehensive test suite"""
    parser = argparse.ArgumentParser(description="MEXC Comprehensive Test Suite")

    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=10.0, help="Order quantity")
    parser.add_argument("--test", choices=["all", "tabs", "config", "popups", "fields", "integration"],
                       default="all", help="Specific test to run")

    args = parser.parse_args()

    config = TradeConfig(
        symbol=args.symbol,
        side=args.side,
        quantity=args.quantity
    )

    print(f"""
🧪 MEXC Comprehensive Test Suite
================================

TEST CATEGORIES:
1. 📑 Tab Navigation Testing (Open/Close tabs)
2. ⚙️ Trading Configuration Testing (Trigger Type, TP/SL)
3. 🪟 Popup Management System (Error/Confirmation/Unexpected)
4. 📝 Comprehensive Field Testing (All input fields)
5. 🔄 Integration Testing (Complete workflow)

Test Configuration:
  Symbol: {config.symbol}
  Side: {config.side}
  Quantity: {config.quantity}
  Test Mode: {args.test.upper()}
    """)

    print("Starting comprehensive test suite...")

    # Initialize test suite
    test_suite = MEXCComprehensiveTestSuite(config)

    try:
        if args.test == "all":
            report = test_suite.run_all_tests()
        else:
            # Run specific test
            test_methods = {
                "tabs": test_suite.test_tab_navigation,
                "config": test_suite.test_trading_configuration,
                "popups": test_suite.test_popup_management,
                "fields": test_suite.test_comprehensive_fields,
                "integration": test_suite.test_integration_workflow
            }

            if args.test in test_methods:
                if not test_suite.connect_to_browser():
                    print("❌ Browser connection failed")
                    return

                result = test_methods[args.test]()
                test_suite.test_results.append(result)
                report = test_suite.generate_comprehensive_report()
            else:
                print(f"❌ Unknown test: {args.test}")
                return

        # Display results
        if "error" in report:
            print(f"❌ Test suite failed: {report['error']}")
        else:
            summary = report["summary"]
            print(f"""
📊 Test Suite Results:
=====================
Total Tests: {summary['total_tests']}
Successful: {summary['successful_tests']}
Failed: {summary['failed_tests']}
Success Rate: {summary['success_rate']*100:.1f}%
Total Duration: {summary['total_duration']:.2f}s
Screenshots: {summary['total_screenshots']}

Test Details:
            """)

            for test_result in report["test_results"]:
                status = "✅" if test_result["success"] else "❌"
                print(f"  {status} {test_result['test_name']}: {test_result['duration']:.2f}s")
                if test_result["errors"]:
                    for error in test_result["errors"]:
                        print(f"    Error: {error}")

            if report["recommendations"]:
                print("\n💡 Recommendations:")
                for rec in report["recommendations"]:
                    print(f"  - {rec}")

    except KeyboardInterrupt:
        print("\n👋 Test suite interrupted")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        test_suite.cleanup()

if __name__ == "__main__":
    main()
    
    def test_trading_configuration(self) -> TestResult:
        """Test 2: Trading Configuration Testing (Trigger Type, TP/SL)"""
        self.logger.info("🧪 TEST 2: Trading Configuration Testing")
        
        start_time = time.time()
        result = TestResult(
            test_name="trading_configuration",
            success=False,
            duration=0
        )
        
        # Take before screenshot
        before_screenshot = self.take_screenshot("before_config_test", "Before trading configuration test")
        result.screenshots.append(before_screenshot)
        
        config_test_script = """
        () => {
            console.log('🧪 Testing trading configuration...');
            
            const results = {
                trigger_type: { found: false, tested: false, success: false },
                take_profit: { found: false, tested: false, success: false },
                stop_loss: { found: false, tested: false, success: false },
                leverage: { found: false, tested: false, success: false },
                margin_mode: { found: false, tested: false, success: false }
            };
            
            // Test Trigger Type (Post Only mode)
            console.log('Testing Trigger Type...');
            const triggerSelectors = [
                'select[name*="trigger"]',
                'select[name*="type"]',
                '.ant-select:has-text("Post Only")',
                'button:has-text("Post Only")',
                '[data-testid*="trigger"]',
                '[class*="trigger"]'
            ];
            
            for (const selector of triggerSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.trigger_type.found = true;
                        results.trigger_type.selector = selector;
                        results.trigger_type.count = elements.length;
                        
                        // Try to interact with first element
                        const element = elements[0];
                        element.focus();
                        element.click();
                        results.trigger_type.tested = true;
                        results.trigger_type.success = true;
                        break;
                    }
                } catch (error) {
                    results.trigger_type.error = error.message;
                }
            }
            
            // Test Take Profit
            console.log('Testing Take Profit...');
            const tpSelectors = [
                'input[name*="profit"]',
                'input[placeholder*="profit"]',
                'input[placeholder*="TP"]',
                '.take-profit input',
                '[data-testid*="profit"] input'
            ];
            
            for (const selector of tpSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.take_profit.found = true;
                        results.take_profit.selector = selector;
                        results.take_profit.count = elements.length;
                        
                        // Try to fill with test value
                        const element = elements[0];
                        element.focus();
                        element.value = '0.05';
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        results.take_profit.tested = true;
                        results.take_profit.success = element.value === '0.05';
                        break;
                    }
                } catch (error) {
                    results.take_profit.error = error.message;
                }
            }
            
            // Test Stop Loss
            console.log('Testing Stop Loss...');
            const slSelectors = [
                'input[name*="loss"]',
                'input[placeholder*="loss"]',
                'input[placeholder*="SL"]',
                '.stop-loss input',
                '[data-testid*="loss"] input'
            ];
            
            for (const selector of slSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.stop_loss.found = true;
                        results.stop_loss.selector = selector;
                        results.stop_loss.count = elements.length;
                        
                        // Try to fill with test value
                        const element = elements[0];
                        element.focus();
                        element.value = '0.02';
                        element.dispatchEvent(new Event('input', { bubbles: true }));
                        results.stop_loss.tested = true;
                        results.stop_loss.success = element.value === '0.02';
                        break;
                    }
                } catch (error) {
                    results.stop_loss.error = error.message;
                }
            }
            
            // Test Leverage
            console.log('Testing Leverage...');
            const leverageSelectors = [
                'input[name*="leverage"]',
                'input[placeholder*="leverage"]',
                '.leverage input',
                '[data-testid*="leverage"] input',
                'button:has-text("20x")',
                'button:has-text("10x")'
            ];
            
            for (const selector of leverageSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.leverage.found = true;
                        results.leverage.selector = selector;
                        results.leverage.count = elements.length;
                        
                        const element = elements[0];
                        element.focus();
                        element.click();
                        results.leverage.tested = true;
                        results.leverage.success = true;
                        break;
                    }
                } catch (error) {
                    results.leverage.error = error.message;
                }
            }
            
            // Test Margin Mode
            console.log('Testing Margin Mode...');
            const marginSelectors = [
                'button:has-text("Cross")',
                'button:has-text("Isolated")',
                '.margin-mode button',
                '[data-testid*="margin"] button'
            ];
            
            for (const selector of marginSelectors) {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        results.margin_mode.found = true;
                        results.margin_mode.selector = selector;
                        results.margin_mode.count = elements.length;
                        
                        const element = elements[0];
                        element.focus();
                        element.click();
                        results.margin_mode.tested = true;
                        results.margin_mode.success = true;
                        break;
                    }
                } catch (error) {
                    results.margin_mode.error = error.message;
                }
            }
            
            return {
                success: true,
                results: results,
                summary: {
                    total_configs: 5,
                    found: Object.values(results).filter(r => r.found).length,
                    tested: Object.values(results).filter(r => r.tested).length,
                    successful: Object.values(results).filter(r => r.success).length
                }
            };
        }
        """
        
        try:
            config_result = self.page.evaluate(config_test_script)
            
            # Take after screenshot
            after_screenshot = self.take_screenshot("after_config_test", "After trading configuration test")
            result.screenshots.append(after_screenshot)
            
            if config_result.get('success'):
                results_data = config_result.get('results', {})
                summary = config_result.get('summary', {})
                
                result.success = summary.get('found', 0) > 0
                result.details = {
                    'results': results_data,
                    'summary': summary
                }
                
                self.logger.info(f"✅ Trading configuration test completed:")
                self.logger.info(f"   Configurations found: {summary.get('found', 0)}/5")
                self.logger.info(f"   Configurations tested: {summary.get('tested', 0)}/5")
                self.logger.info(f"   Successful interactions: {summary.get('successful', 0)}/5")
                
                for config_name, config_data in results_data.items():
                    status = "✅" if config_data.get('success') else "❌" if config_data.get('tested') else "🔍" if config_data.get('found') else "❓"
                    self.logger.info(f"   {config_name}: {status}")
                
            else:
                error = config_result.get('error', 'Unknown error')
                result.errors.append(error)
                self.logger.error(f"❌ Trading configuration test failed: {error}")
                
        except Exception as e:
            result.errors.append(str(e))
            self.logger.error(f"❌ Trading configuration test exception: {e}")
        
        result.duration = time.time() - start_time
        return result
