#!/usr/bin/env python3
"""
ADVANCED SIGNATURE CRACKER
Focus on understanding the p0, k0, chash parameters and their role in signature generation
"""

import json
import hashlib
import hmac
import base64
import time
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class AdvancedSignatureCracker:
    """Advanced approach - capture MORE data and analyze the signature generation process"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        self.captured_data = []
        
        print("🔥 ADVANCED SIGNATURE CRACKER")
        print("="*40)
        print("🎯 CAPTURING DETAILED REQUEST DATA")
    
    def setup_enhanced_capture(self):
        """Setup enhanced capture to get ALL request details"""
        
        print("\n🌐 Setting up enhanced capture system...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                print("❌ No browser contexts found")
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject SUPER detailed monitoring
            self.page.evaluate("""
                window.detailedCaptures = [];
                window.captureIndex = 0;
                
                // Store original functions
                const originalFetch = window.fetch;
                const originalXHR = window.XMLHttpRequest;
                
                // Enhanced fetch override
                window.fetch = function(...args) {
                    const [url, options] = args;
                    
                    if (url.includes('order/create') || url.includes('order/submit') || url.includes('order/place')) {
                        window.captureIndex++;
                        
                        // Parse the request body to get ALL details
                        let bodyData = {};
                        try {
                            if (options.body) {
                                bodyData = JSON.parse(options.body);
                            }
                        } catch (e) {}
                        
                        const capture = {
                            id: window.captureIndex,
                            timestamp: Date.now(),
                            url: url,
                            headers: options.headers || {},
                            bodyRaw: options.body,
                            bodyParsed: bodyData,
                            // Extract key components
                            signature: (options.headers || {})['x-mxc-sign'],
                            nonce: (options.headers || {})['x-mxc-nonce'],
                            authorization: (options.headers || {})['Authorization'] || (options.headers || {})['authorization'],
                            mtoken: (options.headers || {})['mtoken'],
                            // Body components
                            symbol: bodyData.symbol,
                            side: bodyData.side,
                            price: bodyData.price,
                            vol: bodyData.vol,
                            p0: bodyData.p0,
                            k0: bodyData.k0,
                            chash: bodyData.chash,
                            ts: bodyData.ts,
                            mhash: bodyData.mhash,
                            // Additional analysis
                            p0Length: bodyData.p0 ? bodyData.p0.length : 0,
                            k0Length: bodyData.k0 ? bodyData.k0.length : 0,
                            chashLength: bodyData.chash ? bodyData.chash.length : 0,
                            // Try to decode p0 and k0
                            p0Decoded: null,
                            k0Decoded: null
                        };
                        
                        // Try to decode p0 and k0 (they look like base64)
                        try {
                            if (bodyData.p0) {
                                capture.p0Decoded = atob(bodyData.p0);
                            }
                        } catch (e) {}
                        
                        try {
                            if (bodyData.k0) {
                                capture.k0Decoded = atob(bodyData.k0);
                            }
                        } catch (e) {}
                        
                        window.detailedCaptures.push(capture);
                        
                        console.log(`🎯 DETAILED CAPTURE #${window.captureIndex}:`);
                        console.log('Signature:', capture.signature);
                        console.log('Nonce:', capture.nonce);
                        console.log('P0 length:', capture.p0Length);
                        console.log('K0 length:', capture.k0Length);
                        console.log('Chash:', capture.chash);
                        
                        alert(`DETAILED CAPTURE #${window.captureIndex} COMPLETE!\\nSignature: ${capture.signature?.substring(0,8)}...\\nP0 length: ${capture.p0Length}\\nK0 length: ${capture.k0Length}`);
                    }
                    
                    return originalFetch.apply(this, args);
                };
                
                console.log('✅ Enhanced detailed capture system ready!');
            """)
            
            print("✅ Enhanced capture system setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def capture_detailed_signatures(self):
        """Capture detailed signature data"""
        
        print("\n" + "="*60)
        print("DETAILED SIGNATURE CAPTURE SESSION")
        print("="*60)
        print()
        print("🎯 INSTRUCTIONS:")
        print("1. Place 2-3 orders with DIFFERENT parameters")
        print("2. Try different symbols, prices, quantities")
        print("3. Each capture will show detailed analysis")
        print("4. We need to understand p0, k0, chash generation")
        print()
        print("⚠️  Use very low prices to avoid fills!")
        print()
        
        target_captures = 2
        timeout = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Get detailed captures
                captures = self.page.evaluate("() => window.detailedCaptures || []")
                
                if len(captures) >= target_captures:
                    print(f"\n🎉 Captured {len(captures)} detailed signatures!")
                    self.captured_data = captures
                    return True
                
                # Show progress
                if len(captures) > 0:
                    print(f"\n📊 Captured {len(captures)} detailed signature(s)...")
                
                elapsed = int(time.time() - start_time)
                if elapsed % 20 == 0 and elapsed > 0:
                    print(f"⏱️  Waiting... ({elapsed}s elapsed)")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(2)
        
        print(f"\n⏰ Timeout. Captured {len(self.captured_data)} signatures.")
        return len(self.captured_data) > 0
    
    def analyze_detailed_captures(self):
        """Analyze the detailed captured data"""
        
        if not self.captured_data:
            print("❌ No detailed data to analyze")
            return False
        
        print(f"\n🔍 ANALYZING {len(self.captured_data)} DETAILED CAPTURES")
        print("="*55)
        
        for i, capture in enumerate(self.captured_data):
            print(f"\n📋 DETAILED CAPTURE #{i+1}:")
            print(f"   Signature: {capture['signature']}")
            print(f"   Nonce: {capture['nonce']}")
            print(f"   Symbol: {capture['symbol']}")
            print(f"   Side: {capture['side']}")
            print(f"   Price: {capture['price']}")
            print(f"   Vol: {capture['vol']}")
            print(f"   Timestamp: {capture['ts']}")
            print(f"   Chash: {capture['chash']}")
            print(f"   Mhash: {capture['mhash']}")
            print(f"   P0 length: {capture['p0Length']}")
            print(f"   K0 length: {capture['k0Length']}")
            
            # Analyze p0 and k0
            if capture.get('p0Decoded'):
                print(f"   P0 decoded length: {len(capture['p0Decoded'])}")
                print(f"   P0 decoded preview: {capture['p0Decoded'][:50]}...")
            
            if capture.get('k0Decoded'):
                print(f"   K0 decoded length: {len(capture['k0Decoded'])}")
                print(f"   K0 decoded preview: {capture['k0Decoded'][:50]}...")
            
            # Try to crack THIS specific signature
            self.crack_specific_signature(capture, i+1)
        
        return True
    
    def crack_specific_signature(self, capture, capture_num):
        """Try to crack a specific signature with all available data"""
        
        print(f"\n🧪 CRACKING SIGNATURE #{capture_num}")
        print("-" * 40)
        
        target_sig = capture['signature']
        nonce = capture['nonce']
        auth = capture['authorization']
        
        # Get all components
        components = {
            'symbol': capture['symbol'],
            'side': str(capture['side']),
            'price': capture['price'],
            'vol': str(capture['vol']),
            'ts': str(capture['ts']),
            'chash': capture['chash'],
            'mhash': capture['mhash'],
            'mtoken': capture['mtoken'],
            'p0': capture['p0'],
            'k0': capture['k0']
        }
        
        # Test signature generation WITHOUT p0/k0 (they might be added after)
        base_order = {
            'symbol': components['symbol'],
            'side': int(components['side']),
            'openType': 1,
            'type': '2',
            'vol': int(components['vol']),
            'leverage': 1,
            'marketCeiling': False,
            'price': components['price'],
            'priceProtect': '0'
        }
        
        # Try different JSON serializations
        json_variants = [
            json.dumps(base_order, separators=(',', ':')),
            json.dumps(base_order, separators=(',', ':'), sort_keys=True),
            json.dumps(base_order),
        ]
        
        print(f"   Testing {len(json_variants)} JSON variants...")
        
        for json_str in json_variants:
            # Test basic combinations
            test_inputs = [
                f"{auth}{nonce}{json_str}",
                f"{nonce}{json_str}{auth}",
                f"{auth}{nonce}",
                f"{nonce}{auth}",
                f"{auth}{nonce}{components['chash']}",
                f"{nonce}{components['chash']}{auth}",
                f"{auth}{nonce}{components['mhash']}",
                f"{nonce}{components['mhash']}{auth}",
                f"{auth}{nonce}{components['ts']}",
                f"{nonce}{components['ts']}{auth}",
                # Complex combinations
                f"{auth}{nonce}{components['symbol']}{components['side']}{components['price']}{components['vol']}",
                f"{nonce}{components['symbol']}{components['side']}{components['price']}{components['vol']}{auth}",
            ]
            
            for test_input in test_inputs:
                # Test MD5
                result = hashlib.md5(test_input.encode()).hexdigest()
                if result == target_sig:
                    print(f"🎉 CRACKED! MD5({test_input[:50]}...)")
                    print(f"   Full input: {test_input}")
                    return True
                
                # Test SHA256 (first 32 chars)
                result = hashlib.sha256(test_input.encode()).hexdigest()[:32]
                if result == target_sig:
                    print(f"🎉 CRACKED! SHA256({test_input[:50]}...)[:32]")
                    print(f"   Full input: {test_input}")
                    return True
                
                # Test HMAC-MD5
                try:
                    result = hmac.new(auth.encode(), test_input.encode(), hashlib.md5).hexdigest()
                    if result == target_sig:
                        print(f"🎉 CRACKED! HMAC-MD5(key=auth, msg={test_input[:50]}...)")
                        print(f"   Full input: {test_input}")
                        return True
                except:
                    pass
        
        print(f"   ❌ Failed to crack signature #{capture_num}")
        return False
    
    def run_advanced_crack(self):
        """Run the advanced cracking process"""
        
        print("="*60)
        print("🔥 ADVANCED SIGNATURE CRACKING ATTACK 🔥")
        print("="*60)
        
        # Setup enhanced capture
        if not self.setup_enhanced_capture():
            return False
        
        try:
            # Capture detailed data
            if not self.capture_detailed_signatures():
                print("❌ Failed to capture detailed signatures")
                return False
            
            # Analyze and crack
            if self.analyze_detailed_captures():
                print("\n🎉 ADVANCED ANALYSIS COMPLETE!")
                return True
            else:
                print("\n❌ Advanced analysis failed")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main cracking function"""
    
    cracker = AdvancedSignatureCracker()
    success = cracker.run_advanced_crack()
    
    if success:
        print("\n🚀 SIGNATURE ALGORITHM CRACKED!")
    else:
        print("\n🔧 Continue the attack with more data...")

if __name__ == '__main__':
    main()
