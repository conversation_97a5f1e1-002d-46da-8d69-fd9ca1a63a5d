"""
MEXC API Integration
===================

Official MEXC API integration for account information and spot trading.
Note: MEXC doesn't provide futures position control API, so we keep browser automation for futures.
"""

import hashlib
import hmac
import time
import json
import aiohttp
from typing import Dict, Any, Optional, List
from datetime import datetime
from urllib.parse import urlencode

from src.utils.logger import get_logger
from src.config import settings

logger = get_logger(__name__)


class MEXCAPIClient:
    """MEXC API Client for account information and spot trading"""
    
    def __init__(self, api_key: str = None, api_secret: str = None):
        """
        Initialize MEXC API client
        
        Args:
            api_key: MEXC API key
            api_secret: MEXC API secret
        """
        self.api_key = api_key or getattr(settings, 'MEXC_API_KEY', None)
        self.api_secret = api_secret or getattr(settings, 'MEXC_API_SECRET', None)
        
        # API endpoints
        self.spot_base_url = "https://api.mexc.com"
        self.futures_base_url = "https://contract.mexc.com"
        
        # Session for HTTP requests
        self.session: Optional[aiohttp.ClientSession] = None
        
        self.logger = logger
        
        if not self.api_key or not self.api_secret:
            self.logger.warning("MEXC API credentials not provided - some features will be unavailable")
    
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature for API requests"""
        if not self.api_secret:
            raise ValueError("API secret not provided")
        
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _get_headers(self, signed: bool = False) -> Dict[str, str]:
        """Get headers for API requests"""
        headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'MEXC-Trading-System/1.0'
        }
        
        if signed and self.api_key:
            headers['X-MEXC-APIKEY'] = self.api_key
        
        return headers
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Dict[str, Any] = None,
        signed: bool = False,
        base_url: str = None
    ) -> Dict[str, Any]:
        """Make HTTP request to MEXC API"""
        if not self.session:
            raise RuntimeError("Session not initialized - use async context manager")
        
        if not self.api_key or not self.api_secret:
            raise ValueError("API credentials not provided")
        
        base_url = base_url or self.spot_base_url
        url = f"{base_url}{endpoint}"
        
        params = params or {}
        
        if signed:
            params['timestamp'] = int(time.time() * 1000)
            query_string = urlencode(params)
            params['signature'] = self._generate_signature(query_string)
        
        headers = self._get_headers(signed=signed)
        
        try:
            if method.upper() == 'GET':
                async with self.session.get(url, params=params, headers=headers) as response:
                    data = await response.json()
            elif method.upper() == 'POST':
                async with self.session.post(url, json=params, headers=headers) as response:
                    data = await response.json()
            else:
                raise ValueError(f"Unsupported HTTP method: {method}")
            
            if response.status != 200:
                self.logger.error(f"MEXC API error: {response.status} - {data}")
                raise Exception(f"MEXC API error: {data}")
            
            return data
            
        except Exception as e:
            self.logger.error(f"MEXC API request failed: {e}")
            raise
    
    async def get_account_info(self) -> Dict[str, Any]:
        """Get spot account information"""
        try:
            data = await self._make_request('GET', '/api/v3/account', signed=True)
            
            # Process account data
            account_info = {
                'account_type': 'spot',
                'can_trade': data.get('canTrade', False),
                'can_withdraw': data.get('canWithdraw', False),
                'can_deposit': data.get('canDeposit', False),
                'update_time': data.get('updateTime'),
                'balances': []
            }
            
            # Process balances
            for balance in data.get('balances', []):
                free_balance = float(balance.get('free', 0))
                locked_balance = float(balance.get('locked', 0))
                total_balance = free_balance + locked_balance
                
                if total_balance > 0:  # Only include non-zero balances
                    account_info['balances'].append({
                        'asset': balance.get('asset'),
                        'free': free_balance,
                        'locked': locked_balance,
                        'total': total_balance
                    })
            
            self.logger.info(f"Retrieved spot account info: {len(account_info['balances'])} assets")
            return account_info
            
        except Exception as e:
            self.logger.error(f"Failed to get account info: {e}")
            return {
                'error': str(e),
                'account_type': 'spot',
                'balances': []
            }
    
    async def get_futures_account_info(self) -> Dict[str, Any]:
        """Get futures account information (limited API support)"""
        try:
            # Note: MEXC futures API is limited, this is what's available
            data = await self._make_request(
                'GET', 
                '/api/v1/private/account/assets',
                signed=True,
                base_url=self.futures_base_url
            )
            
            account_info = {
                'account_type': 'futures',
                'total_wallet_balance': 0,
                'total_unrealized_pnl': 0,
                'total_margin_balance': 0,
                'available_balance': 0,
                'assets': []
            }
            
            # Process futures account data if available
            if isinstance(data, dict) and 'data' in data:
                for asset in data.get('data', []):
                    asset_info = {
                        'asset': asset.get('currency'),
                        'wallet_balance': float(asset.get('positionMargin', 0)),
                        'unrealized_pnl': float(asset.get('unrealizedPnl', 0)),
                        'margin_balance': float(asset.get('availableMargin', 0)),
                        'available_balance': float(asset.get('cashBalance', 0))
                    }
                    account_info['assets'].append(asset_info)
                    
                    # Sum totals
                    account_info['total_wallet_balance'] += asset_info['wallet_balance']
                    account_info['total_unrealized_pnl'] += asset_info['unrealized_pnl']
                    account_info['total_margin_balance'] += asset_info['margin_balance']
                    account_info['available_balance'] += asset_info['available_balance']
            
            self.logger.info(f"Retrieved futures account info: {len(account_info['assets'])} assets")
            return account_info
            
        except Exception as e:
            self.logger.error(f"Failed to get futures account info: {e}")
            return {
                'error': str(e),
                'account_type': 'futures',
                'assets': [],
                'note': 'MEXC futures API has limited support'
            }
    
    async def get_spot_balances(self) -> List[Dict[str, Any]]:
        """Get spot balances only"""
        try:
            account_info = await self.get_account_info()
            return account_info.get('balances', [])
        except Exception as e:
            self.logger.error(f"Failed to get spot balances: {e}")
            return []
    
    async def get_trading_symbols(self) -> List[Dict[str, Any]]:
        """Get available trading symbols"""
        try:
            data = await self._make_request('GET', '/api/v3/exchangeInfo')
            
            symbols = []
            for symbol_info in data.get('symbols', []):
                if symbol_info.get('status') == 'TRADING':
                    symbols.append({
                        'symbol': symbol_info.get('symbol'),
                        'base_asset': symbol_info.get('baseAsset'),
                        'quote_asset': symbol_info.get('quoteAsset'),
                        'status': symbol_info.get('status'),
                        'is_spot_trading_allowed': symbol_info.get('isSpotTradingAllowed', False),
                        'is_margin_trading_allowed': symbol_info.get('isMarginTradingAllowed', False)
                    })
            
            self.logger.info(f"Retrieved {len(symbols)} trading symbols")
            return symbols
            
        except Exception as e:
            self.logger.error(f"Failed to get trading symbols: {e}")
            return []
    
    async def get_24hr_ticker(self, symbol: str = None) -> Dict[str, Any]:
        """Get 24hr ticker statistics"""
        try:
            params = {}
            if symbol:
                params['symbol'] = symbol
            
            data = await self._make_request('GET', '/api/v3/ticker/24hr', params=params)
            
            if symbol:
                # Single symbol response
                return {
                    'symbol': data.get('symbol'),
                    'price_change': float(data.get('priceChange', 0)),
                    'price_change_percent': float(data.get('priceChangePercent', 0)),
                    'last_price': float(data.get('lastPrice', 0)),
                    'volume': float(data.get('volume', 0)),
                    'quote_volume': float(data.get('quoteVolume', 0)),
                    'high_price': float(data.get('highPrice', 0)),
                    'low_price': float(data.get('lowPrice', 0)),
                    'open_price': float(data.get('openPrice', 0)),
                    'count': int(data.get('count', 0))
                }
            else:
                # All symbols response
                tickers = []
                for ticker in data:
                    tickers.append({
                        'symbol': ticker.get('symbol'),
                        'price_change_percent': float(ticker.get('priceChangePercent', 0)),
                        'last_price': float(ticker.get('lastPrice', 0)),
                        'volume': float(ticker.get('volume', 0))
                    })
                return {'tickers': tickers}
            
        except Exception as e:
            self.logger.error(f"Failed to get 24hr ticker: {e}")
            return {'error': str(e)}
    
    async def test_connectivity(self) -> Dict[str, Any]:
        """Test API connectivity"""
        try:
            # Test public endpoint
            await self._make_request('GET', '/api/v3/ping')
            
            # Test private endpoint if credentials available
            if self.api_key and self.api_secret:
                await self._make_request('GET', '/api/v3/account', signed=True)
                return {
                    'status': 'connected',
                    'public_api': True,
                    'private_api': True,
                    'timestamp': datetime.now().isoformat()
                }
            else:
                return {
                    'status': 'connected',
                    'public_api': True,
                    'private_api': False,
                    'message': 'API credentials not provided',
                    'timestamp': datetime.now().isoformat()
                }
                
        except Exception as e:
            self.logger.error(f"API connectivity test failed: {e}")
            return {
                'status': 'failed',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }


# Global MEXC API client instance
mexc_api_client = None


async def get_mexc_client() -> MEXCAPIClient:
    """Get MEXC API client instance"""
    global mexc_api_client
    
    if not mexc_api_client:
        api_key = getattr(settings, 'MEXC_API_KEY', None)
        api_secret = getattr(settings, 'MEXC_API_SECRET', None)
        mexc_api_client = MEXCAPIClient(api_key, api_secret)
    
    return mexc_api_client


async def test_mexc_api() -> Dict[str, Any]:
    """Test MEXC API connectivity"""
    async with MEXCAPIClient() as client:
        return await client.test_connectivity()
