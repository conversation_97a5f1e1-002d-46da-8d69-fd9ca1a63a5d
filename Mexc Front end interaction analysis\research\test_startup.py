#!/usr/bin/env python3
"""
MEXC Trading System - Fixed Startup Script
Includes Windows Playwright compatibility fixes and comprehensive testing
"""

import asyncio
import platform
import sys
import os
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def setup_windows_compatibility():
    """Setup Windows-specific compatibility fixes"""
    if platform.system().lower() == "windows":
        if hasattr(asyncio, 'WindowsSelectorEventLoopPolicy'):
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
            print("✅ Set WindowsSelectorEventLoopPolicy for Playwright compatibility")
            return True
    return False

async def test_system_health():
    """Test system components after startup"""
    print("\n🔍 Testing System Health...")

    try:
        # Import after event loop policy is set
        from src.core.session_manager import SessionManager
        from src.utils.telegram_bot import TelegramBot
        from src.config import settings

        # Test session manager
        print("  📋 Testing session manager...")
        telegram_bot = TelegramBot(settings.TELEGRAM_BOT_TOKEN, settings.TELEGRAM_CHAT_ID)
        session_manager = SessionManager(pool_size=1, telegram_bot=telegram_bot)

        # Test browser detection
        try:
            browser_exe = session_manager._find_browser_executable()
            print(f"  ✅ Browser found: {browser_exe}")
        except Exception as e:
            print(f"  ❌ Browser detection failed: {e}")
            return False

        print("  ✅ System health check passed")
        return True

    except Exception as e:
        print(f"  ❌ System health check failed: {e}")
        return False

async def main():
    """Main startup with comprehensive fixes and testing"""

    print("🚀 MEXC Trading System - Enhanced Startup")
    print("=" * 50)

    # Apply Windows compatibility fixes
    windows_fixed = setup_windows_compatibility()

    # Test system health before starting server
    health_ok = await test_system_health()

    if not health_ok:
        print("❌ System health check failed. Please check the logs.")
        return

    # Import after all fixes are applied
    import uvicorn
    from main import app

    print("\n🎯 Starting server with the following endpoints:")
    print("📊 Main Dashboard: http://localhost:8000")
    print("📋 Sessions: http://localhost:8000/dashboard/sessions")
    print("💹 Trades: http://localhost:8000/dashboard/trades")
    print("⚙️ Config: http://localhost:8000/dashboard/config")
    print("🧪 Test Webhook: http://localhost:8000/webhook/test")
    print("🔧 Debug Port: http://localhost:9222 (when sessions are active)")
    print("📚 API Docs: http://localhost:8000/docs")
    print("\n" + "="*50)
    print("⏳ Starting server... (this may take 30-60 seconds)")
    print("🔍 Watch for 'Browser launched successfully' in the logs")
    print("✅ Success indicator: 'Session pool created: 3/3 sessions'")
    print("❌ Failure indicator: 'Session pool created: 0/3 sessions'")
    print("=" * 50)

    # Start the server
    config = uvicorn.Config(
        app=app,
        host="127.0.0.1",
        port=8000,
        log_level="info",
        reload=False
    )

    server = uvicorn.Server(config)
    await server.serve()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Shutting down MEXC Trading System...")
    except Exception as e:
        print(f"❌ Startup Error: {e}")
        print("\n🔧 Troubleshooting Tips:")
        print("1. Check if Chrome is installed")
        print("2. Verify .env file configuration")
        print("3. Check logs/trading_system.log for details")
        print("4. Try running with HEADLESS_MODE=true")
        sys.exit(1)
