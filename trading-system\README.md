# MEXC High-Speed Futures Trading System

A production-ready trading system for MEXC futures with browser automation, session management, and TradingView webhook integration.

## Features

- **High-Speed Trading**: 2-5 second execution times with optimized browser automation
- **Session Management**: Automatic session pooling with health monitoring
- **TradingView Integration**: Direct webhook support for trading signals
- **Telegram Notifications**: Real-time alerts for session management and trade status
- **Web Dashboard**: Responsive admin interface for monitoring and control
- **Docker Deployment**: Ready for Linux server deployment

## Architecture

```
TradingView → FastAPI Webhook → Session Manager → Browser Pool → MEXC → Order Execution
                              ↓
                        Telegram Bot ← Health Monitor ← Session Pool
```

## Quick Start

### 1. Installation

```bash
# Clone and navigate
cd trading-system

# Install dependencies
pip install -r requirements.txt

# Install Playwright browsers
playwright install chromium
```

### 2. Configuration

```bash
# Copy environment template
cp .env.template .env

# Edit configuration
nano .env
```

### 3. Run Development Server

```bash
# Start the trading system
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. Access Dashboard

Open http://localhost:8000 in your browser for the admin dashboard.

## Configuration

### Environment Variables

```bash
# MEXC Configuration
MEXC_BASE_URL=https://futures.mexc.com
MEXC_SESSION_TIMEOUT=432000  # 5 days in seconds

# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_CHAT_ID=your_chat_id

# Trading Configuration
MAX_CONCURRENT_TRADES=5
DEFAULT_LEVERAGE=1
RISK_MANAGEMENT_ENABLED=true

# Session Management
SESSION_POOL_SIZE=3
SESSION_HEALTH_CHECK_INTERVAL=1800  # 30 minutes
SESSION_EXPIRY_WARNING_HOURS=24

# Performance
BROWSER_POOL_SIZE=3
HEADLESS_MODE=true
ENABLE_NETWORK_INTERCEPTION=true
```

### TradingView Webhook Setup

Configure your TradingView alerts to send webhooks to:
```
POST http://your-server:8000/webhook/tradingview
```

Webhook payload format:
```json
{
  "action": "buy|sell|close",
  "symbol": "BTC_USDT",
  "price": 45000.50,
  "quantity": 0.1,
  "leverage": 10,
  "stop_loss": 44000.00,
  "take_profit": 46000.00
}
```

## Usage

### Starting the System

```bash
# Production mode
python main.py

# Development mode with auto-reload
python -m uvicorn main:app --reload
```

### Session Management

The system automatically manages browser sessions:
- Maintains a pool of 3 authenticated sessions
- Monitors session health every 30 minutes
- Sends Telegram alerts 24 hours before expiry
- Handles session rotation and failover

### Manual Session Setup

If sessions expire, you'll receive a Telegram notification. To re-authenticate:

1. Open the provided browser instance
2. Log into MEXC manually
3. Complete 2FA if required
4. The system will automatically detect the new session

## API Endpoints

### Webhook Endpoints
- `POST /webhook/tradingview` - Receive TradingView signals
- `POST /webhook/test` - Test webhook functionality

### Management Endpoints
- `GET /api/status` - System status and health
- `GET /api/sessions` - Session pool status
- `POST /api/sessions/refresh` - Force session refresh
- `GET /api/trades` - Recent trade history

### Dashboard
- `GET /` - Main dashboard
- `GET /dashboard/sessions` - Session management
- `GET /dashboard/trades` - Trade monitoring
- `GET /dashboard/config` - Configuration panel

## Monitoring

### Health Checks

The system provides comprehensive health monitoring:
- Session validity checks
- Browser pool status
- Network connectivity
- Trade execution metrics

### Telegram Notifications

Automatic notifications for:
- Session expiry warnings (24h, 6h, 1h before)
- Trade execution confirmations
- System errors and alerts
- Health check failures

### Logging

Structured logging with multiple levels:
- `INFO`: Normal operations and trade confirmations
- `WARNING`: Session issues and recoverable errors
- `ERROR`: Critical failures requiring attention
- `DEBUG`: Detailed execution traces (development only)

## Deployment

### Docker Deployment

```bash
# Build image
docker build -t mexc-trading-system .

# Run container
docker run -d \
  --name mexc-trader \
  -p 8000:8000 \
  -v $(pwd)/.env:/app/.env \
  -v $(pwd)/data:/app/data \
  mexc-trading-system
```

### Linux Server Setup

```bash
# Install system dependencies
sudo apt update
sudo apt install -y python3 python3-pip chromium-browser

# Install Python dependencies
pip3 install -r requirements.txt
playwright install chromium

# Setup systemd service
sudo cp mexc-trader.service /etc/systemd/system/
sudo systemctl enable mexc-trader
sudo systemctl start mexc-trader
```

## Security

- Session data encrypted at rest using Fernet encryption
- API keys stored in environment variables only
- No credentials logged or stored in plain text
- Browser sessions isolated in separate user data directories
- Optional proxy support for additional anonymity

## Troubleshooting

### Common Issues

1. **Session Expired**: Check Telegram notifications and re-authenticate manually
2. **Browser Crashes**: System automatically restarts browser instances
3. **Network Issues**: Built-in retry logic with exponential backoff
4. **Rate Limiting**: Automatic request throttling and queue management

### Debug Mode

Enable debug logging:
```bash
export LOG_LEVEL=DEBUG
python main.py
```

### Support

For issues and questions:
1. Check the logs in `logs/` directory
2. Review session status in the dashboard
3. Verify Telegram bot connectivity
4. Test webhook endpoints manually

## Performance Optimization

- Pre-warmed browser sessions for instant execution
- Selector caching to reduce DOM queries
- Network request interception for faster confirmations
- Parallel processing of multiple signals
- Connection pooling for HTTP requests

## Risk Management

- Configurable position sizing and leverage limits
- Stop-loss and take-profit order support
- Maximum concurrent trades protection
- Emergency stop functionality
- Trade confirmation requirements

## License

This software is for educational and research purposes. Users are responsible for compliance with MEXC's terms of service and applicable regulations.
