#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Simple Test Script
======================

Simple test script to verify the comprehensive automation system works.
Based on the proven blur prevention technique.
"""

import os
import sys
import time
import json
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class MEXCSimpleTest:
    """Simple test for MEXC automation"""
    
    def __init__(self, symbol="TRU_USDT", quantity=1.0):
        self.symbol = symbol
        self.quantity = quantity
        
        # Setup logging without emojis
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"MEXC Simple Test initialized: {symbol} quantity {quantity}")
    
    def connect_to_browser(self) -> bool:
        """Connect to Chrome browser"""
        self.logger.info("Connecting to Chrome browser...")
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                self.logger.error("No browser contexts found")
                return False
            
            context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in context.pages:
                url = page.url or ''
                if 'mexc.com' in url and 'futures' in url:
                    mexc_page = page
                    break
            
            if not mexc_page:
                self.logger.error("MEXC page not found")
                return False
            
            self.page = mexc_page
            self.logger.info(f"Connected to MEXC: {self.page.url}")
            return True
            
        except Exception as e:
            self.logger.error(f"Connection failed: {e}")
            return False
    
    def test_element_detection(self) -> dict:
        """Test element detection"""
        self.logger.info("Testing element detection...")
        
        detection_script = """
        () => {
            console.log('Testing MEXC element detection...');
            
            const results = {
                inputs: [],
                buttons: [],
                tabs: [],
                dropdowns: []
            };
            
            // Find all input fields
            const inputs = document.querySelectorAll('input.ant-input');
            inputs.forEach((input, index) => {
                const rect = input.getBoundingClientRect();
                const placeholder = input.placeholder || '';
                const value = input.value || '';
                
                results.inputs.push({
                    index: index,
                    x: Math.round(rect.x),
                    y: Math.round(rect.y),
                    width: Math.round(rect.width),
                    height: Math.round(rect.height),
                    placeholder: placeholder,
                    value: value,
                    visible: rect.width > 0 && rect.height > 0
                });
            });
            
            // Find buttons
            const buyButton = document.querySelector('button.component_longBtn__eazYU');
            const sellButton = document.querySelector('button.component_shortBtn__x5P3I');
            
            if (buyButton) {
                const rect = buyButton.getBoundingClientRect();
                results.buttons.push({
                    type: 'buy',
                    text: buyButton.textContent,
                    x: Math.round(rect.x),
                    y: Math.round(rect.y),
                    visible: rect.width > 0 && rect.height > 0
                });
            }
            
            if (sellButton) {
                const rect = sellButton.getBoundingClientRect();
                results.buttons.push({
                    type: 'sell',
                    text: sellButton.textContent,
                    x: Math.round(rect.x),
                    y: Math.round(rect.y),
                    visible: rect.width > 0 && rect.height > 0
                });
            }
            
            // Find tabs
            const tabs = document.querySelectorAll('.ant-tabs-tab');
            tabs.forEach((tab, index) => {
                results.tabs.push({
                    index: index,
                    text: tab.textContent,
                    active: tab.classList.contains('ant-tabs-tab-active')
                });
            });
            
            // Find dropdowns
            const dropdowns = document.querySelectorAll('.ant-select-selector');
            results.dropdowns.push({
                count: dropdowns.length
            });
            
            console.log('Element detection results:', results);
            return results;
        }
        """
        
        try:
            result = self.page.evaluate(detection_script)
            
            self.logger.info("Element Detection Results:")
            self.logger.info(f"  Input fields: {len(result.get('inputs', []))}")
            self.logger.info(f"  Buttons: {len(result.get('buttons', []))}")
            self.logger.info(f"  Tabs: {len(result.get('tabs', []))}")
            self.logger.info(f"  Dropdowns: {result.get('dropdowns', [{}])[0].get('count', 0)}")
            
            # Log input field details
            for i, input_field in enumerate(result.get('inputs', [])):
                if input_field.get('visible', False):
                    self.logger.info(f"    Input {i}: pos({input_field['x']}, {input_field['y']}) "
                                   f"placeholder='{input_field['placeholder']}' value='{input_field['value']}'")
            
            # Log button details
            for button in result.get('buttons', []):
                if button.get('visible', False):
                    self.logger.info(f"    Button {button['type']}: '{button['text']}' at ({button['x']}, {button['y']})")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Element detection failed: {e}")
            return {}
    
    def test_quantity_field_interaction(self) -> bool:
        """Test quantity field interaction with blur prevention"""
        self.logger.info("Testing quantity field interaction...")
        
        quantity_test_script = f"""
        () => {{
            console.log('Testing quantity field interaction...');
            
            // Find quantity field - try multiple methods
            let quantityField = null;
            
            // Method 1: Position-based (668, 603)
            const inputs = document.querySelectorAll('input.ant-input');
            for (const input of inputs) {{
                const rect = input.getBoundingClientRect();
                if (Math.abs(rect.x - 668) < 20 && Math.abs(rect.y - 603) < 50 && rect.width > 0) {{
                    quantityField = input;
                    console.log(`Found quantity field by position: (${{rect.x}}, ${{rect.y}})`);
                    break;
                }}
            }}
            
            // Method 2: Find empty field after price field
            if (!quantityField) {{
                for (let i = 0; i < inputs.length - 1; i++) {{
                    const currentInput = inputs[i];
                    const nextInput = inputs[i + 1];
                    
                    if (currentInput.value && currentInput.value.includes('.') && 
                        parseFloat(currentInput.value) > 0 && !nextInput.value) {{
                        quantityField = nextInput;
                        console.log('Found quantity field as empty field after price field');
                        break;
                    }}
                }}
            }}
            
            // Method 3: Find by placeholder or nearby text
            if (!quantityField) {{
                for (const input of inputs) {{
                    const placeholder = input.placeholder || '';
                    if (placeholder.toLowerCase().includes('quantity') || 
                        placeholder.toLowerCase().includes('amount')) {{
                        quantityField = input;
                        console.log(`Found quantity field by placeholder: "${{placeholder}}"`);
                        break;
                    }}
                }}
            }}
            
            if (!quantityField) {{
                return {{ success: false, error: 'Quantity field not found' }};
            }}
            
            // Setup blur prevention
            console.log('Setting up blur prevention...');
            
            const originalBlur = quantityField.blur;
            quantityField.blur = function() {{
                console.log('Blur event blocked');
                return;
            }};
            
            quantityField.addEventListener('blur', function(event) {{
                console.log('Blur event captured and blocked');
                event.preventDefault();
                event.stopPropagation();
                event.stopImmediatePropagation();
                return false;
            }}, true);
            
            // Test field interaction
            const originalValue = quantityField.value;
            
            quantityField.focus();
            quantityField.value = '';
            quantityField.value = '{self.quantity}';
            
            quantityField.dispatchEvent(new Event('input', {{ bubbles: true }}));
            quantityField.dispatchEvent(new Event('change', {{ bubbles: true }}));
            
            const newValue = quantityField.value;
            const success = newValue === '{self.quantity}';
            
            console.log(`Quantity field test: '${{originalValue}}' -> '${{newValue}}' (success: ${{success}})`);
            
            return {{
                success: success,
                originalValue: originalValue,
                newValue: newValue,
                position: {{
                    x: Math.round(quantityField.getBoundingClientRect().x),
                    y: Math.round(quantityField.getBoundingClientRect().y)
                }}
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(quantity_test_script)
            
            if result.get('success'):
                self.logger.info("Quantity field test PASSED!")
                self.logger.info(f"  Original: '{result.get('originalValue', '')}'")
                self.logger.info(f"  New: '{result.get('newValue', '')}'")
                self.logger.info(f"  Position: {result.get('position', {})}")
                return True
            else:
                error = result.get('error', 'Unknown error')
                self.logger.error(f"Quantity field test FAILED: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"Quantity field test error: {e}")
            return False
    
    def run_comprehensive_test(self) -> dict:
        """Run comprehensive test"""
        self.logger.info("Starting comprehensive MEXC test...")
        
        results = {
            "success": False,
            "tests_passed": [],
            "tests_failed": [],
            "element_detection": {},
            "duration": 0
        }
        
        start_time = time.time()
        
        try:
            # Test 1: Browser connection
            if not self.connect_to_browser():
                results["tests_failed"].append("browser_connection")
                return results
            results["tests_passed"].append("browser_connection")
            
            # Test 2: Element detection
            detection_result = self.test_element_detection()
            results["element_detection"] = detection_result
            if detection_result:
                results["tests_passed"].append("element_detection")
            else:
                results["tests_failed"].append("element_detection")
            
            # Test 3: Quantity field interaction
            if self.test_quantity_field_interaction():
                results["tests_passed"].append("quantity_field_interaction")
            else:
                results["tests_failed"].append("quantity_field_interaction")
            
            # Overall success
            results["success"] = len(results["tests_failed"]) == 0
            
        except Exception as e:
            self.logger.error(f"Test error: {e}")
            results["tests_failed"].append(f"exception: {str(e)}")
        
        finally:
            results["duration"] = time.time() - start_time
        
        return results
    
    def cleanup(self):
        """Cleanup resources"""
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    """Main test function"""
    print("MEXC Simple Test Script")
    print("======================")
    print("Testing comprehensive automation system...")
    
    test = MEXCSimpleTest()
    
    try:
        results = test.run_comprehensive_test()
        
        print(f"\nTest Results:")
        print(f"Success: {results['success']}")
        print(f"Duration: {results['duration']:.2f}s")
        print(f"Tests Passed: {len(results['tests_passed'])}")
        print(f"Tests Failed: {len(results['tests_failed'])}")
        
        if results['tests_passed']:
            print(f"Passed: {', '.join(results['tests_passed'])}")
        
        if results['tests_failed']:
            print(f"Failed: {', '.join(results['tests_failed'])}")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"mexc_simple_test_report_{timestamp}.json"
        with open(report_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nTest report saved: {report_file}")
        
    except KeyboardInterrupt:
        print("\nTest interrupted")
    except Exception as e:
        print(f"\nTest error: {e}")
    finally:
        test.cleanup()

if __name__ == "__main__":
    main()
