#!/usr/bin/env python3
"""
FINAL COMPREHENSIVE CRACKER
Capture everything and provide manual analysis tools
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class FinalComprehensiveCracker:
    """Final comprehensive approach with manual analysis"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🎯 FINAL COMPREHENSIVE CRACKER")
        print("="*40)
        print("🔥 CAPTURING EVERYTHING FOR MANUAL ANALYSIS")
    
    def setup_comprehensive_hooks(self):
        """Setup comprehensive hooks for everything"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject COMPREHENSIVE hooks
            self.page.evaluate("""
                window.allData = {
                    signatures: [],
                    entropy: [],
                    headers: [],
                    requests: [],
                    crypto: []
                };
                
                console.log('🎯 Installing COMPREHENSIVE hooks...');
                
                // Hook ALL XMLHttpRequest operations
                const originalXHROpen = XMLHttpRequest.prototype.open;
                const originalXHRSend = XMLHttpRequest.prototype.send;
                const originalXHRSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
                
                XMLHttpRequest.prototype.open = function(method, url, ...args) {
                    this._method = method;
                    this._url = url;
                    return originalXHROpen.apply(this, arguments);
                };
                
                XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                    if (!this._headers) this._headers = {};
                    this._headers[name] = value;
                    
                    // Capture ALL headers
                    window.allData.headers.push({
                        name: name,
                        value: value,
                        timestamp: Date.now(),
                        url: this._url
                    });
                    
                    // Special handling for signatures
                    if (name.toLowerCase().includes('sign') || name.toLowerCase().includes('nonce')) {
                        console.log(`🔥 HEADER: ${name} = ${value}`);
                        
                        if (name.toLowerCase() === 'x-mxc-sign') {
                            window.allData.signatures.push({
                                signature: value,
                                timestamp: Date.now(),
                                url: this._url,
                                method: this._method,
                                allHeaders: {...this._headers}
                            });
                            
                            console.log('🎉 SIGNATURE CAPTURED:', value);
                        }
                    }
                    
                    return originalXHRSetRequestHeader.apply(this, arguments);
                };
                
                XMLHttpRequest.prototype.send = function(data) {
                    // Capture request data
                    if (this._url && this._url.includes('order')) {
                        window.allData.requests.push({
                            method: this._method,
                            url: this._url,
                            headers: {...this._headers},
                            data: data,
                            timestamp: Date.now()
                        });
                        
                        console.log('🚀 ORDER REQUEST:', this._url);
                    }
                    
                    return originalXHRSend.apply(this, arguments);
                };
                
                // Hook fetch as well
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const [url, options] = args;
                    
                    if (url.includes('order') || (options && options.headers)) {
                        console.log('🚀 FETCH REQUEST:', url);
                        
                        window.allData.requests.push({
                            type: 'fetch',
                            url: url,
                            options: options,
                            timestamp: Date.now()
                        });
                        
                        // Check for signatures in fetch headers
                        if (options && options.headers) {
                            for (const [name, value] of Object.entries(options.headers)) {
                                if (name.toLowerCase().includes('sign')) {
                                    console.log(`🔥 FETCH SIGNATURE: ${name} = ${value}`);
                                    
                                    window.allData.signatures.push({
                                        signature: value,
                                        timestamp: Date.now(),
                                        url: url,
                                        type: 'fetch',
                                        headers: options.headers
                                    });
                                }
                            }
                        }
                    }
                    
                    return originalFetch.apply(this, args);
                };
                
                // Hook crypto operations
                if (window.crypto && window.crypto.getRandomValues) {
                    const originalGetRandomValues = window.crypto.getRandomValues;
                    window.crypto.getRandomValues = function(array) {
                        const result = originalGetRandomValues.apply(this, arguments);
                        
                        const randomData = Array.from(array);
                        const randomHex = randomData.map(b => b.toString(16).padStart(2, '0')).join('');
                        
                        window.allData.entropy.push({
                            type: 'crypto_random',
                            hex: randomHex,
                            length: array.length,
                            timestamp: Date.now()
                        });
                        
                        return result;
                    };
                }
                
                // Hook CryptoJS if available
                if (window.CryptoJS) {
                    ['MD5', 'SHA1', 'SHA256', 'HmacMD5', 'HmacSHA1', 'HmacSHA256'].forEach(method => {
                        if (window.CryptoJS[method]) {
                            const original = window.CryptoJS[method];
                            window.CryptoJS[method] = function(...args) {
                                const result = original.apply(this, args);
                                
                                console.log(`🔥 CryptoJS.${method}:`, args, '=>', result.toString());
                                
                                window.allData.crypto.push({
                                    method: method,
                                    args: args.map(arg => arg.toString().substring(0, 100)),
                                    result: result.toString(),
                                    timestamp: Date.now()
                                });
                                
                                return result;
                            };
                        }
                    });
                }
                
                console.log('✅ COMPREHENSIVE hooks installed!');
                console.log('📊 Monitoring: signatures, entropy, headers, requests, crypto');
            """)
            
            print("✅ Comprehensive hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def monitor_and_analyze(self):
        """Monitor all data and provide real-time analysis"""
        
        print("\n📊 MONITORING ALL DATA")
        print("="*30)
        print()
        print("🎯 PLACE ORDERS NOW!")
        print("   - All network activity is being captured")
        print("   - Real-time signature analysis")
        print("   - Manual analysis tools available")
        print()
        
        timeout = 600  # 10 minutes
        start_time = time.time()
        last_counts = {'signatures': 0, 'entropy': 0, 'headers': 0, 'requests': 0, 'crypto': 0}
        
        while time.time() - start_time < timeout:
            try:
                # Get all captured data
                all_data = self.page.evaluate("() => window.allData || {}")
                
                # Check for new data
                current_counts = {
                    'signatures': len(all_data.get('signatures', [])),
                    'entropy': len(all_data.get('entropy', [])),
                    'headers': len(all_data.get('headers', [])),
                    'requests': len(all_data.get('requests', [])),
                    'crypto': len(all_data.get('crypto', []))
                }
                
                # Report new signatures
                if current_counts['signatures'] > last_counts['signatures']:
                    new_signatures = all_data['signatures'][last_counts['signatures']:]
                    
                    for i, sig_data in enumerate(new_signatures):
                        sig_num = last_counts['signatures'] + i + 1
                        print(f"\n🔥 SIGNATURE #{sig_num}: {sig_data['signature']}")
                        print(f"   URL: {sig_data.get('url', 'Unknown')}")
                        print(f"   Method: {sig_data.get('method', 'Unknown')}")
                        
                        # Try to crack this signature
                        if self.attempt_crack(sig_data, all_data):
                            print(f"🎉 SIGNATURE CRACKED!")
                            return True
                
                # Report new crypto operations
                if current_counts['crypto'] > last_counts['crypto']:
                    new_crypto = all_data['crypto'][last_counts['crypto']:]
                    
                    for crypto_op in new_crypto:
                        print(f"\n🔐 CRYPTO: {crypto_op['method']}")
                        print(f"   Result: {crypto_op['result']}")
                        
                        # Check if this crypto result matches any signature
                        for sig_data in all_data.get('signatures', []):
                            if crypto_op['result'] == sig_data['signature']:
                                print(f"🎉🎉🎉 CRYPTO MATCH FOUND! 🎉🎉🎉")
                                print(f"   Method: {crypto_op['method']}")
                                print(f"   Args: {crypto_op['args']}")
                                print(f"   Signature: {sig_data['signature']}")
                                return True
                
                # Update counts
                last_counts = current_counts
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:
                    print(f"⏱️  Monitoring... ({elapsed}s)")
                    print(f"   📊 Signatures: {current_counts['signatures']}")
                    print(f"   📊 Entropy: {current_counts['entropy']}")
                    print(f"   📊 Headers: {current_counts['headers']}")
                    print(f"   📊 Requests: {current_counts['requests']}")
                    print(f"   📊 Crypto: {current_counts['crypto']}")
                
                time.sleep(2)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(2)
        
        print(f"\n⏰ Monitoring complete")
        
        # Provide final analysis
        try:
            final_data = self.page.evaluate("() => window.allData || {}")
            self.provide_manual_analysis(final_data)
        except:
            pass
        
        return False
    
    def attempt_crack(self, sig_data, all_data):
        """Attempt to crack a signature with available data"""
        
        signature = sig_data['signature']
        timestamp = sig_data['timestamp']
        
        # Get recent entropy (within 30 seconds of signature)
        recent_entropy = [
            e for e in all_data.get('entropy', [])
            if abs(e['timestamp'] - timestamp) < 30000
        ]
        
        print(f"   🧪 Testing with {len(recent_entropy)} recent entropy values...")
        
        # Test with recent entropy
        for entropy in recent_entropy[-10:]:  # Last 10 entropy values
            if entropy['type'] == 'crypto_random':
                entropy_hex = entropy['hex']
                
                # Quick test of most likely patterns
                test_patterns = [
                    entropy_hex + self.auth,
                    self.auth + entropy_hex,
                    entropy_hex + "1754929178532",
                    self.auth + entropy_hex + "1754929178532",
                ]
                
                for pattern in test_patterns:
                    test_sig = hashlib.md5(pattern.encode()).hexdigest()
                    if test_sig == signature:
                        print(f"🎉🎉🎉 SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"   Algorithm: MD5({pattern})")
                        print(f"   Entropy: {entropy_hex}")
                        return True
        
        return False
    
    def provide_manual_analysis(self, final_data):
        """Provide manual analysis tools and data export"""
        
        print(f"\n📋 FINAL ANALYSIS REPORT")
        print("="*35)
        
        signatures = final_data.get('signatures', [])
        entropy = final_data.get('entropy', [])
        crypto_ops = final_data.get('crypto', [])
        
        print(f"📊 Total signatures captured: {len(signatures)}")
        print(f"📊 Total entropy values: {len(entropy)}")
        print(f"📊 Total crypto operations: {len(crypto_ops)}")
        
        if signatures:
            print(f"\n🔐 CAPTURED SIGNATURES:")
            for i, sig in enumerate(signatures):
                print(f"   {i+1}. {sig['signature']}")
        
        if crypto_ops:
            print(f"\n🔐 CRYPTO OPERATIONS:")
            for i, op in enumerate(crypto_ops):
                print(f"   {i+1}. {op['method']}: {op['result']}")
        
        # Export data for manual analysis
        try:
            with open('captured_data.json', 'w') as f:
                json.dump(final_data, f, indent=2)
            print(f"\n💾 Data exported to: captured_data.json")
        except:
            pass
        
        print(f"\n🎯 MANUAL ANALYSIS SUGGESTIONS:")
        print("1. Check if any crypto operation results match signatures")
        print("2. Analyze entropy timing correlation with signatures")
        print("3. Look for patterns in signature generation timing")
        print("4. Test combinations of auth + entropy + nonce")
    
    def run_comprehensive_analysis(self):
        """Run the comprehensive analysis"""
        
        print("="*60)
        print("🎯 FINAL COMPREHENSIVE ANALYSIS")
        print("="*60)
        
        # Setup hooks
        if not self.setup_comprehensive_hooks():
            return False
        
        try:
            # Monitor and analyze
            if self.monitor_and_analyze():
                print("\n🎉 SIGNATURE ALGORITHM CRACKED!")
                return True
            else:
                print("\n📋 Analysis complete - check captured_data.json")
                return False
            
        finally:
            # Cleanup
            try:
                if hasattr(self, 'browser'):
                    self.browser.close()
                if hasattr(self, 'playwright'):
                    self.playwright.stop()
            except:
                pass

def main():
    """Main function"""
    
    cracker = FinalComprehensiveCracker()
    cracker.run_comprehensive_analysis()

if __name__ == '__main__':
    main()
