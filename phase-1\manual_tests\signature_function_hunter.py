#!/usr/bin/env python3
"""
SIGNATURE FUNCTION HUNTER
Target the large script and extract the actual signature generation function
"""

import json
import time
import re
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class SignatureFunctionHunter:
    """Hunt down the exact signature generation function"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🎯 SIGNATURE FUNCTION HUNTER")
        print("="*35)
        print("🔥 TARGETING THE LARGE SCRIPT")
    
    def setup_browser(self):
        """Setup browser"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def extract_large_script(self):
        """Extract the large script that likely contains signature function"""
        
        print("\n📜 EXTRACTING LARGE SIGNATURE SCRIPT")
        print("="*45)
        
        # Get the largest script with signature keywords
        script_content = self.page.evaluate("""
            () => {
                const scripts = document.querySelectorAll('script');
                let largestScript = null;
                let maxScore = 0;
                
                scripts.forEach(script => {
                    if (script.textContent) {
                        const content = script.textContent;
                        const keywords = ['sign', 'sha', 'crypto', 'auth', 'token', 'hash', 'md5'];
                        let score = 0;
                        
                        keywords.forEach(keyword => {
                            const matches = content.toLowerCase().split(keyword.toLowerCase()).length - 1;
                            score += matches;
                        });
                        
                        if (score > maxScore && content.length > 100000) {
                            maxScore = score;
                            largestScript = content;
                        }
                    }
                });
                
                return {
                    content: largestScript,
                    score: maxScore,
                    length: largestScript ? largestScript.length : 0
                };
            }
        """)
        
        if script_content['content']:
            print(f"✅ Found large script: {script_content['length']} chars, score: {script_content['score']}")
            return script_content['content']
        else:
            print("❌ No large script found")
            return None
    
    def search_signature_patterns(self, script_content):
        """Search for signature generation patterns in the script"""
        
        print(f"\n🔍 SEARCHING SIGNATURE PATTERNS")
        print("="*40)
        
        if not script_content:
            return []
        
        # Search for specific patterns that might be signature generation
        patterns = [
            # Look for x-mxc-sign header setting
            r'["\']x-mxc-sign["\'][^}]{0,200}',
            # Look for signature calculation
            r'signature[^}]{0,200}',
            r'sign[^}]{0,200}=.*?[a-f0-9]{32}',
            # Look for MD5/SHA functions
            r'md5\([^)]{0,200}\)',
            r'sha\d+\([^)]{0,200}\)',
            r'hmac[^}]{0,200}',
            # Look for crypto operations
            r'CryptoJS\.[^}]{0,200}',
            # Look for nonce + auth combinations
            r'nonce[^}]{0,200}auth',
            r'auth[^}]{0,200}nonce',
            # Look for header construction
            r'headers[^}]{0,500}x-mxc-sign',
        ]
        
        found_patterns = []
        
        for pattern in patterns:
            matches = re.finditer(pattern, script_content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                # Get more context around the match
                start = max(0, match.start() - 100)
                end = min(len(script_content), match.end() + 100)
                context = script_content[start:end]
                
                found_patterns.append({
                    'pattern': pattern,
                    'match': match.group(0),
                    'context': context,
                    'position': match.start()
                })
        
        print(f"✅ Found {len(found_patterns)} signature patterns")
        
        # Sort by position to see the flow
        found_patterns.sort(key=lambda x: x['position'])
        
        return found_patterns
    
    def analyze_signature_patterns(self, patterns):
        """Analyze the found patterns to understand signature generation"""
        
        print(f"\n🧪 ANALYZING SIGNATURE PATTERNS")
        print("="*40)
        
        if not patterns:
            print("❌ No patterns to analyze")
            return
        
        # Group patterns by type
        pattern_groups = {
            'header_setting': [],
            'crypto_operations': [],
            'signature_calculation': [],
            'nonce_auth': [],
            'other': []
        }
        
        for pattern_info in patterns:
            match = pattern_info['match'].lower()
            
            if 'x-mxc-sign' in match:
                pattern_groups['header_setting'].append(pattern_info)
            elif any(crypto in match for crypto in ['md5', 'sha', 'hmac', 'cryptojs']):
                pattern_groups['crypto_operations'].append(pattern_info)
            elif 'signature' in match or 'sign' in match:
                pattern_groups['signature_calculation'].append(pattern_info)
            elif 'nonce' in match and 'auth' in match:
                pattern_groups['nonce_auth'].append(pattern_info)
            else:
                pattern_groups['other'].append(pattern_info)
        
        # Analyze each group
        for group_name, group_patterns in pattern_groups.items():
            if group_patterns:
                print(f"\n📋 {group_name.upper()} ({len(group_patterns)} patterns):")
                
                for i, pattern_info in enumerate(group_patterns[:3]):  # Show top 3
                    print(f"   🔍 Pattern #{i+1}:")
                    print(f"      Match: {pattern_info['match'][:100]}...")
                    print(f"      Context: ...{pattern_info['context'][:200]}...")
    
    def extract_signature_function_directly(self):
        """Try to extract the signature function directly from the page"""
        
        print(f"\n🎯 DIRECT SIGNATURE FUNCTION EXTRACTION")
        print("="*50)
        
        # Try to find and call the signature function directly
        signature_test = self.page.evaluate("""
            () => {
                const results = {
                    attempts: [],
                    success: false,
                    workingFunction: null
                };
                
                // Test data
                const testAuth = 'WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6';
                const testNonce = '1754927000000';
                const testData = {
                    symbol: 'BTC_USDT',
                    side: 1,
                    price: '1000',
                    vol: 1
                };
                
                // Look for signature functions in all possible places
                function searchForSignatureFunctions(obj, path = '', depth = 0) {
                    if (depth > 3) return;
                    
                    try {
                        for (const key in obj) {
                            if (typeof obj[key] === 'function') {
                                const funcName = key.toLowerCase();
                                const funcStr = obj[key].toString().toLowerCase();
                                
                                // Check if function looks like signature generation
                                if ((funcName.includes('sign') || funcName.includes('hash') || 
                                     funcName.includes('crypto')) &&
                                    (funcStr.includes('md5') || funcStr.includes('sha') || 
                                     funcStr.includes('hmac') || funcStr.includes('sign'))) {
                                    
                                    try {
                                        // Try calling with different parameter combinations
                                        const testCalls = [
                                            [testAuth, testNonce],
                                            [testNonce, testAuth],
                                            [testAuth, testNonce, JSON.stringify(testData)],
                                            [testData, testAuth, testNonce],
                                            [testAuth],
                                            [testNonce],
                                            [JSON.stringify(testData)]
                                        ];
                                        
                                        for (let i = 0; i < testCalls.length; i++) {
                                            try {
                                                const result = obj[key](...testCalls[i]);
                                                
                                                if (result && typeof result === 'string' && 
                                                    result.length === 32 && /^[a-f0-9]+$/i.test(result)) {
                                                    
                                                    results.success = true;
                                                    results.workingFunction = {
                                                        path: path + '.' + key,
                                                        name: key,
                                                        params: testCalls[i],
                                                        result: result,
                                                        source: obj[key].toString().substring(0, 500)
                                                    };
                                                    
                                                    return true;
                                                }
                                                
                                                results.attempts.push({
                                                    path: path + '.' + key,
                                                    name: key,
                                                    params: testCalls[i].map(p => typeof p),
                                                    result: typeof result === 'string' ? result.substring(0, 50) : typeof result,
                                                    resultLength: typeof result === 'string' ? result.length : 0
                                                });
                                                
                                            } catch (e) {
                                                // Function call failed
                                            }
                                        }
                                    } catch (e) {
                                        // Function access failed
                                    }
                                }
                            } else if (typeof obj[key] === 'object' && obj[key] !== null && depth < 2) {
                                if (searchForSignatureFunctions(obj[key], path + '.' + key, depth + 1)) {
                                    return true;
                                }
                            }
                        }
                    } catch (e) {
                        // Object access failed
                    }
                    
                    return false;
                }
                
                // Search in common places
                const searchSpaces = [
                    { obj: window, name: 'window' },
                    { obj: document, name: 'document' },
                ];
                
                // Add crypto libraries if they exist
                if (window.CryptoJS) searchSpaces.push({ obj: window.CryptoJS, name: 'CryptoJS' });
                if (window.crypto) searchSpaces.push({ obj: window.crypto, name: 'crypto' });
                if (window.forge) searchSpaces.push({ obj: window.forge, name: 'forge' });
                
                for (const space of searchSpaces) {
                    if (searchForSignatureFunctions(space.obj, space.name)) {
                        break;
                    }
                }
                
                return results;
            }
        """)
        
        print(f"✅ Direct extraction completed")
        print(f"   Attempts: {len(signature_test['attempts'])}")
        print(f"   Success: {signature_test['success']}")
        
        if signature_test['success']:
            working_func = signature_test['workingFunction']
            print(f"\n🎉 SIGNATURE FUNCTION FOUND!")
            print(f"   Function: {working_func['path']}")
            print(f"   Name: {working_func['name']}")
            print(f"   Params: {working_func['params']}")
            print(f"   Result: {working_func['result']}")
            print(f"   Source: {working_func['source'][:200]}...")
            return working_func
        else:
            print(f"\n🔍 No working signature function found")
            if signature_test['attempts']:
                print(f"   Sample attempts:")
                for attempt in signature_test['attempts'][:5]:
                    print(f"      {attempt['path']}: {attempt['result']} (len: {attempt['resultLength']})")
            return None
    
    def run_hunt(self):
        """Run the complete signature function hunt"""
        
        print("="*60)
        print("🎯 SIGNATURE FUNCTION HUNTING IN PROGRESS")
        print("="*60)
        
        # Setup browser
        if not self.setup_browser():
            return False
        
        try:
            # Extract large script
            script_content = self.extract_large_script()
            
            # Search for signature patterns
            patterns = self.search_signature_patterns(script_content)
            
            # Analyze patterns
            self.analyze_signature_patterns(patterns)
            
            # Try direct extraction
            working_function = self.extract_signature_function_directly()
            
            if working_function:
                print("\n🎉 SIGNATURE FUNCTION SUCCESSFULLY HUNTED!")
                return True
            else:
                print("\n🔍 Hunt continues - function may be obfuscated")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main hunting function"""
    
    hunter = SignatureFunctionHunter()
    success = hunter.run_hunt()
    
    if success:
        print("\n🚀 SIGNATURE FUNCTION CAPTURED!")
        print("Ready for reverse engineering!")
    else:
        print("\n⚔️ HUNT CONTINUES...")
        print("Function may be heavily obfuscated or encrypted")

if __name__ == '__main__':
    main()
