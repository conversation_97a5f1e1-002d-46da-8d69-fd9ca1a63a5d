#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Trading System Direct Test
===================================

Direct test of the enhanced trading system without API server.
Tests the core automation functionality directly.
"""

import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.integration.enhanced_integration import enhanced_trading_system
from src.core.enhanced_trading_engine import TradeRequest, OrderType


class EnhancedDirectTest:
    """Direct test of enhanced trading system"""
    
    def __init__(self):
        self.test_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "start_time": datetime.now(),
            "end_time": None
        }
        
        print("Enhanced Trading System Direct Test")
        print("=" * 40)
        print("Testing core automation functionality")
        print()
    
    def log_test_result(self, test_name: str, success: bool, details: str = "", data: Dict[str, Any] = None):
        """Log test result"""
        self.test_results["total_tests"] += 1
        
        if success:
            self.test_results["passed_tests"] += 1
            status = "PASS"
            symbol = "✅"
        else:
            self.test_results["failed_tests"] += 1
            status = "FAIL"
            symbol = "❌"
        
        result = {
            "test_name": test_name,
            "status": status,
            "details": details,
            "data": data or {},
            "timestamp": datetime.now().isoformat()
        }
        
        self.test_results["test_details"].append(result)
        
        print(f"{symbol} {test_name}: {status}")
        if details:
            print(f"   Details: {details}")
        print()
    
    async def test_enhanced_system_initialization(self) -> bool:
        """Test enhanced system initialization"""
        try:
            success = await enhanced_trading_system.initialize()
            
            self.log_test_result(
                "Enhanced System Initialization",
                success,
                f"System initialized: {success}"
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Enhanced System Initialization",
                False,
                f"Initialization error: {str(e)}"
            )
            return False
    
    async def test_trade_request_creation(self) -> bool:
        """Test trade request creation"""
        try:
            trade_request = TradeRequest(
                action="buy",
                symbol="TRU_USDT",
                side="long",
                quantity=1.0,
                price=0.03334,
                leverage=1,
                order_type=OrderType.LIMIT
            )
            
            # Verify trade request properties
            success = (
                trade_request.action == "buy" and
                trade_request.symbol == "TRU_USDT" and
                trade_request.quantity == 1.0 and
                trade_request.price == 0.03334
            )
            
            self.log_test_result(
                "Trade Request Creation",
                success,
                f"Trade request created with correct properties",
                trade_request.to_dict()
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Trade Request Creation",
                False,
                f"Trade request creation error: {str(e)}"
            )
            return False
    
    async def test_webhook_parsing(self) -> bool:
        """Test webhook data parsing"""
        try:
            webhook_data = {
                "action": "buy",
                "symbol": "TRU_USDT",
                "side": "long",
                "quantity": 2.5,
                "price": 0.03334,
                "leverage": 1,
                "order_type": "limit",
                "strategy": "test_strategy"
            }
            
            # Test parsing using the enhanced system's internal method
            trade_request = enhanced_trading_system._parse_webhook_to_trade_request(webhook_data)
            
            success = (
                trade_request is not None and
                trade_request.action == "buy" and
                trade_request.symbol == "TRU_USDT" and
                trade_request.quantity == 2.5
            )
            
            self.log_test_result(
                "Webhook Data Parsing",
                success,
                f"Webhook parsed correctly: {success}",
                trade_request.to_dict() if trade_request else {}
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Webhook Data Parsing",
                False,
                f"Webhook parsing error: {str(e)}"
            )
            return False
    
    async def test_system_status(self) -> bool:
        """Test system status retrieval"""
        try:
            status = enhanced_trading_system.get_system_status()
            
            success = (
                isinstance(status, dict) and
                "initialized" in status and
                "system_type" in status
            )
            
            self.log_test_result(
                "System Status Retrieval",
                success,
                f"Status retrieved successfully",
                status
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "System Status Retrieval",
                False,
                f"Status retrieval error: {str(e)}"
            )
            return False
    
    async def test_browser_connection_check(self) -> bool:
        """Test browser connection availability"""
        try:
            import requests
            
            # Check if Chrome remote debugging is available
            response = requests.get("http://127.0.0.1:9222/json", timeout=5)
            
            if response.status_code == 200:
                tabs = response.json()
                mexc_tabs = [tab for tab in tabs if 'mexc.com' in tab.get('url', '')]
                
                success = len(mexc_tabs) > 0
                
                self.log_test_result(
                    "Browser Connection Check",
                    success,
                    f"Found {len(mexc_tabs)} MEXC tabs out of {len(tabs)} total tabs",
                    {"total_tabs": len(tabs), "mexc_tabs": len(mexc_tabs)}
                )
                
                return success
            else:
                self.log_test_result(
                    "Browser Connection Check",
                    False,
                    f"Chrome remote debugging not accessible: HTTP {response.status_code}"
                )
                return False
                
        except Exception as e:
            self.log_test_result(
                "Browser Connection Check",
                False,
                f"Browser connection error: {str(e)}"
            )
            return False
    
    async def test_comprehensive_automation_dry_run(self) -> bool:
        """Test comprehensive automation in dry run mode"""
        try:
            # This would test the actual automation system
            # For now, we'll test the system components
            
            # Check if the enhanced system is properly configured
            if not enhanced_trading_system.is_initialized:
                self.log_test_result(
                    "Comprehensive Automation Dry Run",
                    False,
                    "Enhanced system not initialized"
                )
                return False
            
            # Test creating a manual trade request
            result = await enhanced_trading_system.execute_manual_trade(
                action="buy",
                symbol="TRU_USDT",
                side="long",
                quantity=0.1,  # Small test quantity
                price=0.03334,
                leverage=1,
                order_type="limit"
            )
            
            # In a real scenario, this would execute the trade
            # For testing, we check if the request was processed
            success = isinstance(result, dict) and "success" in result
            
            self.log_test_result(
                "Comprehensive Automation Dry Run",
                success,
                f"Automation system processed request: {success}",
                result
            )
            
            return success
            
        except Exception as e:
            self.log_test_result(
                "Comprehensive Automation Dry Run",
                False,
                f"Automation dry run error: {str(e)}"
            )
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all direct tests"""
        print("Starting Enhanced Trading System Direct Tests...")
        print()
        
        # Test 1: System Initialization
        init_success = await self.test_enhanced_system_initialization()
        
        # Test 2: Trade Request Creation
        request_success = await self.test_trade_request_creation()
        
        # Test 3: Webhook Parsing
        webhook_success = await self.test_webhook_parsing()
        
        # Test 4: System Status
        status_success = await self.test_system_status()
        
        # Test 5: Browser Connection Check
        browser_success = await self.test_browser_connection_check()
        
        # Test 6: Comprehensive Automation Dry Run
        automation_success = await self.test_comprehensive_automation_dry_run()
        
        # Calculate results
        self.test_results["end_time"] = datetime.now()
        duration = (self.test_results["end_time"] - self.test_results["start_time"]).total_seconds()
        
        success_rate = 0
        if self.test_results["total_tests"] > 0:
            success_rate = (self.test_results["passed_tests"] / self.test_results["total_tests"]) * 100
        
        # Print summary
        print("=" * 40)
        print("ENHANCED DIRECT TEST RESULTS")
        print("=" * 40)
        print(f"Total Tests: {self.test_results['total_tests']}")
        print(f"Passed: {self.test_results['passed_tests']}")
        print(f"Failed: {self.test_results['failed_tests']}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Duration: {duration:.2f}s")
        print()
        
        if success_rate == 100:
            print("🎉 ALL DIRECT TESTS PASSED!")
            print("✅ Enhanced trading system core functionality working")
            print("✅ Ready for API server integration")
        elif success_rate >= 80:
            print("⚠️  Most tests passed, minor issues detected")
        else:
            print("❌ Multiple test failures detected")
            print("🚨 Core system issues need to be resolved")
        
        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = f"enhanced_direct_test_report_{timestamp}.json"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"📊 Test report saved: {report_file}")
        
        return self.test_results


async def main():
    """Main test function"""
    test_runner = EnhancedDirectTest()
    results = await test_runner.run_all_tests()
    
    # Return appropriate exit code
    if results["failed_tests"] == 0:
        return 0  # Success
    else:
        return 1  # Failure


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
