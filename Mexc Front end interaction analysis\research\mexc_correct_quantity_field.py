#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MEXC Correct Quantity Field
Target the actual Quantity (USDT) field, not the Price (USDT) field.

ISSUE: Script was filling Price field instead of Quantity field
SOLUTION: Specifically target Quantity field with dropdown arrow
"""

import os
import sys
import time
import logging
from datetime import datetime
from playwright.sync_api import sync_playwright

class MEXCCorrectQuantityField:
    """Target the correct Quantity (USDT) field with dropdown"""
    
    def __init__(self, symbol="TRU_USDT", side="BUY", quantity=2.5):
        self.symbol = symbol
        self.side = side
        self.quantity = quantity
        
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(message)s')
        self.logger = logging.getLogger(__name__)
        
        self.playwright = None
        self.browser = None
        self.page = None
        
        self.logger.info(f"🎯 CORRECT QUANTITY FIELD: {side} {quantity} {symbol}")
    
    def connect(self):
        """Connect to MEXC"""
        self.logger.info("🔌 Connecting...")
        
        self.playwright = sync_playwright().start()
        self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
        
        context = self.browser.contexts[0]
        for page in context.pages:
            if 'mexc.com' in (page.url or '') and 'testnet' not in (page.url or ''):
                self.page = page
                break
        
        if not self.page:
            self.logger.error("❌ MEXC page not found")
            return False
        
        self.logger.info(f"✅ Connected: {self.page.url}")
        return True
    
    def find_and_fill_correct_quantity_field(self):
        """Find the correct Quantity (USDT) field with dropdown arrow"""
        self.logger.info("🎯 FINDING CORRECT QUANTITY (USDT) FIELD...")
        
        quantity_field_script = f"""
        () => {{
            console.log('🎯 Searching for Quantity (USDT) field with dropdown...');
            
            const testValue = '{self.quantity}';
            const results = {{
                fields_analyzed: [],
                quantity_field_found: false,
                price_field_found: false,
                filled_field: null,
                dropdown_found: false
            }};
            
            // STEP 1: Analyze all input fields and their labels
            console.log('Step 1: Analyzing all input fields and their labels...');
            
            const allInputs = document.querySelectorAll('input.ant-input');
            console.log(`Found ${{allInputs.length}} input fields`);
            
            allInputs.forEach((input, index) => {{
                const rect = input.getBoundingClientRect();
                if (rect.width > 0 && rect.height > 0 && !input.disabled) {{
                    
                    // Get context from parent elements
                    let context = '';
                    let parent = input.parentElement;
                    let depth = 0;
                    
                    while (parent && depth < 5) {{
                        const parentText = parent.textContent || '';
                        if (parentText.includes('Quantity') || parentText.includes('Price') || 
                            parentText.includes('USDT') || parentText.includes('Amount')) {{
                            context = parentText.substring(0, 100);
                            break;
                        }}
                        parent = parent.parentElement;
                        depth++;
                    }}
                    
                    // Look for dropdown arrow near this input
                    let hasDropdown = false;
                    const nearbyElements = input.parentElement?.querySelectorAll('*') || [];
                    for (const element of nearbyElements) {{
                        const elementText = element.textContent || '';
                        const elementClass = element.className || '';
                        if (elementClass.includes('arrow') || elementClass.includes('dropdown') || 
                            elementClass.includes('select') || elementText.includes('▼') || 
                            elementText.includes('↓')) {{
                            hasDropdown = true;
                            break;
                        }}
                    }}
                    
                    const fieldInfo = {{
                        index: index,
                        placeholder: input.placeholder || '',
                        value: input.value || '',
                        className: input.className || '',
                        context: context,
                        hasDropdown: hasDropdown,
                        position: {{
                            x: Math.round(rect.x),
                            y: Math.round(rect.y)
                        }}
                    }};
                    
                    results.fields_analyzed.push(fieldInfo);
                    
                    console.log(`Field ${{index}}: position(${{fieldInfo.position.x}}, ${{fieldInfo.position.y}})`);
                    console.log(`  Placeholder: "${{fieldInfo.placeholder}}"`);
                    console.log(`  Value: "${{fieldInfo.value}}"`);
                    console.log(`  Context: "${{fieldInfo.context}}"`);
                    console.log(`  Has dropdown: ${{fieldInfo.hasDropdown}}`);
                    
                    // Identify field type
                    if (fieldInfo.context.toLowerCase().includes('quantity')) {{
                        console.log(`  → IDENTIFIED AS QUANTITY FIELD`);
                        results.quantity_field_found = true;
                    }} else if (fieldInfo.context.toLowerCase().includes('price')) {{
                        console.log(`  → IDENTIFIED AS PRICE FIELD`);
                        results.price_field_found = true;
                    }}
                }}
            }});
            
            // STEP 2: Find the correct Quantity field
            console.log('\\nStep 2: Finding the correct Quantity field...');
            
            let targetInput = null;
            
            // Method 1: Look for field with "Quantity" in context
            for (const fieldInfo of results.fields_analyzed) {{
                if (fieldInfo.context.toLowerCase().includes('quantity') && 
                    fieldInfo.context.toLowerCase().includes('usdt')) {{
                    targetInput = allInputs[fieldInfo.index];
                    console.log(`✅ Found Quantity (USDT) field by context at (${{fieldInfo.position.x}}, ${{fieldInfo.position.y}})`);
                    results.dropdown_found = fieldInfo.hasDropdown;
                    break;
                }}
            }}
            
            // Method 2: Look for empty field with dropdown (Quantity fields are usually empty by default)
            if (!targetInput) {{
                for (const fieldInfo of results.fields_analyzed) {{
                    if (fieldInfo.value === '' && fieldInfo.hasDropdown) {{
                        targetInput = allInputs[fieldInfo.index];
                        console.log(`✅ Found empty field with dropdown at (${{fieldInfo.position.x}}, ${{fieldInfo.position.y}})`);
                        results.dropdown_found = true;
                        break;
                    }}
                }}
            }}
            
            // Method 3: Look for field that's NOT the price field (price usually has a value)
            if (!targetInput) {{
                for (const fieldInfo of results.fields_analyzed) {{
                    if (!fieldInfo.context.toLowerCase().includes('price') && 
                        fieldInfo.value === '') {{
                        targetInput = allInputs[fieldInfo.index];
                        console.log(`✅ Found non-price empty field at (${{fieldInfo.position.x}}, ${{fieldInfo.position.y}})`);
                        break;
                    }}
                }}
            }}
            
            if (!targetInput) {{
                console.log('❌ Could not identify Quantity field');
                return {{ success: false, error: 'Quantity field not found', results: results }};
            }}
            
            // STEP 3: Fill the correct field
            console.log('\\nStep 3: Filling the correct Quantity field...');
            
            try {{
                const rect = targetInput.getBoundingClientRect();
                console.log(`Filling field at (${{rect.x}}, ${{rect.y}}) with "${{testValue}}"`);
                
                // Focus the field
                targetInput.focus();
                targetInput.scrollIntoView();
                
                // Clear any existing value
                targetInput.value = '';
                targetInput.select();
                
                // Fill with quantity value
                targetInput.value = testValue;
                
                // Trigger comprehensive events
                const events = [
                    new Event('focus', {{ bubbles: true }}),
                    new Event('input', {{ bubbles: true }}),
                    new Event('change', {{ bubbles: true }}),
                    new KeyboardEvent('keyup', {{ key: 'Enter', bubbles: true }}),
                    new Event('blur', {{ bubbles: true }})
                ];
                
                events.forEach(event => {{
                    targetInput.dispatchEvent(event);
                }});
                
                // Verify the value stuck
                const finalValue = targetInput.value;
                const success = finalValue === testValue;
                
                results.filled_field = {{
                    position: {{ x: Math.round(rect.x), y: Math.round(rect.y) }},
                    placeholder: targetInput.placeholder,
                    className: targetInput.className,
                    initial_value: '',
                    final_value: finalValue,
                    success: success
                }};
                
                console.log(`Fill result: "${{finalValue}}" (success: ${{success}})`);
                
                return {{
                    success: success,
                    results: results,
                    summary: {{
                        quantity_field_found: results.quantity_field_found,
                        price_field_found: results.price_field_found,
                        dropdown_found: results.dropdown_found,
                        fill_success: success,
                        final_value: finalValue
                    }}
                }};
                
            }} catch (error) {{
                console.log(`❌ Error filling field: ${{error.message}}`);
                return {{ success: false, error: error.message, results: results }};
            }}
        }}
        """
        
        try:
            result = self.page.evaluate(quantity_field_script)
            
            if result.get('success'):
                summary = result.get('summary', {})
                results_data = result.get('results', {})
                
                self.logger.info("🎯 CORRECT QUANTITY FIELD RESULTS:")
                self.logger.info(f"   Quantity field found: {summary.get('quantity_field_found', False)}")
                self.logger.info(f"   Price field found: {summary.get('price_field_found', False)}")
                self.logger.info(f"   Dropdown found: {summary.get('dropdown_found', False)}")
                self.logger.info(f"   Fill success: {summary.get('fill_success', False)}")
                self.logger.info(f"   Final value: '{summary.get('final_value', '')}'")
                
                # Show filled field details
                filled_field = results_data.get('filled_field', {})
                if filled_field:
                    self.logger.info(f"   Filled field position: {filled_field.get('position', {})}")
                    self.logger.info(f"   Filled field placeholder: '{filled_field.get('placeholder', '')}'")
                
                # Show all analyzed fields
                self.logger.info("📋 ALL ANALYZED FIELDS:")
                for field in results_data.get('fields_analyzed', []):
                    field_type = "QUANTITY" if "quantity" in field.get('context', '').lower() else "PRICE" if "price" in field.get('context', '').lower() else "OTHER"
                    dropdown_indicator = " 🔽" if field.get('hasDropdown') else ""
                    self.logger.info(f"   Field {field.get('index', 0)}: {field_type}{dropdown_indicator} at {field.get('position', {})} - '{field.get('context', '')[:50]}'")
                
                return summary.get('fill_success', False)
            else:
                self.logger.error("❌ Correct quantity field search failed")
                error = result.get('error', 'Unknown error')
                self.logger.error(f"   Error: {error}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Correct quantity field error: {e}")
            return False
    
    def click_buy_button_and_verify(self):
        """Click buy button and verify no quantity error"""
        self.logger.info("🔘 CLICKING BUY BUTTON AND VERIFYING...")
        
        # Determine button class
        if self.side == "BUY":
            button_class = "component_longBtn__eazYU"
        else:
            button_class = "component_shortBtn__x5P3I"
        
        click_verify_script = f"""
        () => {{
            console.log('🔘 Clicking button and verifying no quantity error...');
            
            const results = {{
                quantity_verified: false,
                button_found: false,
                button_clicked: false,
                dom_changes: 0,
                error_messages: [],
                success_indicators: []
            }};
            
            // STEP 1: Verify quantity is in field
            console.log('Step 1: Verifying quantity is in field...');
            
            const allInputs = document.querySelectorAll('input.ant-input');
            for (const input of allInputs) {{
                if (input.value === '{self.quantity}') {{
                    const rect = input.getBoundingClientRect();
                    if (rect.width > 0 && rect.height > 0) {{
                        console.log(`✅ Quantity verified: "${{input.value}}" at (${{rect.x}}, ${{rect.y}})`);
                        results.quantity_verified = true;
                        break;
                    }}
                }}
            }}
            
            if (!results.quantity_verified) {{
                console.log('❌ Quantity not found in any field');
                return {{ success: false, error: 'Quantity not verified', results: results }};
            }}
            
            // STEP 2: Find and click button
            console.log('Step 2: Finding and clicking button...');
            
            const button = document.querySelector('button.{button_class}');
            if (!button) {{
                console.log('❌ Button not found');
                return {{ success: false, error: 'Button not found', results: results }};
            }}
            
            results.button_found = true;
            console.log(`Found button: "${{button.textContent}}" at (${{button.getBoundingClientRect().x}}, ${{button.getBoundingClientRect().y}})`);
            
            // Record DOM state before click
            const beforeModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
            const beforeNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
            
            try {{
                // Click button
                button.focus();
                button.click();
                button.dispatchEvent(new Event('click', {{ bubbles: true }}));
                
                results.button_clicked = true;
                console.log('✅ Button clicked');
                
                // Wait for response
                setTimeout(() => {{
                    console.log('Step 3: Checking for responses...');
                    
                    // Check DOM changes
                    const afterModals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]').length;
                    const afterNotifications = document.querySelectorAll('.ant-notification, .ant-message').length;
                    
                    results.dom_changes = (afterModals - beforeModals) + (afterNotifications - beforeNotifications);
                    console.log(`DOM changes: ${{results.dom_changes}}`);
                    
                    // Check for error messages (especially quantity-related)
                    const errorSelectors = [
                        '.ant-message-error',
                        '.ant-notification-error',
                        '.error',
                        '[class*="error"]'
                    ];
                    
                    errorSelectors.forEach(selector => {{
                        const errorElements = document.querySelectorAll(selector);
                        errorElements.forEach(errorEl => {{
                            const errorText = errorEl.textContent || '';
                            if (errorText.trim()) {{
                                results.error_messages.push(errorText);
                                console.log(`❌ Error message: ${{errorText}}`);
                            }}
                        }});
                    }});
                    
                    // Check for success indicators
                    const successSelectors = [
                        '.ant-message-success',
                        '.ant-notification-success',
                        '.success',
                        '[class*="success"]'
                    ];
                    
                    successSelectors.forEach(selector => {{
                        const successElements = document.querySelectorAll(selector);
                        successElements.forEach(successEl => {{
                            const successText = successEl.textContent || '';
                            if (successText.trim()) {{
                                results.success_indicators.push(successText);
                                console.log(`✅ Success message: ${{successText}}`);
                            }}
                        }});
                    }});
                    
                    // Check for confirmation modals
                    const modals = document.querySelectorAll('.ant-modal, .modal, [role="dialog"]');
                    modals.forEach(modal => {{
                        const modalText = modal.textContent || '';
                        if (modalText.includes('confirm') || modalText.includes('order') || modalText.includes('trade')) {{
                            results.success_indicators.push('Confirmation modal appeared');
                            console.log(`✅ Confirmation modal detected`);
                        }}
                    }});
                    
                }}, 3000);
                
            }} catch (error) {{
                console.log(`❌ Button click error: ${{error.message}}`);
                results.error_messages.push(error.message);
            }}
            
            return {{
                success: true,
                results: results
            }};
        }}
        """
        
        try:
            result = self.page.evaluate(click_verify_script)
            
            # Wait for results
            time.sleep(4)
            
            if result.get('success'):
                results_data = result.get('results', {})
                
                self.logger.info("🔘 BUTTON CLICK AND VERIFICATION RESULTS:")
                self.logger.info(f"   Quantity verified: {results_data.get('quantity_verified', False)}")
                self.logger.info(f"   Button found: {results_data.get('button_found', False)}")
                self.logger.info(f"   Button clicked: {results_data.get('button_clicked', False)}")
                self.logger.info(f"   DOM changes: {results_data.get('dom_changes', 0)}")
                
                error_messages = results_data.get('error_messages', [])
                success_indicators = results_data.get('success_indicators', [])
                
                if error_messages:
                    self.logger.error("   Error messages:")
                    for error in error_messages:
                        self.logger.error(f"     ❌ {error}")
                
                if success_indicators:
                    self.logger.info("   Success indicators:")
                    for success in success_indicators:
                        self.logger.info(f"     ✅ {success}")
                
                # Success criteria: quantity verified, button clicked, and either DOM changes OR success indicators, and no quantity-related errors
                quantity_errors = [err for err in error_messages if 'quantity' in err.lower() or 'amount' in err.lower() or 'enter' in err.lower()]
                
                success = (results_data.get('quantity_verified', False) and 
                          results_data.get('button_clicked', False) and
                          (results_data.get('dom_changes', 0) > 0 or len(success_indicators) > 0) and
                          len(quantity_errors) == 0)
                
                if success:
                    self.logger.info("🎉 SUCCESS: Quantity field correctly filled and button responded!")
                else:
                    if quantity_errors:
                        self.logger.error(f"❌ FAILURE: Quantity-related errors: {quantity_errors}")
                    else:
                        self.logger.error("❌ FAILURE: No response to button click")
                
                return success
            else:
                self.logger.error("❌ Button click and verification failed")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Button click error: {e}")
            return False
    
    def execute_correct_quantity_automation(self):
        """Execute correct quantity field automation"""
        self.logger.info("🎯 EXECUTING CORRECT QUANTITY FIELD AUTOMATION...")
        
        try:
            # Connect
            if not self.connect():
                return False
            
            # Find and fill correct quantity field
            if not self.find_and_fill_correct_quantity_field():
                return False
            
            # Click button and verify
            if not self.click_buy_button_and_verify():
                return False
            
            self.logger.info("🎉 CORRECT QUANTITY FIELD AUTOMATION SUCCESSFUL!")
            return True
            
        except Exception as e:
            self.logger.error(f"Correct quantity automation error: {e}")
            return False
    
    def cleanup(self):
        try:
            if self.playwright:
                self.playwright.stop()
        except:
            pass

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="MEXC Correct Quantity Field")
    parser.add_argument("--symbol", default="TRU_USDT", help="Trading symbol")
    parser.add_argument("--side", choices=["BUY", "SELL"], default="BUY", help="Order side")
    parser.add_argument("--quantity", type=float, default=2.5, help="Order quantity")
    
    args = parser.parse_args()
    
    print(f"""
🎯 MEXC CORRECT QUANTITY FIELD
==============================
ISSUE: Script was filling Price (USDT) field instead of Quantity (USDT) field
SOLUTION: Target the correct Quantity field with dropdown arrow

IDENTIFICATION METHODS:
1. Look for "Quantity (USDT)" in field context
2. Look for empty field with dropdown arrow
3. Look for field that's NOT the price field

TARGET: {args.side} {args.quantity} {args.symbol}
    """)
    
    automation = MEXCCorrectQuantityField(args.symbol, args.side, args.quantity)
    
    try:
        success = automation.execute_correct_quantity_automation()
        
        if success:
            print("\n🎉 CORRECT QUANTITY FIELD AUTOMATION SUCCESSFUL!")
            print("Quantity field correctly identified and filled.")
        else:
            print("\n❌ CORRECT QUANTITY FIELD AUTOMATION FAILED!")
            print("Could not correctly identify or fill quantity field.")
            
    except KeyboardInterrupt:
        print("\n👋 Automation interrupted")
    except Exception as e:
        print(f"\n❌ Error: {e}")
    finally:
        automation.cleanup()

if __name__ == "__main__":
    main()
