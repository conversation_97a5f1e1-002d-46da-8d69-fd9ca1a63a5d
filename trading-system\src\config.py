"""
Configuration management for MEXC Trading System
Handles environment variables and application settings
"""

import os
from typing import List, Optional
from pathlib import Path

from pydantic import validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables"""
    
    # MEXC Configuration
    MEXC_BASE_URL: str = "https://futures.mexc.com"
    MEXC_SESSION_TIMEOUT: int = 432000  # 5 days
    MEXC_LOGIN_URL: str = "https://www.mexc.com/login"
    
    # MEXC API Configuration
    MEXC_API_KEY: Optional[str] = None
    MEXC_API_SECRET: Optional[str] = None
    MEXC_API_ENABLED: bool = False

    # Telegram Configuration
    TELEGRAM_BOT_TOKEN: Optional[str] = None
    TELEGRAM_CHAT_ID: Optional[str] = None
    TELEGRAM_ENABLED: bool = False
    
    # Trading Configuration
    MAX_CONCURRENT_TRADES: int = 5
    DEFAULT_LEVERAGE: int = 1
    RISK_MANAGEMENT_ENABLED: bool = True
    MAX_POSITION_SIZE: float = 1000.0
    EMERGENCY_STOP_ENABLED: bool = True

    # Bot Control
    BOT_ENABLED: bool = True

    # Money Management Settings
    TRADING_SYMBOL: str = "TRU_USDT"
    LEVERAGE: int = 1
    POSITION_SIZE_TYPE: str = "percentage"  # "percentage" or "fixed"
    POSITION_SIZE_PERCENTAGE: float = 50.0  # Percentage of available balance
    POSITION_SIZE_FIXED: float = 100.0  # Fixed amount in USDT
    MAX_POSITION_AMOUNT: float = 100.0  # Maximum amount per trade in USDT
    USE_MAX_POSITION_LIMIT: bool = True
    
    # Session Management
    SESSION_POOL_SIZE: int = 3
    SESSION_HEALTH_CHECK_INTERVAL: int = 1800  # 30 minutes
    SESSION_EXPIRY_WARNING_HOURS: int = 24
    SESSION_ROTATION_ENABLED: bool = True
    AUTO_SESSION_REFRESH: bool = True
    
    # Browser Configuration
    BROWSER_POOL_SIZE: int = 3
    HEADLESS_MODE: bool = False  # Use visible browser for better compatibility
    BROWSER_TIMEOUT: int = 30000
    ENABLE_NETWORK_INTERCEPTION: bool = True
    BROWSER_USER_AGENT: str = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

    # Browser Connection Configuration
    BROWSER_DEBUG_PORT: int = 9222
    USE_PERSISTENT_BROWSER: bool = True
    BROWSER_USER_DATA_DIR: str = "./browser_data"
    
    # Performance Settings
    ENABLE_SELECTOR_CACHING: bool = True
    ENABLE_CONNECTION_POOLING: bool = True
    MAX_RETRY_ATTEMPTS: int = 3
    RETRY_DELAY_SECONDS: int = 1
    PARALLEL_PROCESSING_ENABLED: bool = True
    
    # Security Settings
    ENCRYPTION_KEY: Optional[str] = None
    SESSION_ENCRYPTION_ENABLED: bool = False
    SECURE_COOKIES: bool = True
    
    # Database Configuration
    DATABASE_URL: str = "sqlite:///./data/trading_system.db"
    DATABASE_POOL_SIZE: int = 10
    DATABASE_MAX_OVERFLOW: int = 20
    
    # Logging Configuration
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/trading_system.log"
    LOG_MAX_SIZE: int = 10485760  # 10MB
    LOG_BACKUP_COUNT: int = 5
    STRUCTURED_LOGGING: bool = True
    
    # API Configuration
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    API_WORKERS: int = 1
    API_TIMEOUT: int = 30
    CORS_ENABLED: bool = True
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:8000"]
    
    # Webhook Configuration
    WEBHOOK_SECRET: Optional[str] = None
    WEBHOOK_TIMEOUT: int = 10
    WEBHOOK_MAX_RETRIES: int = 3
    
    # Monitoring & Health Checks
    HEALTH_CHECK_ENABLED: bool = True
    HEALTH_CHECK_INTERVAL: int = 300  # 5 minutes
    METRICS_ENABLED: bool = True
    PROMETHEUS_PORT: int = 9090
    
    # Development Settings
    DEBUG_MODE: bool = False
    RELOAD_ON_CHANGE: bool = False
    ENABLE_PROFILING: bool = False
    
    # Proxy Configuration
    PROXY_ENABLED: bool = False
    PROXY_URL: Optional[str] = None
    PROXY_USERNAME: Optional[str] = None
    PROXY_PASSWORD: Optional[str] = None
    
    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = True
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60
    
    # Backup & Recovery
    AUTO_BACKUP_ENABLED: bool = True
    BACKUP_INTERVAL_HOURS: int = 24
    BACKUP_RETENTION_DAYS: int = 30
    BACKUP_LOCATION: str = "./backups"
    
    # Trading Pairs
    SUPPORTED_SYMBOLS: List[str] = ["TRU_USDT", "BTC_USDT", "ETH_USDT", "BNB_USDT", "ADA_USDT", "SOL_USDT"]
    DEFAULT_SYMBOL: str = "BTC_USDT"
    
    # Risk Management
    STOP_LOSS_ENABLED: bool = True
    TAKE_PROFIT_ENABLED: bool = True
    TRAILING_STOP_ENABLED: bool = False
    MAX_DRAWDOWN_PERCENT: float = 10.0
    DAILY_LOSS_LIMIT: float = 500.0
    
    # Notification Settings
    EMAIL_NOTIFICATIONS_ENABLED: bool = False
    EMAIL_SMTP_SERVER: str = "smtp.gmail.com"
    EMAIL_SMTP_PORT: int = 587
    EMAIL_USERNAME: Optional[str] = None
    EMAIL_PASSWORD: Optional[str] = None
    EMAIL_RECIPIENTS: List[str] = []
    
    # Advanced Settings
    CUSTOM_HEADERS_ENABLED: bool = True
    USER_DATA_DIR: str = "./browser_data"
    SCREENSHOT_ON_ERROR: bool = True
    SAVE_NETWORK_LOGS: bool = True
    ENABLE_STEALTH_MODE: bool = True
    
    # Environment
    ENVIRONMENT: str = "development"
    VERSION: str = "1.0.0"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
    
    @validator("TELEGRAM_ENABLED")
    def validate_telegram_config(cls, v, values):
        """Validate Telegram configuration"""
        if v and not values.get("TELEGRAM_BOT_TOKEN"):
            raise ValueError("TELEGRAM_BOT_TOKEN is required when TELEGRAM_ENABLED is True")
        if v and not values.get("TELEGRAM_CHAT_ID"):
            raise ValueError("TELEGRAM_CHAT_ID is required when TELEGRAM_ENABLED is True")
        return v
    
    @validator("ENCRYPTION_KEY")
    def validate_encryption_key(cls, v, values):
        """Validate encryption key format"""
        if values.get("SESSION_ENCRYPTION_ENABLED", False) and not v:
            raise ValueError("ENCRYPTION_KEY is required when SESSION_ENCRYPTION_ENABLED is True")
        return v
    
    @validator("CORS_ORIGINS", pre=True)
    def parse_cors_origins(cls, v):
        """Parse CORS origins from string or list"""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v
    
    @validator("SUPPORTED_SYMBOLS", pre=True)
    def parse_supported_symbols(cls, v):
        """Parse supported symbols from string or list"""
        if isinstance(v, str):
            return [symbol.strip() for symbol in v.split(",")]
        return v
    
    @validator("EMAIL_RECIPIENTS", pre=True)
    def parse_email_recipients(cls, v):
        """Parse email recipients from string or list"""
        if isinstance(v, str):
            return [email.strip() for email in v.split(",")]
        return v
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.ENVIRONMENT.lower() == "production"
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.ENVIRONMENT.lower() == "development"
    
    @property
    def browser_args(self) -> List[str]:
        """Get browser launch arguments for standard browser instance"""
        if self.USE_PERSISTENT_BROWSER:
            # Standard browser arguments for persistent connection
            args = [
                f"--remote-debugging-port={self.BROWSER_DEBUG_PORT}",
                f"--user-data-dir={self.BROWSER_USER_DATA_DIR}",
                "--no-first-run",
                "--no-default-browser-check",
                "--disable-default-apps",
                "--disable-popup-blocking",
                "--disable-translate",
                "--disable-background-timer-throttling",
                "--disable-backgrounding-occluded-windows",
                "--disable-renderer-backgrounding",
                "--disable-field-trial-config",
                "--disable-back-forward-cache",
                "--disable-ipc-flooding-protection",
                "--enable-features=NetworkService,NetworkServiceLogging",
                "--disable-features=TranslateUI",
                "--disable-component-extensions-with-background-pages",
                "--disable-background-networking",
                "--disable-sync",
                "--metrics-recording-only",
                "--disable-default-apps",
                "--mute-audio",
                "--no-pings",
                "--no-zygote",
                "--disable-reading-from-canvas",
                "--disable-accelerated-2d-canvas",
                "--disable-accelerated-jpeg-decoding",
                "--disable-accelerated-mjpeg-decode",
                "--disable-accelerated-video-decode",
                "--disable-gpu-sandbox",
                "--disable-software-rasterizer",
                "--disable-dev-shm-usage"
            ]
        else:
            # Minimal arguments for direct browser launch
            args = [
                "--no-sandbox",
                "--disable-setuid-sandbox",
                "--disable-dev-shm-usage",
                "--disable-gpu"
            ]

        if self.PROXY_ENABLED and self.PROXY_URL:
            args.append(f"--proxy-server={self.PROXY_URL}")

        return args
    
    def get_database_url(self) -> str:
        """Get database URL with proper path resolution"""
        if self.DATABASE_URL.startswith("sqlite:///"):
            # Ensure data directory exists
            db_path = Path(self.DATABASE_URL.replace("sqlite:///", ""))
            db_path.parent.mkdir(parents=True, exist_ok=True)
        return self.DATABASE_URL


# Create global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings instance"""
    return settings
