#!/usr/bin/env python3
"""
ENTROPY SIGNATURE CRACKER
Hook into ALL browser entropy sources to crack the signature algorithm
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class EntropySignatureCracker:
    """Hook into all browser entropy sources to crack signatures"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🎲 ENTROPY SIGNATURE CRACKER")
        print("="*35)
        print("🔥 HOOKING ALL BROWSER ENTROPY SOURCES")
    
    def setup_entropy_hooks(self):
        """Setup hooks for ALL possible entropy sources"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject COMPREHENSIVE entropy hooks
            self.page.evaluate("""
                window.entropyCaptures = [];
                window.signatureCaptures = [];
                window.allRandomValues = [];
                
                console.log('🎲 Installing COMPREHENSIVE entropy hooks...');
                
                // Hook crypto.getRandomValues (main source of browser entropy)
                if (window.crypto && window.crypto.getRandomValues) {
                    const originalGetRandomValues = window.crypto.getRandomValues;
                    window.crypto.getRandomValues = function(array) {
                        const result = originalGetRandomValues.apply(this, arguments);
                        
                        // Capture the random values
                        const randomData = Array.from(array);
                        const randomHex = randomData.map(b => b.toString(16).padStart(2, '0')).join('');
                        
                        console.log('🎲 crypto.getRandomValues:', randomHex);
                        
                        window.allRandomValues.push({
                            type: 'crypto_random',
                            data: randomData,
                            hex: randomHex,
                            length: array.length,
                            timestamp: Date.now(),
                            stack: new Error().stack
                        });
                        
                        return result;
                    };
                }
                
                // Hook Math.random
                const originalMathRandom = Math.random;
                Math.random = function() {
                    const result = originalMathRandom.apply(this, arguments);
                    
                    console.log('🎲 Math.random:', result);
                    
                    window.allRandomValues.push({
                        type: 'math_random',
                        value: result,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    });
                    
                    return result;
                };
                
                // Hook Date.now and performance.now for timing entropy
                const originalDateNow = Date.now;
                Date.now = function() {
                    const result = originalDateNow.apply(this, arguments);
                    
                    window.allRandomValues.push({
                        type: 'date_now',
                        value: result,
                        timestamp: result
                    });
                    
                    return result;
                };
                
                if (window.performance && window.performance.now) {
                    const originalPerformanceNow = window.performance.now;
                    window.performance.now = function() {
                        const result = originalPerformanceNow.apply(this, arguments);
                        
                        window.allRandomValues.push({
                            type: 'performance_now',
                            value: result,
                            timestamp: Date.now()
                        });
                        
                        return result;
                    };
                }
                
                // Hook any UUID generation
                if (window.crypto && window.crypto.randomUUID) {
                    const originalRandomUUID = window.crypto.randomUUID;
                    window.crypto.randomUUID = function() {
                        const result = originalRandomUUID.apply(this, arguments);
                        
                        console.log('🎲 crypto.randomUUID:', result);
                        
                        window.allRandomValues.push({
                            type: 'random_uuid',
                            value: result,
                            timestamp: Date.now(),
                            stack: new Error().stack
                        });
                        
                        return result;
                    };
                }
                
                // Hook signature header setting with entropy correlation
                const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
                XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                    if (name.toLowerCase() === 'x-mxc-sign') {
                        console.log('🎉🎉🎉 SIGNATURE WITH ENTROPY CONTEXT! 🎉🎉🎉');
                        console.log('Signature:', value);
                        
                        // Capture recent entropy values (last 10 seconds)
                        const recentEntropy = window.allRandomValues.filter(
                            entry => Date.now() - entry.timestamp < 10000
                        );
                        
                        const signatureCapture = {
                            type: 'signature_with_entropy',
                            signature: value,
                            timestamp: Date.now(),
                            recentEntropy: recentEntropy,
                            entropyCount: recentEntropy.length,
                            stack: new Error().stack
                        };
                        
                        window.signatureCaptures.push(signatureCapture);
                        
                        console.log('🎲 Recent entropy values:', recentEntropy.length);
                        
                        // Try to correlate signature with entropy
                        for (const entropy of recentEntropy) {
                            if (entropy.type === 'crypto_random' && entropy.hex) {
                                // Test if signature contains parts of this entropy
                                if (value.includes(entropy.hex.substring(0, 8))) {
                                    console.log('🎉 ENTROPY CORRELATION FOUND!');
                                    console.log('Signature contains entropy:', entropy.hex.substring(0, 8));
                                    
                                    alert(`ENTROPY CORRELATION FOUND!\\n\\nSignature: ${value}\\nContains: ${entropy.hex.substring(0, 8)}`);
                                }
                                
                                // Test if signature is hash of entropy + data
                                const testContent = entropy.hex + '1754929178532';
                                const testSig = CryptoJS.MD5(testContent).toString();
                                if (testSig === value) {
                                    console.log('🎉 ENTROPY HASH MATCH!');
                                    alert(`ENTROPY HASH MATCH!\\n\\nMD5(${entropy.hex} + nonce) = ${value}`);
                                }
                            }
                        }
                        
                        alert(`SIGNATURE CAPTURED WITH ENTROPY!\\n\\nSignature: ${value}\\nRecent entropy: ${recentEntropy.length} values`);
                    }
                    
                    return originalSetRequestHeader.apply(this, arguments);
                };
                
                // Hook any potential random string generation functions
                const originalToString = Object.prototype.toString;
                Object.prototype.toString = function() {
                    const result = originalToString.apply(this, arguments);
                    
                    // Check if this looks like a random hex string
                    if (typeof result === 'string' && result.length === 32 && /^[a-f0-9]+$/i.test(result)) {
                        console.log('🎲 Potential random hex string:', result);
                        
                        window.allRandomValues.push({
                            type: 'potential_hex',
                            value: result,
                            timestamp: Date.now(),
                            stack: new Error().stack
                        });
                    }
                    
                    return result;
                };
                
                // Hook any base64 operations (might be used for entropy)
                const originalBtoa = window.btoa;
                if (originalBtoa) {
                    window.btoa = function(data) {
                        const result = originalBtoa.apply(this, arguments);
                        
                        if (data.length > 10) {  // Potential crypto data
                            console.log('🎲 btoa called with:', data.substring(0, 20));
                            
                            window.allRandomValues.push({
                                type: 'btoa',
                                input: data.substring(0, 50),
                                output: result.substring(0, 50),
                                timestamp: Date.now()
                            });
                        }
                        
                        return result;
                    };
                }
                
                const originalAtob = window.atob;
                if (originalAtob) {
                    window.atob = function(data) {
                        const result = originalAtob.apply(this, arguments);
                        
                        console.log('🎲 atob called with:', data.substring(0, 20));
                        
                        window.allRandomValues.push({
                            type: 'atob',
                            input: data.substring(0, 50),
                            output: result.substring(0, 50),
                            timestamp: Date.now()
                        });
                        
                        return result;
                    };
                }
                
                console.log('✅ COMPREHENSIVE entropy hooks installed!');
                console.log('🎲 Monitoring ALL browser entropy sources...');
            """)
            
            print("✅ Entropy hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def wait_for_entropy_signature(self):
        """Wait for signature with entropy correlation"""
        
        print("\n🎲 WAITING FOR SIGNATURE WITH ENTROPY")
        print("="*45)
        print()
        print("🎯 PLACE AN ORDER NOW!")
        print("   - All entropy sources are being monitored")
        print("   - We'll correlate signature with random values")
        print("   - Use very low price to avoid fills")
        print()
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # Check for signature captures with entropy
                captures = self.page.evaluate("() => window.signatureCaptures || []")
                entropy_values = self.page.evaluate("() => window.allRandomValues || []")
                
                if captures:
                    print(f"\n🎉 SIGNATURE WITH ENTROPY CAPTURED!")
                    
                    for i, capture in enumerate(captures):
                        print(f"\n📋 CAPTURE #{i+1}:")
                        print(f"   Signature: {capture['signature']}")
                        print(f"   Recent entropy: {capture['entropyCount']} values")
                        
                        # Analyze the entropy correlation
                        if self.analyze_entropy_correlation(capture):
                            return True
                
                # Show entropy activity
                if len(entropy_values) > 0:
                    entropy_count = len(entropy_values)
                    if entropy_count % 10 == 0:  # Show every 10 entropy values
                        print(f"🎲 Entropy activity: {entropy_count} values captured")
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:
                    print(f"⏱️  Waiting... ({elapsed}s, {len(entropy_values)} entropy values)")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(1)
        
        print(f"\n⏰ Timeout reached")
        return False
    
    def analyze_entropy_correlation(self, capture):
        """Analyze correlation between signature and entropy"""
        
        print(f"\n🔍 ANALYZING ENTROPY CORRELATION")
        print("="*40)
        
        signature = capture['signature']
        recent_entropy = capture.get('recentEntropy', [])
        
        print(f"🔐 Signature: {signature}")
        print(f"🎲 Recent entropy values: {len(recent_entropy)}")
        
        # Analyze each entropy value
        for i, entropy in enumerate(recent_entropy):
            print(f"\n🎲 Entropy #{i+1}: {entropy['type']}")
            
            if entropy['type'] == 'crypto_random':
                entropy_hex = entropy.get('hex', '')
                print(f"   Hex: {entropy_hex}")
                
                # Test various combinations
                test_combinations = [
                    entropy_hex,
                    entropy_hex + self.auth,
                    self.auth + entropy_hex,
                    entropy_hex + "1754929178532",
                    "1754929178532" + entropy_hex,
                    self.auth + entropy_hex + "1754929178532",
                ]
                
                for combo in test_combinations:
                    test_sig = hashlib.md5(combo.encode()).hexdigest()
                    if test_sig == signature:
                        print(f"🎉🎉🎉 SIGNATURE ALGORITHM CRACKED! 🎉🎉🎉")
                        print(f"Algorithm: MD5({combo})")
                        print(f"Entropy: {entropy_hex}")
                        return True
            
            elif entropy['type'] == 'math_random':
                random_val = str(entropy.get('value', ''))
                print(f"   Value: {random_val}")
                
                # Test with Math.random value
                test_combinations = [
                    self.auth + random_val,
                    random_val + self.auth,
                    random_val + "1754929178532",
                ]
                
                for combo in test_combinations:
                    test_sig = hashlib.md5(combo.encode()).hexdigest()
                    if test_sig == signature:
                        print(f"🎉🎉🎉 MATH.RANDOM SIGNATURE CRACKED! 🎉🎉🎉")
                        print(f"Algorithm: MD5({combo})")
                        return True
        
        print(f"❌ No direct entropy correlation found")
        return False
    
    def run_entropy_cracking(self):
        """Run the entropy-based signature cracking"""
        
        print("="*60)
        print("🎲 ENTROPY-BASED SIGNATURE CRACKING")
        print("="*60)
        
        # Setup hooks
        if not self.setup_entropy_hooks():
            return False
        
        try:
            # Wait for signature with entropy
            if self.wait_for_entropy_signature():
                print("\n🎉 SIGNATURE ALGORITHM CRACKED WITH ENTROPY!")
                return True
            else:
                print("\n🔍 No entropy correlation found")
                return False
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    cracker = EntropySignatureCracker()
    cracker.run_entropy_cracking()

if __name__ == '__main__':
    main()
