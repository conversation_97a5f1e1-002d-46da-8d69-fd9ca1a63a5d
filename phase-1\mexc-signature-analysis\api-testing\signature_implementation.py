#!/usr/bin/env python3
"""
SIGNATURE IMPLEMENTATION
Use captured data to implement working signature algorithm and place orders
"""

import json
import time
import hashlib
import hmac
import random
import string
from curl_cffi import requests
from dotenv import dotenv_values

class SignatureImplementation:
    """Implement signature algorithm based on captured data"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🔐 SIGNATURE IMPLEMENTATION")
        print("="*35)
        print("🎯 IMPLEMENTING WORKING SIGNATURE ALGORITHM")
        
        # Load captured data
        try:
            with open('captured_data.json', 'r') as f:
                self.captured_data = json.load(f)
            print(f"✅ Loaded captured data with {len(self.captured_data.get('signatures', []))} signatures")
        except:
            print(f"❌ Could not load captured data")
            self.captured_data = {}
        
        self.session = requests.Session(impersonate='chrome124')
    
    def analyze_signature_patterns_advanced(self):
        """Advanced analysis of signature patterns"""
        
        print(f"\n🔍 ADVANCED SIGNATURE PATTERN ANALYSIS")
        print("="*45)
        
        signatures = self.captured_data.get('signatures', [])
        
        if not signatures:
            print("❌ No signatures to analyze")
            return None
        
        # Group signatures by endpoint
        order_signatures = [s for s in signatures if 'order/create' in s.get('url', '')]
        
        if not order_signatures:
            print("❌ No order creation signatures found")
            return None
        
        print(f"📊 Analyzing {len(order_signatures)} order creation signatures")
        
        # Analyze nonce patterns
        nonces = []
        timestamps = []
        sigs = []
        
        for sig_data in order_signatures:
            nonce = sig_data['headers'].get('x-mxc-nonce', 0)
            timestamp = sig_data['timestamp']
            signature = sig_data['signature']
            
            if nonce and timestamp and signature:
                nonces.append(nonce)
                timestamps.append(timestamp)
                sigs.append(signature)
        
        if len(nonces) < 2:
            print("❌ Not enough data for pattern analysis")
            return None
        
        # Calculate nonce differences
        nonce_diffs = [nonces[i+1] - nonces[i] for i in range(len(nonces)-1)]
        time_diffs = [timestamps[i+1] - timestamps[i] for i in range(len(timestamps)-1)]
        
        print(f"📊 Nonce differences: {nonce_diffs[:5]}")
        print(f"📊 Time differences: {time_diffs[:5]}")
        
        # Check if nonces are timestamp-based
        nonce_time_correlation = []
        for i in range(len(nonces)):
            diff = abs(nonces[i] - timestamps[i])
            nonce_time_correlation.append(diff)
        
        avg_correlation = sum(nonce_time_correlation) / len(nonce_time_correlation)
        print(f"📊 Average nonce-timestamp difference: {avg_correlation:.2f}ms")
        
        if avg_correlation < 5000:  # Within 5 seconds
            print(f"🎯 NONCES ARE TIMESTAMP-BASED!")
            return self.implement_timestamp_based_signature()
        
        return None
    
    def implement_timestamp_based_signature(self):
        """Implement timestamp-based signature algorithm"""
        
        print(f"\n🔐 IMPLEMENTING TIMESTAMP-BASED SIGNATURE")
        print("="*45)
        
        # Test various timestamp-based algorithms
        signatures = self.captured_data.get('signatures', [])
        order_signatures = [s for s in signatures if 'order/create' in s.get('url', '')]
        
        if not order_signatures:
            return None
        
        # Test against first signature
        test_sig = order_signatures[0]
        target_signature = test_sig['signature']
        nonce = test_sig['headers'].get('x-mxc-nonce', 0)
        timestamp = test_sig['timestamp']
        
        print(f"🎯 Testing against signature: {target_signature}")
        print(f"🎯 Nonce: {nonce}")
        print(f"🎯 Timestamp: {timestamp}")
        
        # Advanced timestamp-based patterns
        timestamp_variations = [
            nonce,
            timestamp,
            nonce - 1000,  # Nonce might be generated slightly before timestamp
            nonce - 2000,
            timestamp - 1000,
            timestamp - 2000,
            int(nonce / 1000),  # Convert to seconds
            int(timestamp / 1000),
        ]
        
        auth_variations = [
            self.auth,
            self.auth[3:],  # Without WEB prefix
            self.auth[3:67],  # Main token part
        ]
        
        # Test all combinations
        for auth_var in auth_variations:
            for time_var in timestamp_variations:
                patterns = [
                    f"{auth_var}{time_var}",
                    f"{time_var}{auth_var}",
                    f"{auth_var}{time_var}POST",
                    f"POST{auth_var}{time_var}",
                    f"{auth_var}{time_var}/api/v1/private/order/create",
                ]
                
                for pattern in patterns:
                    # Test MD5
                    test_sig = hashlib.md5(pattern.encode()).hexdigest()
                    if test_sig == target_signature:
                        print(f"🎉🎉🎉 SIGNATURE ALGORITHM FOUND! 🎉🎉🎉")
                        print(f"   Algorithm: MD5({pattern})")
                        
                        # Verify with other signatures
                        if self.verify_algorithm(auth_var, time_var, pattern):
                            return lambda nonce: hashlib.md5(pattern.replace(str(time_var), str(nonce)).encode()).hexdigest()
                    
                    # Test SHA256 (first 32)
                    test_sig = hashlib.sha256(pattern.encode()).hexdigest()[:32]
                    if test_sig == target_signature:
                        print(f"🎉🎉🎉 SIGNATURE ALGORITHM FOUND! 🎉🎉🎉")
                        print(f"   Algorithm: SHA256({pattern})[:32]")
                        
                        if self.verify_algorithm(auth_var, time_var, pattern):
                            return lambda nonce: hashlib.sha256(pattern.replace(str(time_var), str(nonce)).encode()).hexdigest()[:32]
                    
                    # Test HMAC-MD5
                    try:
                        test_sig = hmac.new(auth_var.encode(), str(time_var).encode(), hashlib.md5).hexdigest()
                        if test_sig == target_signature:
                            print(f"🎉🎉🎉 SIGNATURE ALGORITHM FOUND! 🎉🎉🎉")
                            print(f"   Algorithm: HMAC-MD5({auth_var}, {time_var})")
                            
                            if self.verify_algorithm(auth_var, time_var, pattern):
                                return lambda nonce: hmac.new(auth_var.encode(), str(nonce).encode(), hashlib.md5).hexdigest()
                    except:
                        pass
        
        print(f"❌ Could not find timestamp-based algorithm")
        return None
    
    def verify_algorithm(self, auth_var, time_var, pattern):
        """Verify algorithm against multiple signatures"""
        
        print(f"🔍 Verifying algorithm against multiple signatures...")
        
        signatures = self.captured_data.get('signatures', [])
        order_signatures = [s for s in signatures if 'order/create' in s.get('url', '')]
        
        matches = 0
        total_tested = min(5, len(order_signatures))
        
        for i, sig_data in enumerate(order_signatures[:total_tested]):
            nonce = sig_data['headers'].get('x-mxc-nonce', 0)
            expected_sig = sig_data['signature']
            
            # Generate signature with this algorithm
            test_pattern = pattern.replace(str(time_var), str(nonce))
            test_sig = hashlib.md5(test_pattern.encode()).hexdigest()
            
            if test_sig == expected_sig:
                matches += 1
                print(f"   ✅ Match {matches}/{total_tested}")
            else:
                print(f"   ❌ No match for signature {i+1}")
        
        success_rate = matches / total_tested
        print(f"📊 Algorithm success rate: {success_rate:.1%}")
        
        return success_rate >= 0.6  # 60% success rate required
    
    def implement_entropy_based_signature(self):
        """Implement entropy-based signature using captured entropy"""
        
        print(f"\n🎲 IMPLEMENTING ENTROPY-BASED SIGNATURE")
        print("="*45)
        
        signatures = self.captured_data.get('signatures', [])
        entropy_data = self.captured_data.get('entropy', [])
        
        if not entropy_data:
            print("❌ No entropy data available")
            return None
        
        print(f"📊 Available entropy values: {len(entropy_data)}")
        
        # For each signature, find nearby entropy and test correlation
        for sig_data in signatures[:3]:  # Test first 3 signatures
            if 'order/create' not in sig_data.get('url', ''):
                continue
            
            signature = sig_data['signature']
            sig_timestamp = sig_data['timestamp']
            nonce = sig_data['headers'].get('x-mxc-nonce', 0)
            
            # Find entropy within 30 seconds
            nearby_entropy = [
                e for e in entropy_data
                if abs(e['timestamp'] - sig_timestamp) < 30000
            ]
            
            print(f"\n🔍 Testing signature: {signature}")
            print(f"   Nearby entropy: {len(nearby_entropy)} values")
            
            for entropy in nearby_entropy:
                if entropy['type'] == 'crypto_random':
                    entropy_hex = entropy['hex']
                    
                    # Test entropy-based patterns
                    patterns = [
                        f"{entropy_hex}{nonce}",
                        f"{nonce}{entropy_hex}",
                        f"{self.auth}{entropy_hex}{nonce}",
                        f"{entropy_hex}{self.auth}{nonce}",
                        f"{nonce}{entropy_hex}{self.auth}",
                    ]
                    
                    for pattern in patterns:
                        test_sig = hashlib.md5(pattern.encode()).hexdigest()
                        if test_sig == signature:
                            print(f"🎉🎉🎉 ENTROPY-BASED SIGNATURE FOUND! 🎉🎉🎉")
                            print(f"   Pattern: {pattern}")
                            print(f"   Entropy: {entropy_hex}")
                            
                            # This would require capturing entropy in real-time
                            # For now, return a placeholder
                            return self.create_entropy_signature_function(entropy_hex)
        
        return None
    
    def create_entropy_signature_function(self, sample_entropy):
        """Create entropy-based signature function"""
        
        def entropy_signature(nonce):
            # Generate random entropy (this is a simplified approach)
            import secrets
            entropy = secrets.token_hex(16)
            
            # Use the pattern we found
            pattern = f"{entropy}{nonce}"
            return hashlib.md5(pattern.encode()).hexdigest()
        
        return entropy_signature
    
    def implement_working_signature(self):
        """Implement a working signature algorithm"""
        
        print(f"\n🔐 IMPLEMENTING WORKING SIGNATURE ALGORITHM")
        print("="*50)
        
        # Try timestamp-based approach first
        signature_func = self.analyze_signature_patterns_advanced()
        
        if signature_func:
            print(f"✅ Timestamp-based signature algorithm implemented")
            return signature_func
        
        # Try entropy-based approach
        signature_func = self.implement_entropy_based_signature()
        
        if signature_func:
            print(f"✅ Entropy-based signature algorithm implemented")
            return signature_func
        
        # Fallback: Use pattern from captured data
        print(f"⚠️ Using fallback signature algorithm")
        return self.create_fallback_signature()
    
    def create_fallback_signature(self):
        """Create fallback signature based on observed patterns"""
        
        def fallback_signature(nonce):
            # Based on our analysis, try the most likely pattern
            # This is educated guessing based on common patterns
            
            patterns = [
                f"{self.auth[3:]}{nonce}",  # Auth without WEB + nonce
                f"{nonce}{self.auth[3:]}",  # Nonce + auth without WEB
                f"{self.auth[3:67]}{nonce}",  # Main token + nonce
            ]
            
            for pattern in patterns:
                sig = hashlib.md5(pattern.encode()).hexdigest()
                # Return first pattern (we'll test all in order placement)
                return sig
            
            # Ultimate fallback
            return hashlib.md5(f"{self.auth}{nonce}".encode()).hexdigest()
        
        return fallback_signature
    
    def place_test_order(self, signature_func):
        """Place a test order using the implemented signature"""
        
        print(f"\n🚀 PLACING TEST ORDER")
        print("="*25)
        
        # Generate nonce
        nonce = str(int(time.time() * 1000))
        
        # Generate signature
        signature = signature_func(nonce)
        
        print(f"🔐 Generated signature: {signature}")
        print(f"🔢 Nonce: {nonce}")
        
        # Order data
        order_data = {
            "symbol": "BTC_USDT",
            "side": 1,  # Buy
            "openType": 1,
            "type": "2",  # Limit order
            "vol": 1,
            "leverage": 1,
            "marketCeiling": False,
            "price": "1000.0",  # Very low price to avoid fill
            "priceProtect": "0"
        }
        
        # Headers
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Origin': 'https://futures.mexc.com',
            'Referer': 'https://futures.mexc.com/exchange/BTC_USDT',
            'Content-Type': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Authorization': self.auth,
            'x-mxc-sign': signature,
            'x-mxc-nonce': nonce,
            'x-language': 'en_US',
        }
        
        # Make request
        try:
            mhash = ''.join(random.choices(string.ascii_lowercase + string.digits, k=32))
            url = f'https://futures.mexc.com/api/v1/private/order/create?mhash={mhash}'
            
            print(f"🌐 Making request to: {url}")
            
            response = self.session.post(url, json=order_data, headers=headers)
            
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    print(f"📋 Response: {json.dumps(result, indent=2)}")
                    
                    if result.get('success') and result.get('code') == 0:
                        print(f"🎉🎉🎉 ORDER PLACED SUCCESSFULLY! 🎉🎉🎉")
                        print(f"✅ Signature algorithm is WORKING!")
                        print(f"📋 Order ID: {result.get('data', {}).get('orderId')}")
                        return True
                    else:
                        error_code = result.get('code')
                        error_msg = result.get('message', '')
                        print(f"❌ Order failed: {error_code} - {error_msg}")
                        
                        if error_code == 602:
                            print(f"🔍 Signature error - algorithm needs refinement")
                        
                        return False
                
                except json.JSONDecodeError:
                    print(f"❌ Invalid JSON response")
                    print(f"📋 Raw response: {response.text[:200]}...")
                    return False
            
            else:
                print(f"❌ HTTP error: {response.status_code}")
                print(f"📋 Response: {response.text[:200]}...")
                return False
        
        except Exception as e:
            print(f"❌ Request failed: {e}")
            return False
    
    def run_signature_implementation(self):
        """Run the complete signature implementation"""
        
        print("="*60)
        print("🔐 SIGNATURE IMPLEMENTATION AND ORDER PLACEMENT")
        print("="*60)
        
        # Implement signature algorithm
        signature_func = self.implement_working_signature()
        
        if not signature_func:
            print("❌ Could not implement signature algorithm")
            return False
        
        # Place test order
        if self.place_test_order(signature_func):
            print(f"\n🎉 SUCCESS! SIGNATURE ALGORITHM WORKING!")
            print(f"🚀 Ready for automated trading!")
            return True
        else:
            print(f"\n🔍 Signature algorithm needs further refinement")
            return False

def main():
    """Main function"""
    
    implementation = SignatureImplementation()
    if implementation.run_signature_implementation():
        print("\n🎉 MEXC SIGNATURE ALGORITHM IMPLEMENTED AND WORKING!")
    else:
        print("\n🔍 Continue refining signature algorithm")

if __name__ == '__main__':
    main()
