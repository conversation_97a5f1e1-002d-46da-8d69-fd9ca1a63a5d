version: '3.8'

services:
  mexc-trading-system:
    build: .
    container_name: mexc-trader
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=production
      - API_HOST=0.0.0.0
      - API_PORT=8000
      - HEADLESS_MODE=true
      - LOG_LEVEL=INFO
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./browser_data:/app/browser_data
      - ./backups:/app/backups
      - ./.env:/app/.env:ro
    networks:
      - mexc-network
    depends_on:
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  redis:
    image: redis:7-alpine
    container_name: mexc-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - mexc-network
    command: redis-server --appendonly yes

  nginx:
    image: nginx:alpine
    container_name: mexc-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - mexc-network
    depends_on:
      - mexc-trading-system

volumes:
  redis_data:

networks:
  mexc-network:
    driver: bridge
