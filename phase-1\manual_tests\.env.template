# MEXC Authentication Configuration
# Copy this file to .env and fill in your actual values

# MEXC Web Authentication Token
# Format: WEB + 64-character hexadecimal token
# Example: WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6
MEXC_WEB_AUTH=WEB[your-64-character-token-here]

# Browser Configuration
# Chrome remote debugging port (default: 9222)
CHROME_DEBUG_PORT=9222

# Research Configuration
# Enable debug logging (true/false)
DEBUG_MODE=true

# Data capture settings
CAPTURE_SIGNATURES=true
CAPTURE_ENTROPY=true
CAPTURE_TIMING=true

# Analysis settings
ANALYZE_PATTERNS=true
TEST_ALGORITHMS=true
CORRELATION_ANALYSIS=true

# Browser automation settings
HEADLESS_MODE=false
BROWSER_TIMEOUT=30000
PAGE_LOAD_TIMEOUT=10000

# API testing settings
API_TIMEOUT=30
MAX_RETRIES=3
RATE_LIMIT_DELAY=1

# File paths
CAPTURED_DATA_FILE=data/captured_data.json
ANALYSIS_OUTPUT_DIR=analysis_results/
LOG_FILE=mexc_analysis.log

# Security settings (DO NOT COMMIT REAL VALUES)
# These are for research purposes only
MEXC_SESSION_COOKIES=
MEXC_USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36

# Research notes
# Token extraction: Browser DevTools -> Application -> Cookies -> futures.mexc.com
# Session management: Tokens typically valid for 5-7 days
# Rate limiting: Respect MEXC's rate limits during research
# Legal compliance: Ensure research complies with terms of service
