#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Enhanced Server
======================

Simple server to demonstrate the enhanced features including:
- MEXC API integration
- Configuration management
- Money management settings
- Dashboard functionality
"""

from fastapi import FastAPI, HTTPException
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import Dict, Any, Optional
import uvicorn
import json
from datetime import datetime

# Simple configuration for demo
class SimpleConfig:
    BOT_ENABLED = True
    TRADING_SYMBOL = "TRU_USDT"
    LEVERAGE = 1
    POSITION_SIZE_TYPE = "percentage"
    POSITION_SIZE_PERCENTAGE = 50.0
    POSITION_SIZE_FIXED = 100.0
    MAX_POSITION_AMOUNT = 100.0
    USE_MAX_POSITION_LIMIT = True
    MEXC_API_KEY = None
    MEXC_API_SECRET = None
    MEXC_API_ENABLED = False

config = SimpleConfig()

# Create FastAPI app
app = FastAPI(
    title="Enhanced MEXC Trading System Demo",
    description="Demo of enhanced features with MEXC API integration",
    version="2.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class BotControlConfig(BaseModel):
    bot_enabled: bool

class MoneyManagementConfig(BaseModel):
    trading_symbol: str = "TRU_USDT"
    leverage: int = 1
    position_size_type: str = "percentage"
    position_size_percentage: float = 50.0
    position_size_fixed: float = 100.0
    max_position_amount: float = 100.0
    use_max_position_limit: bool = True

class MEXCAPIConfig(BaseModel):
    api_key: Optional[str] = None
    api_secret: Optional[str] = None
    api_enabled: bool = False


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Enhanced MEXC Trading System Demo",
        "version": "2.0.0",
        "features": [
            "MEXC API integration",
            "Advanced money management",
            "Real-time configuration",
            "Bot control toggle",
            "Account information display"
        ],
        "dashboard_url": "/enhanced-dashboard",
        "status": "ready"
    }


@app.get("/enhanced-dashboard", response_class=HTMLResponse)
async def enhanced_dashboard():
    """Enhanced dashboard"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced MEXC Trading System</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
            .container { max-width: 1200px; margin: 0 auto; }
            .card { background: white; padding: 20px; margin: 20px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-align: center; }
            .toggle { display: inline-block; width: 60px; height: 30px; background: #ccc; border-radius: 15px; position: relative; cursor: pointer; }
            .toggle.active { background: #4CAF50; }
            .toggle-slider { position: absolute; top: 3px; left: 3px; width: 24px; height: 24px; background: white; border-radius: 50%; transition: 0.3s; }
            .toggle.active .toggle-slider { transform: translateX(30px); }
            .config-item { display: flex; justify-content: space-between; margin: 10px 0; align-items: center; }
            .config-input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px; }
            .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
            .btn-primary { background: #007bff; color: white; }
            .btn-success { background: #28a745; color: white; }
            .status-online { background: #28a745; color: white; padding: 5px 10px; border-radius: 15px; }
            .status-offline { background: #dc3545; color: white; padding: 5px 10px; border-radius: 15px; }
            .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="card header">
                <h1>🚀 Enhanced MEXC Trading System</h1>
                <div>
                    <span id="systemStatus" class="status-online">System Online</span>
                    <span style="margin-left: 20px;">Bot Status:</span>
                    <span class="toggle active" id="botToggle" onclick="toggleBot()">
                        <span class="toggle-slider"></span>
                    </span>
                    <span id="botStatus">Enabled</span>
                </div>
            </div>
            
            <div class="grid">
                <div class="card">
                    <h3>💰 Money Management</h3>
                    <div class="config-item">
                        <label>Trading Symbol:</label>
                        <select class="config-input" id="tradingSymbol">
                            <option value="TRU_USDT">TRU_USDT</option>
                            <option value="ETH_USDT">ETH_USDT</option>
                            <option value="BNB_USDT">BNB_USDT</option>
                        </select>
                    </div>
                    <div class="config-item">
                        <label>Leverage:</label>
                        <input type="number" class="config-input" id="leverage" min="1" max="100" value="1">
                    </div>
                    <div class="config-item">
                        <label>Position Size (%):</label>
                        <input type="number" class="config-input" id="positionSizePercentage" min="0" max="100" value="50">
                    </div>
                    <div class="config-item">
                        <label>Max Amount (USDT):</label>
                        <input type="number" class="config-input" id="maxPositionAmount" min="0" step="0.01" value="100">
                    </div>
                    <button class="btn btn-primary" onclick="saveMoneyManagement()">Save Settings</button>
                </div>
                
                <div class="card">
                    <h3>🔑 MEXC API Configuration</h3>
                    <div class="config-item">
                        <label>API Key:</label>
                        <input type="text" class="config-input" id="mexcApiKey" placeholder="Enter API Key">
                    </div>
                    <div class="config-item">
                        <label>API Secret:</label>
                        <input type="password" class="config-input" id="mexcApiSecret" placeholder="Enter API Secret">
                    </div>
                    <div class="config-item">
                        <label>API Enabled:</label>
                        <input type="checkbox" id="mexcApiEnabled">
                    </div>
                    <button class="btn btn-primary" onclick="saveMexcApi()">Save API Config</button>
                    <button class="btn btn-success" onclick="testConnection()">Test Connection</button>
                </div>
                
                <div class="card">
                    <h3>📊 Account Information</h3>
                    <div id="accountInfo">
                        <p>Configure MEXC API credentials to view account information</p>
                    </div>
                    <button class="btn btn-success" onclick="loadAccountInfo()">Refresh Account Info</button>
                </div>
                
                <div class="card">
                    <h3>⚙️ System Status</h3>
                    <div id="systemInfo">
                        <div class="config-item">
                            <span>Enhanced Automation:</span>
                            <span class="status-online">Active</span>
                        </div>
                        <div class="config-item">
                            <span>Blur Prevention:</span>
                            <span class="status-online">Enabled</span>
                        </div>
                        <div class="config-item">
                            <span>UI Automation:</span>
                            <span class="status-online">100% Success Rate</span>
                        </div>
                        <div class="config-item">
                            <span>Browser Connection:</span>
                            <span class="status-online">Connected</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <script>
            // Load current configuration
            loadConfig();
            
            async function loadConfig() {
                try {
                    const response = await fetch('/config/current');
                    const data = await response.json();
                    
                    if (data.success) {
                        const config = data.config;
                        
                        // Update UI with current config
                        document.getElementById('tradingSymbol').value = config.money_management.trading_symbol;
                        document.getElementById('leverage').value = config.money_management.leverage;
                        document.getElementById('positionSizePercentage').value = config.money_management.position_size_percentage;
                        document.getElementById('maxPositionAmount').value = config.money_management.max_position_amount;
                        document.getElementById('mexcApiEnabled').checked = config.mexc_api.api_enabled;
                        
                        // Update bot status
                        const botToggle = document.getElementById('botToggle');
                        const botStatus = document.getElementById('botStatus');
                        if (config.bot_control.bot_enabled) {
                            botToggle.classList.add('active');
                            botStatus.textContent = 'Enabled';
                        } else {
                            botToggle.classList.remove('active');
                            botStatus.textContent = 'Disabled';
                        }
                    }
                } catch (error) {
                    console.error('Failed to load config:', error);
                }
            }
            
            async function toggleBot() {
                const botToggle = document.getElementById('botToggle');
                const isEnabled = !botToggle.classList.contains('active');
                
                try {
                    const response = await fetch('/config/bot-control', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ bot_enabled: isEnabled })
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        if (isEnabled) {
                            botToggle.classList.add('active');
                            document.getElementById('botStatus').textContent = 'Enabled';
                            document.getElementById('systemStatus').textContent = 'System Online';
                            document.getElementById('systemStatus').className = 'status-online';
                        } else {
                            botToggle.classList.remove('active');
                            document.getElementById('botStatus').textContent = 'Disabled';
                            document.getElementById('systemStatus').textContent = 'Bot Disabled';
                            document.getElementById('systemStatus').className = 'status-offline';
                        }
                        alert(data.message);
                    }
                } catch (error) {
                    alert('Error updating bot status');
                }
            }
            
            async function saveMoneyManagement() {
                const config = {
                    trading_symbol: document.getElementById('tradingSymbol').value,
                    leverage: parseInt(document.getElementById('leverage').value),
                    position_size_type: 'percentage',
                    position_size_percentage: parseFloat(document.getElementById('positionSizePercentage').value),
                    position_size_fixed: 100.0,
                    max_position_amount: parseFloat(document.getElementById('maxPositionAmount').value),
                    use_max_position_limit: true
                };
                
                try {
                    const response = await fetch('/config/money-management', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(config)
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        alert('Money management settings saved successfully!');
                    } else {
                        alert('Failed to save settings');
                    }
                } catch (error) {
                    alert('Error saving settings');
                }
            }
            
            async function saveMexcApi() {
                const config = {
                    api_key: document.getElementById('mexcApiKey').value || null,
                    api_secret: document.getElementById('mexcApiSecret').value || null,
                    api_enabled: document.getElementById('mexcApiEnabled').checked
                };
                
                try {
                    const response = await fetch('/config/mexc-api', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(config)
                    });
                    
                    const data = await response.json();
                    if (data.success) {
                        alert('MEXC API configuration saved successfully!');
                        if (config.api_enabled && config.api_key && config.api_secret) {
                            loadAccountInfo();
                        }
                    } else {
                        alert('Failed to save API configuration');
                    }
                } catch (error) {
                    alert('Error saving API configuration');
                }
            }
            
            async function testConnection() {
                try {
                    const response = await fetch('/api/test-mexc-connection');
                    const data = await response.json();
                    
                    if (data.success && data.connection_test.status === 'connected') {
                        alert('MEXC API connection successful!');
                    } else {
                        alert('MEXC API connection failed: ' + (data.connection_test.error || 'Unknown error'));
                    }
                } catch (error) {
                    alert('Error testing connection');
                }
            }
            
            async function loadAccountInfo() {
                try {
                    const response = await fetch('/api/account-info');
                    const data = await response.json();
                    
                    const accountDiv = document.getElementById('accountInfo');
                    
                    if (data.success) {
                        let html = '<h4>Spot Account:</h4>';
                        if (data.spot_account && data.spot_account.balances) {
                            data.spot_account.balances.forEach(balance => {
                                if (balance.total > 0) {
                                    html += `<div class="config-item"><span>${balance.asset}:</span><span>${balance.total.toFixed(8)}</span></div>`;
                                }
                            });
                        } else {
                            html += '<p>No balances found or API not configured</p>';
                        }
                        
                        html += '<h4>Futures Account:</h4>';
                        if (data.futures_account && data.futures_account.total_wallet_balance !== undefined) {
                            html += `<div class="config-item"><span>Wallet Balance:</span><span>${data.futures_account.total_wallet_balance.toFixed(2)} USDT</span></div>`;
                            html += `<div class="config-item"><span>Available:</span><span>${data.futures_account.available_balance.toFixed(2)} USDT</span></div>`;
                        } else {
                            html += '<p>Futures account data not available (limited API support)</p>';
                        }
                        
                        accountDiv.innerHTML = html;
                    } else {
                        accountDiv.innerHTML = '<p style="color: red;">Failed to load account info: ' + data.error + '</p>';
                    }
                } catch (error) {
                    document.getElementById('accountInfo').innerHTML = '<p style="color: red;">Error loading account info</p>';
                }
            }
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)


@app.get("/config/current")
async def get_current_config():
    """Get current configuration"""
    return {
        "success": True,
        "config": {
            "bot_control": {
                "bot_enabled": config.BOT_ENABLED
            },
            "money_management": {
                "trading_symbol": config.TRADING_SYMBOL,
                "leverage": config.LEVERAGE,
                "position_size_type": config.POSITION_SIZE_TYPE,
                "position_size_percentage": config.POSITION_SIZE_PERCENTAGE,
                "position_size_fixed": config.POSITION_SIZE_FIXED,
                "max_position_amount": config.MAX_POSITION_AMOUNT,
                "use_max_position_limit": config.USE_MAX_POSITION_LIMIT
            },
            "mexc_api": {
                "api_key": "***" if config.MEXC_API_KEY else None,
                "api_secret": "***" if config.MEXC_API_SECRET else None,
                "api_enabled": config.MEXC_API_ENABLED
            }
        }
    }


@app.post("/config/bot-control")
async def update_bot_control(bot_config: BotControlConfig):
    """Update bot control"""
    config.BOT_ENABLED = bot_config.bot_enabled
    
    status_message = "Bot enabled" if bot_config.bot_enabled else "Bot disabled"
    
    return {
        "success": True,
        "message": status_message,
        "bot_enabled": bot_config.bot_enabled
    }


@app.post("/config/money-management")
async def update_money_management(mm_config: MoneyManagementConfig):
    """Update money management configuration"""
    config.TRADING_SYMBOL = mm_config.trading_symbol
    config.LEVERAGE = mm_config.leverage
    config.POSITION_SIZE_TYPE = mm_config.position_size_type
    config.POSITION_SIZE_PERCENTAGE = mm_config.position_size_percentage
    config.POSITION_SIZE_FIXED = mm_config.position_size_fixed
    config.MAX_POSITION_AMOUNT = mm_config.max_position_amount
    config.USE_MAX_POSITION_LIMIT = mm_config.use_max_position_limit
    
    return {
        "success": True,
        "message": "Money management configuration updated successfully"
    }


@app.post("/config/mexc-api")
async def update_mexc_api(mexc_config: MEXCAPIConfig):
    """Update MEXC API configuration"""
    if mexc_config.api_key:
        config.MEXC_API_KEY = mexc_config.api_key
    if mexc_config.api_secret:
        config.MEXC_API_SECRET = mexc_config.api_secret
    config.MEXC_API_ENABLED = mexc_config.api_enabled
    
    return {
        "success": True,
        "message": "MEXC API configuration updated successfully"
    }


@app.get("/api/test-mexc-connection")
async def test_mexc_connection():
    """Test MEXC API connection"""
    if not config.MEXC_API_KEY or not config.MEXC_API_SECRET:
        return {
            "success": False,
            "connection_test": {
                "status": "failed",
                "error": "API credentials not configured"
            }
        }
    
    # Simulate connection test
    return {
        "success": True,
        "connection_test": {
            "status": "connected",
            "public_api": True,
            "private_api": True
        }
    }


@app.get("/api/account-info")
async def get_account_info():
    """Get account information"""
    if not config.MEXC_API_ENABLED or not config.MEXC_API_KEY:
        return {
            "success": False,
            "error": "MEXC API not configured or disabled"
        }
    
    # Simulate account info
    return {
        "success": True,
        "spot_account": {
            "can_trade": True,
            "can_withdraw": True,
            "balances": [
                {"asset": "USDT", "free": 1000.0, "locked": 0.0, "total": 1000.0},
                {"asset": "TRU", "free": 500.0, "locked": 0.0, "total": 500.0},
                {"asset": "ETH", "free": 0.5, "locked": 0.0, "total": 0.5}
            ]
        },
        "futures_account": {
            "total_wallet_balance": 1000.0,
            "available_balance": 950.0,
            "total_unrealized_pnl": 25.0,
            "assets": [
                {"asset": "USDT", "wallet_balance": 1000.0, "available_balance": 950.0}
            ]
        }
    }


def main():
    """Main function"""
    print("""
🚀 Enhanced MEXC Trading System Demo
===================================
✅ MEXC API Integration
✅ Money Management Settings  
✅ Configuration Management
✅ Bot Control Toggle
✅ Account Information Display

Features Demonstrated:
- Real-time configuration updates
- MEXC API credential management
- Money management with percentage/fixed amounts
- Bot enable/disable control
- Account balance display
- Connection testing

Access the dashboard at: http://localhost:8000/enhanced-dashboard
    """)
    
    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info")


if __name__ == "__main__":
    main()
