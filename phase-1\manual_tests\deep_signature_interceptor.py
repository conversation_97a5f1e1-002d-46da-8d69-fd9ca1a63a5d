#!/usr/bin/env python3
"""
DEEP SIGNATURE INTERCEPTOR
Hook into the browser at the lowest level to intercept signature generation
"""

import json
import time
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class DeepSignatureInterceptor:
    """Intercept signature generation at the lowest level"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🕵️ DEEP SIGNATURE INTERCEPTOR")
        print("="*35)
        print("🎯 HOOKING INTO SIGNATURE GENERATION")
    
    def setup_deep_hooks(self):
        """Setup deep hooks into browser functions"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            # Inject DEEP hooks
            self.page.evaluate("""
                window.deepInterceptions = [];
                window.interceptCount = 0;
                
                // Hook into ALL crypto operations
                if (window.CryptoJS) {
                    console.log('🔥 CryptoJS found! Hooking all methods...');
                    
                    // Hook MD5
                    if (window.CryptoJS.MD5) {
                        const originalMD5 = window.CryptoJS.MD5;
                        window.CryptoJS.MD5 = function(message) {
                            const result = originalMD5.apply(this, arguments);
                            
                            window.deepInterceptions.push({
                                type: 'MD5',
                                message: message.toString(),
                                result: result.toString(),
                                timestamp: Date.now()
                            });
                            
                            console.log('🔥 MD5:', message.toString().substring(0, 100), '=>', result.toString());
                            
                            return result;
                        };
                    }
                    
                    // Hook SHA256
                    if (window.CryptoJS.SHA256) {
                        const originalSHA256 = window.CryptoJS.SHA256;
                        window.CryptoJS.SHA256 = function(message) {
                            const result = originalSHA256.apply(this, arguments);
                            
                            window.deepInterceptions.push({
                                type: 'SHA256',
                                message: message.toString(),
                                result: result.toString(),
                                timestamp: Date.now()
                            });
                            
                            console.log('🔥 SHA256:', message.toString().substring(0, 100), '=>', result.toString());
                            
                            return result;
                        };
                    }
                    
                    // Hook HMAC-MD5
                    if (window.CryptoJS.HmacMD5) {
                        const originalHmacMD5 = window.CryptoJS.HmacMD5;
                        window.CryptoJS.HmacMD5 = function(message, key) {
                            const result = originalHmacMD5.apply(this, arguments);
                            
                            window.deepInterceptions.push({
                                type: 'HMAC-MD5',
                                message: message.toString(),
                                key: key.toString(),
                                result: result.toString(),
                                timestamp: Date.now()
                            });
                            
                            console.log('🔥 HMAC-MD5:', 'key=' + key.toString().substring(0, 20), 'msg=' + message.toString().substring(0, 50), '=>', result.toString());
                            
                            return result;
                        };
                    }
                    
                    // Hook HMAC-SHA256
                    if (window.CryptoJS.HmacSHA256) {
                        const originalHmacSHA256 = window.CryptoJS.HmacSHA256;
                        window.CryptoJS.HmacSHA256 = function(message, key) {
                            const result = originalHmacSHA256.apply(this, arguments);
                            
                            window.deepInterceptions.push({
                                type: 'HMAC-SHA256',
                                message: message.toString(),
                                key: key.toString(),
                                result: result.toString(),
                                timestamp: Date.now()
                            });
                            
                            console.log('🔥 HMAC-SHA256:', 'key=' + key.toString().substring(0, 20), 'msg=' + message.toString().substring(0, 50), '=>', result.toString());
                            
                            return result;
                        };
                    }
                }
                
                // Hook XMLHttpRequest header setting
                const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
                XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                    if (name.toLowerCase().includes('sign') || name.toLowerCase().includes('nonce')) {
                        window.interceptCount++;
                        
                        window.deepInterceptions.push({
                            type: 'HEADER',
                            headerName: name,
                            headerValue: value,
                            timestamp: Date.now(),
                            interceptId: window.interceptCount
                        });
                        
                        console.log(`🎯 HEADER #${window.interceptCount}: ${name} = ${value}`);
                        
                        if (name.toLowerCase() === 'x-mxc-sign') {
                            console.log('🔥🔥🔥 SIGNATURE HEADER INTERCEPTED! 🔥🔥🔥');
                            alert(`SIGNATURE FOUND: ${value}`);
                        }
                    }
                    
                    return originalSetRequestHeader.apply(this, arguments);
                };
                
                // Hook fetch
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const [url, options] = args;
                    
                    if (url.includes('order/create') || url.includes('order/submit') || url.includes('order/place')) {
                        console.log('🎯 ORDER REQUEST INTERCEPTED');
                        
                        window.deepInterceptions.push({
                            type: 'ORDER_REQUEST',
                            url: url,
                            headers: options.headers,
                            body: options.body,
                            timestamp: Date.now()
                        });
                    }
                    
                    return originalFetch.apply(this, args);
                };
                
                console.log('✅ DEEP HOOKS INSTALLED!');
                console.log('Ready to intercept signature generation...');
            """)
            
            print("✅ Deep hooks setup completed")
            return True
            
        except Exception as e:
            print(f"❌ Setup failed: {e}")
            return False
    
    def monitor_interceptions(self):
        """Monitor for signature generation"""
        
        print("\n🔍 MONITORING FOR SIGNATURE GENERATION")
        print("="*50)
        print()
        print("🎯 PLACE AN ORDER NOW!")
        print("   - Use very low price to avoid fills")
        print("   - Any symbol, any quantity")
        print("   - We'll intercept the signature generation")
        print()
        
        timeout = 300  # 5 minutes
        start_time = time.time()
        last_count = 0
        
        while time.time() - start_time < timeout:
            try:
                # Get interceptions
                interceptions = self.page.evaluate("() => window.deepInterceptions || []")
                
                if len(interceptions) > last_count:
                    new_interceptions = interceptions[last_count:]
                    
                    for interception in new_interceptions:
                        print(f"\n🎯 {interception['type']} INTERCEPTED:")
                        
                        if interception['type'] == 'HEADER':
                            print(f"   {interception['headerName']}: {interception['headerValue']}")
                            
                            if 'sign' in interception['headerName'].lower():
                                print(f"🔥 SIGNATURE FOUND: {interception['headerValue']}")
                                
                                # Look for the crypto operation that generated this
                                self.find_signature_generation(interception['headerValue'], interceptions)
                        
                        elif interception['type'] in ['MD5', 'SHA256', 'HMAC-MD5', 'HMAC-SHA256']:
                            print(f"   Message: {interception['message'][:100]}...")
                            if 'key' in interception:
                                print(f"   Key: {interception['key'][:50]}...")
                            print(f"   Result: {interception['result']}")
                        
                        elif interception['type'] == 'ORDER_REQUEST':
                            print(f"   URL: {interception['url']}")
                            print(f"   Headers: {list(interception['headers'].keys()) if interception['headers'] else 'None'}")
                    
                    last_count = len(interceptions)
                
                # Show progress
                elapsed = int(time.time() - start_time)
                if elapsed % 30 == 0 and elapsed > 0:
                    print(f"⏱️  Monitoring... ({elapsed}s, {len(interceptions)} interceptions)")
                
                time.sleep(1)
                
            except Exception as e:
                print(f"⚠️  Error: {e}")
                time.sleep(1)
        
        print(f"\n⏰ Monitoring complete. Total interceptions: {len(interceptions) if 'interceptions' in locals() else 0}")
        return True
    
    def find_signature_generation(self, signature, all_interceptions):
        """Find which crypto operation generated the signature"""
        
        print(f"\n🔍 SEARCHING FOR SIGNATURE GENERATION")
        print(f"Target: {signature}")
        
        for interception in all_interceptions:
            if interception.get('result') == signature:
                print(f"🎉 FOUND IT!")
                print(f"   Algorithm: {interception['type']}")
                print(f"   Message: {interception['message']}")
                if 'key' in interception:
                    print(f"   Key: {interception['key']}")
                
                # This is the signature algorithm!
                self.crack_signature_algorithm(interception)
                return True
        
        print(f"❌ Signature generation not found in {len(all_interceptions)} interceptions")
        return False
    
    def crack_signature_algorithm(self, signature_interception):
        """We found the signature generation! Crack it!"""
        
        print(f"\n🎉 SIGNATURE ALGORITHM CRACKED!")
        print("="*40)
        print(f"Algorithm: {signature_interception['type']}")
        print(f"Message: {signature_interception['message']}")
        if 'key' in signature_interception:
            print(f"Key: {signature_interception['key']}")
        print(f"Result: {signature_interception['result']}")
        
        # Now we can implement this!
        print(f"\n🚀 IMPLEMENTATION:")
        if signature_interception['type'] == 'MD5':
            print(f"   signature = hashlib.md5('{signature_interception['message']}'.encode()).hexdigest()")
        elif signature_interception['type'] == 'SHA256':
            print(f"   signature = hashlib.sha256('{signature_interception['message']}'.encode()).hexdigest()[:32]")
        elif signature_interception['type'] == 'HMAC-MD5':
            print(f"   signature = hmac.new('{signature_interception['key']}'.encode(), '{signature_interception['message']}'.encode(), hashlib.md5).hexdigest()")
        elif signature_interception['type'] == 'HMAC-SHA256':
            print(f"   signature = hmac.new('{signature_interception['key']}'.encode(), '{signature_interception['message']}'.encode(), hashlib.sha256).hexdigest()[:32]")
        
        return True
    
    def run_deep_interception(self):
        """Run the complete deep interception"""
        
        print("="*60)
        print("🕵️ DEEP SIGNATURE INTERCEPTION")
        print("="*60)
        
        # Setup hooks
        if not self.setup_deep_hooks():
            return False
        
        try:
            # Monitor
            self.monitor_interceptions()
            print("\n🎉 DEEP INTERCEPTION COMPLETE!")
            return True
            
        finally:
            # Cleanup
            if hasattr(self, 'browser'):
                self.browser.close()
            if hasattr(self, 'playwright'):
                self.playwright.stop()

def main():
    """Main function"""
    
    interceptor = DeepSignatureInterceptor()
    interceptor.run_deep_interception()

if __name__ == '__main__':
    main()
