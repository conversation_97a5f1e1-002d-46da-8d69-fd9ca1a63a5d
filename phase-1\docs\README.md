# WebTrader Path B (UI Automation via Edge CDP)

This repo demonstrates placing and canceling MEXC futures orders through the live browser session (Edge) by automating the actual web UI. This path bypasses API restrictions and WAF checks by letting the site generate required tokens and headers.

## TL;DR
- We successfully sniffed and recorded the exact order placement and cancel flows from your live Edge session
- Raw programmatic submit via XHR returned 401/602 due to front‑end artifacts (x-mxc-sign, x-mxc-nonce, mtoken, p0, k0) and robot checks
- The reliable solution is to drive the UI (click the real buttons) through Edge CDP; the site issues all artifacts and accepts the order
- We already built two helpers:
  - webtrader_edge_cdp_sniffer.py — attaches to your Edge and logs submit/cancel requests
  - webtrader_edge_cdp_place_cancel.py — demonstrated programmatic place (failed due to 401 when not clicking UI); we will replace with a UI‑click version

## What works today
- Attaching to an already‑logged‑in Edge with remote debugging (CDP)
- Sniffing and logging the real network requests for place + cancel on TRU_USDT
- Confirmed submit endpoint and request shape: POST /api/v1/private/order/create?mhash=... with x-mxc-sign/x-mxc-nonce/mtoken and p0/k0 fields
- Confirmed cancel endpoint and request shape: POST /api/v1/private/order/cancel with body ["orderId"]

## Why raw XHR failed
- Server requires front‑end generated artifacts:
  - Headers: x-mxc-sign, x-mxc-nonce, mtoken
  - Body fields: p0, k0
  - Robot/captcha checks to obfuscated endpoints
- These are minted client‑side just before/around the actual submit/cancel click. Attempting to POST without them yields 401/602.

## Path B solution
- Drive the UI with Playwright attached to Edge CDP
- Fill the limit order form (Isolated, leverage, Post‑Only, price, volume)
- Click Buy/Sell — MEXC generates all artifacts transparently
- Observe the orderId in the UI and cancel via the UI

## Next deliverables
- A UI‑click helper script: webtrader_edge_cdp_place_cancel_ui.py
  - Place a safe Post‑Only order far from market and cancel it
  - Return JSON: { symbol, orderId, placedAt, canceledAt, status }
- A CLI wrapper and runbook
- Optionally, nightly check that the session is alive; if not, open a page to re‑collect 2FA and reactivate

## Prerequisites
- Windows, Edge installed
- Start Edge with CDP:
  "C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe" --remote-debugging-port=9222 --user-data-dir="C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\Edge\\User Data"
- Log into MEXC in that Edge window; open Futures page
- Python venv with Playwright installed (already set up in this repo)

## Run sniffer (capture your manual place/cancel)
- python webtrader_edge_cdp_sniffer.py
- In Edge: place a tiny Post‑Only order far from market; then cancel it
- Observe the terminal: it prints order/create and order/cancel requests and responses

## Run automated place+cancel (UI) — to be added
- python webtrader_edge_cdp_place_cancel_ui.py --symbol TRU_USDT --side long --price 0.02 --vol 1 --post-only
- It will:
  - Attach to Edge, navigate to the Futures page
  - Populate the order form and click submit
  - Read back the orderId; click Cancel in Open Orders
  - Print final JSON result

## Risks and mitigations
- Anti‑bot changes: Sniffer and UI click mirror the first‑party flow; if the site changes, we adapt selectors and timings
- Session expiry: script detects not‑logged‑in and prompts re‑login in Edge; later, we’ll add a minimal 2FA collection page to refresh
- Price fill risk: we always use Post‑Only with price far from market; script verifies state and cancels quickly

## What to demo to the employer
- Show the sniffer capturing a TRU_USDT order and cancel in real time
- Run the automated UI script: places and cancels order, returns JSON with orderId
- Explain why API‑only is blocked and how UI automation is robust and compliant with platform rules

See docs/captured-endpoints.md and docs/path-b-implementation.md for details.

