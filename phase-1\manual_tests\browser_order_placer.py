#!/usr/bin/env python3
"""
BROWSER ORDER PLACER
Use the existing browser session to place orders and capture signatures in real-time
"""

import json
import time
import hashlib
import hmac
from playwright.sync_api import sync_playwright
from dotenv import dotenv_values

class BrowserOrderPlacer:
    """Place orders through browser and capture signatures in real-time"""
    
    def __init__(self):
        vals = dotenv_values('.env')
        self.auth = vals.get('MEXC_WEB_AUTH')
        
        print("🚀 BROWSER ORDER PLACER")
        print("="*30)
        print("🎯 AUTOMATED ORDER PLACEMENT WITH SIGNATURE CAPTURE")
    
    def setup_browser_session(self):
        """Setup browser session"""
        
        try:
            self.playwright = sync_playwright().start()
            self.browser = self.playwright.chromium.connect_over_cdp('http://127.0.0.1:9222')
            
            if not self.browser.contexts:
                return False
            
            self.context = self.browser.contexts[0]
            
            # Find MEXC page
            mexc_page = None
            for page in self.context.pages:
                if 'mexc.com' in (page.url or ''):
                    mexc_page = page
                    break
            
            if not mexc_page:
                mexc_page = self.context.new_page()
                mexc_page.goto('https://futures.mexc.com/exchange/BTC_USDT', wait_until='domcontentloaded')
            
            self.page = mexc_page
            
            print("✅ Browser session connected")
            return True
            
        except Exception as e:
            print(f"❌ Browser setup failed: {e}")
            return False
    
    def inject_signature_interceptor(self):
        """Inject signature interceptor that captures signatures in real-time"""
        
        print("\n🔐 INJECTING SIGNATURE INTERCEPTOR")
        print("="*40)
        
        self.page.evaluate("""
            window.capturedSignatures = [];
            window.signatureAlgorithm = null;
            
            console.log('🔐 Installing signature interceptor...');
            
            // Hook XMLHttpRequest to capture signatures
            const originalSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;
            XMLHttpRequest.prototype.setRequestHeader = function(name, value) {
                if (name.toLowerCase() === 'x-mxc-sign') {
                    console.log('🎉 SIGNATURE INTERCEPTED:', value);
                    
                    const nonce = this._mexc_nonce || 'unknown';
                    
                    window.capturedSignatures.push({
                        signature: value,
                        nonce: nonce,
                        timestamp: Date.now(),
                        url: this._mexc_url || 'unknown'
                    });
                    
                    // Try to reverse engineer the signature immediately
                    window.tryReverseEngineer(value, nonce);
                }
                
                if (name.toLowerCase() === 'x-mxc-nonce') {
                    this._mexc_nonce = value;
                }
                
                return originalSetRequestHeader.apply(this, arguments);
            };
            
            const originalXHROpen = XMLHttpRequest.prototype.open;
            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._mexc_url = url;
                return originalXHROpen.apply(this, arguments);
            };
            
            // Function to try reverse engineering signatures
            window.tryReverseEngineer = function(signature, nonce) {
                console.log('🔍 Attempting to reverse engineer signature...');
                
                const auth = 'WEBd98cd3a6eecf378454e859e5b4b680aeae16c4bd38492179d5a4f4afa81f43b6';
                
                // Test various patterns
                const patterns = [
                    auth + nonce,
                    nonce + auth,
                    auth.substring(3) + nonce,
                    nonce + auth.substring(3),
                    auth.substring(3, 67) + nonce,
                    nonce + auth.substring(3, 67),
                    auth + nonce + 'POST',
                    'POST' + auth + nonce,
                    auth.substring(3) + nonce + 'POST',
                    auth + nonce + '/api/v1/private/order/create',
                    auth.substring(3) + nonce + 'order/create'
                ];
                
                for (const pattern of patterns) {
                    // Test MD5
                    const testSig = CryptoJS.MD5(pattern).toString();
                    if (testSig === signature) {
                        console.log('🎉🎉🎉 SIGNATURE ALGORITHM FOUND! 🎉🎉🎉');
                        console.log('Pattern:', pattern);
                        console.log('Algorithm: MD5');
                        
                        window.signatureAlgorithm = {
                            type: 'MD5',
                            pattern: pattern,
                            auth: auth,
                            verified: true
                        };
                        
                        alert(`SIGNATURE CRACKED!\\n\\nAlgorithm: MD5(${pattern})\\n\\nSignature: ${signature}`);
                        return true;
                    }
                    
                    // Test SHA256 (first 32 chars)
                    const testSigSHA = CryptoJS.SHA256(pattern).toString().substring(0, 32);
                    if (testSigSHA === signature) {
                        console.log('🎉🎉🎉 SIGNATURE ALGORITHM FOUND! 🎉🎉🎉');
                        console.log('Pattern:', pattern);
                        console.log('Algorithm: SHA256[:32]');
                        
                        window.signatureAlgorithm = {
                            type: 'SHA256_32',
                            pattern: pattern,
                            auth: auth,
                            verified: true
                        };
                        
                        alert(`SIGNATURE CRACKED!\\n\\nAlgorithm: SHA256(${pattern})[:32]\\n\\nSignature: ${signature}`);
                        return true;
                    }
                }
                
                console.log('❌ Could not reverse engineer signature');
                return false;
            };
            
            // Function to generate signature using discovered algorithm
            window.generateSignature = function(nonce) {
                if (!window.signatureAlgorithm) {
                    console.log('❌ No signature algorithm available');
                    return null;
                }
                
                const algo = window.signatureAlgorithm;
                const pattern = algo.pattern.replace(algo.auth + algo.nonce, algo.auth + nonce);
                
                if (algo.type === 'MD5') {
                    return CryptoJS.MD5(pattern).toString();
                } else if (algo.type === 'SHA256_32') {
                    return CryptoJS.SHA256(pattern).toString().substring(0, 32);
                }
                
                return null;
            };
            
            console.log('✅ Signature interceptor installed!');
        """)
        
        print("✅ Signature interceptor injected")
    
    def place_automated_order(self, symbol="BTC_USDT", side="buy", price=1000.0, quantity=1):
        """Place an automated order through the browser interface"""
        
        print(f"\n🚀 PLACING AUTOMATED ORDER")
        print("="*35)
        print(f"Symbol: {symbol}")
        print(f"Side: {side}")
        print(f"Price: ${price}")
        print(f"Quantity: {quantity}")
        
        try:
            # Navigate to the trading page
            self.page.goto(f'https://futures.mexc.com/exchange/{symbol}', wait_until='domcontentloaded')
            
            # Wait for page to load
            time.sleep(3)
            
            # Fill in order form
            print("📝 Filling order form...")
            
            # Set order type to limit
            try:
                self.page.click('text=Limit', timeout=5000)
            except:
                print("⚠️ Could not set limit order type")
            
            # Set side (buy/sell)
            if side.lower() == "buy":
                try:
                    self.page.click('[data-testid="buy-button"], .buy-button, text=Buy', timeout=5000)
                except:
                    print("⚠️ Could not click buy button")
            else:
                try:
                    self.page.click('[data-testid="sell-button"], .sell-button, text=Sell', timeout=5000)
                except:
                    print("⚠️ Could not click sell button")
            
            # Fill price
            try:
                price_input = self.page.locator('input[placeholder*="Price"], input[data-testid*="price"]').first
                price_input.clear()
                price_input.fill(str(price))
                print(f"✅ Set price: {price}")
            except Exception as e:
                print(f"⚠️ Could not set price: {e}")
            
            # Fill quantity
            try:
                quantity_input = self.page.locator('input[placeholder*="Quantity"], input[placeholder*="Amount"], input[data-testid*="quantity"]').first
                quantity_input.clear()
                quantity_input.fill(str(quantity))
                print(f"✅ Set quantity: {quantity}")
            except Exception as e:
                print(f"⚠️ Could not set quantity: {e}")
            
            # Click submit button
            print("🚀 Submitting order...")
            
            submit_selectors = [
                'button:has-text("Buy")',
                'button:has-text("Sell")',
                'button[data-testid*="submit"]',
                'button[data-testid*="place"]',
                '.submit-button',
                '.place-order-button'
            ]
            
            submitted = False
            for selector in submit_selectors:
                try:
                    self.page.click(selector, timeout=2000)
                    print(f"✅ Clicked submit button: {selector}")
                    submitted = True
                    break
                except:
                    continue
            
            if not submitted:
                print("❌ Could not find submit button")
                return False
            
            # Wait for signature capture
            print("⏳ Waiting for signature capture...")
            time.sleep(5)
            
            # Check if signature was captured
            captured_data = self.page.evaluate("() => ({ signatures: window.capturedSignatures || [], algorithm: window.signatureAlgorithm })")
            
            if captured_data['signatures']:
                print(f"🎉 CAPTURED {len(captured_data['signatures'])} SIGNATURES!")
                
                for i, sig_data in enumerate(captured_data['signatures']):
                    print(f"   Signature #{i+1}: {sig_data['signature']}")
                    print(f"   Nonce: {sig_data['nonce']}")
                
                if captured_data['algorithm']:
                    print(f"🎉🎉🎉 SIGNATURE ALGORITHM DISCOVERED! 🎉🎉🎉")
                    print(f"   Type: {captured_data['algorithm']['type']}")
                    print(f"   Pattern: {captured_data['algorithm']['pattern']}")
                    return True
            
            return len(captured_data['signatures']) > 0
            
        except Exception as e:
            print(f"❌ Order placement failed: {e}")
            return False
    
    def test_multiple_orders(self):
        """Test multiple orders to capture more signatures"""
        
        print(f"\n🔄 TESTING MULTIPLE ORDERS")
        print("="*35)
        
        test_orders = [
            {"symbol": "BTC_USDT", "side": "buy", "price": 1000.0, "quantity": 1},
            {"symbol": "BTC_USDT", "side": "sell", "price": 100000.0, "quantity": 1},
            {"symbol": "ETH_USDT", "side": "buy", "price": 100.0, "quantity": 1},
        ]
        
        total_signatures = 0
        
        for i, order in enumerate(test_orders):
            print(f"\n📋 Test Order #{i+1}")
            
            if self.place_automated_order(**order):
                # Get captured signatures
                captured_data = self.page.evaluate("() => window.capturedSignatures || []")
                total_signatures += len(captured_data)
                
                if total_signatures > 0:
                    print(f"✅ Total signatures captured: {total_signatures}")
                    
                    # Check if algorithm was discovered
                    algorithm = self.page.evaluate("() => window.signatureAlgorithm")
                    if algorithm and algorithm.get('verified'):
                        print(f"🎉 ALGORITHM VERIFIED!")
                        return True
            
            # Wait between orders
            time.sleep(2)
        
        return total_signatures > 0
    
    def implement_discovered_algorithm(self):
        """Implement the discovered signature algorithm"""
        
        print(f"\n🔐 IMPLEMENTING DISCOVERED ALGORITHM")
        print("="*45)
        
        # Get the discovered algorithm
        algorithm = self.page.evaluate("() => window.signatureAlgorithm")
        
        if not algorithm:
            print("❌ No algorithm discovered")
            return None
        
        print(f"✅ Algorithm found:")
        print(f"   Type: {algorithm['type']}")
        print(f"   Pattern: {algorithm['pattern']}")
        
        # Create signature function
        def signature_function(nonce):
            auth = algorithm['auth']
            pattern = algorithm['pattern']
            
            # Replace nonce in pattern
            if 'nonce' in pattern:
                final_pattern = pattern.replace('nonce', str(nonce))
            else:
                # Try to replace the nonce value
                import re
                final_pattern = re.sub(r'\d{13}', str(nonce), pattern)
            
            if algorithm['type'] == 'MD5':
                return hashlib.md5(final_pattern.encode()).hexdigest()
            elif algorithm['type'] == 'SHA256_32':
                return hashlib.sha256(final_pattern.encode()).hexdigest()[:32]
            
            return None
        
        return signature_function
    
    def run_browser_order_placement(self):
        """Run the complete browser order placement"""
        
        print("="*60)
        print("🚀 BROWSER ORDER PLACEMENT AND SIGNATURE DISCOVERY")
        print("="*60)
        
        # Setup browser
        if not self.setup_browser_session():
            return False
        
        try:
            # Inject signature interceptor
            self.inject_signature_interceptor()
            
            # Test multiple orders
            if self.test_multiple_orders():
                print(f"\n🎉 SIGNATURES CAPTURED SUCCESSFULLY!")
                
                # Implement discovered algorithm
                signature_func = self.implement_discovered_algorithm()
                
                if signature_func:
                    print(f"✅ Signature algorithm implemented!")
                    print(f"🚀 Ready for automated trading!")
                    return True
                else:
                    print(f"⚠️ Algorithm captured but implementation failed")
                    return True  # Still success - we have the data
            else:
                print(f"❌ No signatures captured")
                return False
            
        finally:
            # Cleanup
            try:
                if hasattr(self, 'browser'):
                    self.browser.close()
                if hasattr(self, 'playwright'):
                    self.playwright.stop()
            except:
                pass

def main():
    """Main function"""
    
    placer = BrowserOrderPlacer()
    if placer.run_browser_order_placement():
        print("\n🎉 BROWSER ORDER PLACEMENT SUCCESSFUL!")
        print("🔐 SIGNATURE ALGORITHM DISCOVERED!")
    else:
        print("\n🔍 Browser order placement completed - check results")

if __name__ == '__main__':
    main()
